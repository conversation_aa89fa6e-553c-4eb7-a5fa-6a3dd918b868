<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
    $quiz = get_json('/api/quiz/url/'.$_GET['url']);
    $quiz_data = json_decode($quiz,true);
    $quiz_id = $quiz_data['quiz'][0]['id'];
    $url = 'https://admin.franchiseventures.com/quiz-preview/';
    $subdomain_check = explode('.',$_SERVER['HTTP_HOST']);
    if (!in_array($subdomain_check[0], ['myaccount','beta','www','old'])){
        $url = 'https://'.$subdomain_check[0].'.admin.franchiseventures.com/quiz-preview/'; 
    }
    $url .= $quiz_id.'?site_id=60&modal_include=1';
    if(isset($_GET['preview'])){
        $url .= '&preview='.$_GET['preview'];
    }

    //open connection
    $ch = curl_init();

    //set the url, number of POST vars, POST data
    curl_setopt($ch,CURLOPT_URL, $url);
    curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: text/html"));
    curl_setopt($ch,CURLOPT_POST, true);
    curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);

    //So that curl_exec returns the contents of the cURL; rather than echoing it
    curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

    //execute post
    $return = curl_exec($ch);

    echo $return;
    die;