<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $limitVal = '';
    $limit = @$_GET['limit'];
    $master_types_id = @$_GET['master_types_id'];
    if ($limit) {
        $limitVal = '&limit='.$limit;
    }
    if (isset($_GET['article_type']) && $_GET['article_type']) {
        $limitVal .= '&article_type='.$_GET['article_type'].'&is_random=1';
    }else{
        $limitVal .= '&article_type=303';
    }
    if ($master_types_id) {
        $limitVal .= '&master_types_id='.$master_types_id;
    }
    $temp_array = get_json('/api/myaccount_articles/list?active=1'.$limitVal);
    $temp_array = json_decode($temp_array,true);
    $temp_array = $temp_array['data'];

    $articles = '';

    if ($temp_array && count($temp_array) > 0) {
        foreach ($temp_array as $key => $value) {
            $description = trim($value['body_content']);
            $description = strip_tags($description);
            $description = $shorts[0] = str_replace(array("\r", "\n"), '', $description);
            if(strlen($description ?? '') > 260) {
                $shorts = explode( "\n", wordwrap($description, 260));
            }
            $shorts[0] .= '&hellip;';
            $img_url = $value['og_image'] ? $value['og_image'] : '/images/solid.png';
            $data_name_url = urlencode($value['title']);
            $temp = '<div class="articles_banner" style="--bg-image: url(\''.$img_url.'\');"></div>
                            <strong class="articles_title">'.$value['title'].'</strong>
                            <p class="articles_details">'.$shorts[0].'</p>
                            <div class="articles_read_more"><a href="javascript:;" data-name="'.$data_name_url.'" onclick="fetchArticleModal(this,'.$value['id'].');" class="button">Read More</a></div>';
                            $articles .= '<div class="item"><a class="article_anchor" href="javascript:;" data-name="'.$data_name_url.'" onclick="fetchArticleModal(this,'.$value['id'].');">'.$temp.'</a></div>';
        }
    }

    $finalData = '<div class="articles">'.$articles.'</div>';
    echo json_encode($finalData);

?>