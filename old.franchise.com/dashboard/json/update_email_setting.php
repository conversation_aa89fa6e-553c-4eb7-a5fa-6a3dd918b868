<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    if(isset($_GET['email_unsubscribe'])){
        $fields['email_unsubscribe'] = $_GET['email_unsubscribe'];

        $url = $api_url.'/api/myaccount/update_email_setting/'.$_SESSION['myac_login_contact_id'];

        $fields_string = json_encode($fields);

        //open connection
        $ch = curl_init();

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);

        //So that curl_exec returns the contents of the cURL; rather than echoing it
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

        //execute post
        $result = curl_exec($ch);
        $returned = json_decode($result,true);
        if ($returned['status'] == 'success') {
            $data = $returned['data'];
            $_SESSION['myac_email_unsubscribe'] = $data['email_unsubscribe'];
        }
    }else{
        $returned = ['status'=>'failure'];
    }
    echo json_encode($returned);
?>