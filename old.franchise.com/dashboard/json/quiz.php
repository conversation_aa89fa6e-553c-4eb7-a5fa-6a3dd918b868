<?php
    include_once($_SERVER['DOCUMENT_ROOT'].$dashboard.'/includes/global.php');
    $quiz_id = $_GET['id'];
    $url = 'https://admin.franchiseventures.com/quiz-preview/';
    $subdomain_check = explode('.',$_SERVER['HTTP_HOST']);
    if (!in_array($subdomain_check[0], ['www','old'])){
        $url = 'https://'.$subdomain_check[0].'.admin.franchiseventures.com/quiz-preview/'; 
    }
    $url .= $quiz_id.'?site_id=7&quiz_only=true';
    if(isset($_GET['modal_include'])){
        $url .= '&modal_include='.$_GET['modal_include'];
    }
    if(isset($_GET['preview'])){
        $url .= '&preview='.$_GET['preview'];
    }
    if(isset($geo['state'])){
        $url .= '&state='.$geo['state'];
    }
    // Set custom headers including User-Agent
$options = [
    "http" => [
        "header" => "User-Agent: FVAgent/1.0\r\n"
    ]
];
$context = stream_context_create($options);
$return = file_get_contents($url, false, $context) or die("Error: Cannot create object");
    echo $return;
    die;
?>