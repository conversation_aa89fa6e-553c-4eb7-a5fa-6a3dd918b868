<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    if(isset($_GET['email'])){
        $sanitized_a = filter_var($_GET['email'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        if (filter_var($sanitized_a, FILTER_VALIDATE_EMAIL)) {
            $fields['email'] = strip_tags($_GET['email']);
            $fields['password'] = strip_tags($_GET['password']);
            $fields['site_id'] = 60;
            if(isset($_GET['login_type'])){
                $fields['login_type'] = $_GET['login_type'];
            }
    
            $url = $api_url.'/api/oneview/login';
    
            $fields_string = json_encode($fields);
    
            //open connection
            $ch = curl_init();
    
            //set the url, number of POST vars, POST data
            curl_setopt($ch,CURLOPT_URL, $url);
            curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch,CURLOPT_POST, true);
            curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);
    
            //So that curl_exec returns the contents of the cURL; rather than echoing it
            curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 
    
            //execute post
            $result = curl_exec($ch);
            $returned = json_decode($result,true);

            if ($returned['status'] == 'success') {
                $data = $returned['data'];
                $_SESSION['myac_email'] = $_GET['email'];
                $_SESSION['myac_first_name'] = $data['first_name'];
                $_SESSION['myac_last_name'] = $data['last_name'];
                $_SESSION['myac_login_contact_id'] = $data['id'];
                $_SESSION['myac_state_code'] = $data['state_code'];
                $_SESSION['myac_phone'] = $data['phone']; 
                $_SESSION['myac_zip_code'] = $data['zip_code'] ? substr("00000{$data['zip_code']}", -5) : '';
                $_SESSION['myac_email_unsubscribe'] = @$data['email_unsubscribe'];
                $_SESSION['myac_utm_campaign'] = @$data['utm_campaign'];
                $_SESSION['myac_utm_medium'] = @$data['utm_medium'];
                $_SESSION['myac_utm_source'] = @$data['utm_source'];
                $_SESSION['myac_utm_type'] = @$data['utm_type'];
                $_SESSION['myac_gclid_mlclkid'] = @$data['gclid_mlclkid'];
            }
           
        }else{
            $returned = ['status'=>'failure'];
        }
    }
    echo json_encode($returned);
?>