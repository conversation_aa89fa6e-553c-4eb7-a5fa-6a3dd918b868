<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    if (isset($_GET['notification_id'])) {
        $temp_data = get_json('/api/my_account_notification/get_message/'.$_GET['notification_id']);
        $temp_data = json_decode($temp_data,true);
        $temp_data = $temp_data['data'];

        // $notification = $temp_data['message_content'];
        $message_content = nl2br($temp_data['message_content']);
        $message_title = $temp_data['message_title'];
        $create_date = $temp_data['create_date'];
        $initials = getInitials($temp_data['display_name']);
        $notification = '
        <div class="message"><div id="notifications" class="popped"><ul style="margin: 0 !important;"><li>
            <div class="notification_image"><div class="avatar '.$temp_data['type_name'].'">'.$initials.'</div></div>
            <div class="notification_text">
                <h4><b>'.$message_title.'</b></h4>
                <h6>'.date("M j, Y",strtotime($create_date)).'</h6>
            </div>
        </li></ul></div>
        <hr>
        <p>'.$message_content.'</p>
        </div>
        <div class="row">
            <div class="row_item buttons">
                <button class="button cancel" onclick="closeNotificationPopup();">Close</button>
                <button class="button solid delete" onclick="deleteNotificationAlert('.$_GET['notification_id'].');">Delete</button>
            </div>
        </div>';

        // Update for read message
        $temp_data_update = get_json('/api/my_account_notification/update_notification/'.$_GET['notification_id']);

        echo json_encode($notification);
    } 


?>