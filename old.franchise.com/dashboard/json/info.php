<?php
    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
    $return = [
        'i' => $_SESSION['myac_login_contact_id'] ?? null,
        'f' => $_SESSION['myac_first_name'] ?? null,
        'l' => $_SESSION['myac_last_name'] ?? null,
        'e' => $_SESSION['myac_email'] ?? null,
        'p' => $_SESSION['myac_phone'] ?? null,
        'z' => null
    ];
    if(isset($_SESSION['myac_zip_code'])){
        if($_SESSION['myac_zip_code'] && $_SESSION['myac_zip_code'] != 0){
            $return['z'] = substr("00000{$_SESSION['myac_zip_code']}", -5);
        }
    }
    header('Content-Type: application/json');
    echo json_encode($return);