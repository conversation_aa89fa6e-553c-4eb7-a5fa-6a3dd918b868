<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    // Variables
    $mapped = [
        296 => 'Favorites',
        297 => 'Recent Searches',
        298 => 'Recent Requests',
        315 => 'Article Favorites',
        316 => 'Requested',
        328 => 'Comparison',
        329 => 'Comparison',
        '296,315' => 'All Favorites'
    ];
    $message = 'Available '.$mapped[$_GET['master_type']];
    $return = ['status' => 'success','message'=>$message,'data'=>null];
    $login_contact_id = (isset($_SESSION['myac_login_contact_id']) && $_SESSION['myac_login_contact_id']) ? $_SESSION['myac_login_contact_id'] : @$_GET['login_contact_id'];
    $parameters = [
        'login_contact_id' => @$login_contact_id,
        'master_types_id' => $_GET['master_type'],
        'value' => @$_REQUEST['v']
    ]; 
    // Get Activities by master_types_id and login_contact_id
    $url = '/api/my_account_activity/index?master_types_id='.$_GET['master_type'].'&login_contact_id='.$login_contact_id;
    $database = json_decode(get_json($url),true);
    if($_GET['master_type'] == '296,315'){
        $values = ['count'=>0,296 => [],315=>[]];
        foreach($database['data'] as $key => $value){
            if($value['master_types_id'] == 296){
                $values['count']++;
            }
            $values[$value['master_types_id']][] = $value['value'];
        }
    }else if($_GET['master_type'] == '316'){
        $values = [];
        foreach($database['data'] as $key => $value){
            $values[$key]['date'] = $value['create_date'];
            $values[$key]['fboid'] = $value['value'];
        }
    }else if($_GET['master_type'] == '297' && isset($_GET['search'])){
        $values = array_column($database['data'], 'value','last_update');
    }else{
        $values = array_column($database['data'], 'value');
    }
    if(strlen($_REQUEST['v'] ?? '') && strlen($_REQUEST['t'] ?? '')){
        // debug($_REQUEST);
        $new_url = $api_url.'/api/my_account_activity/';
        $in_array = (in_array($_REQUEST['v'],$values)) ? true : false;
        if($_REQUEST['t'] == 'add'){
            if($in_array){
                $return['message'] = 'Already Exists'; 
            }else{
                $query = httpPost($new_url.'create',$parameters);
                if($query['status'] == 'success'){
                    $return['message'] = 'Added to '.$mapped[$_GET['master_type']];
                    $return['data'] = $query['data'];
                }
            }
        }else if($_REQUEST['t'] == 'remove'){
            if(!$in_array){
                $return['message'] = 'Does Not Exists'; 
            }else{
                $temp_id = null;
                foreach($database['data'] as $key => $value){
                    if($value['value'] == $_REQUEST['v']){
                        $temp_id = $value['id'];
                        break; 
                    }
                }
                $query = httpPost($new_url.'destroy/'.$temp_id,$parameters,'DELETE');
                if($query['status'] == 'success'){
                    $return['message'] = 'Removed from '.$mapped[$_GET['master_type']];
                    $return['data'] = $query['data'];
                }
            }
        }
    }else{
        $return = $values;
    }
    header('Content-Type: application/json');
    header('User-Agent: FVAgent/1.0');
    echo json_encode($return);
?>