<?php

// Global Includes
include_once($_SERVER['DOCUMENT_ROOT'].'/includes/global.php');

//CSV DOWNLOAD HEADER
function download_send_headers($filename) {
    $filename = str_replace('.csv','.xlsx',$filename);
    // disable caching
    $now = gmdate("D, d M Y H:i:s");
    header('Expires: '.gmdate('D, d M Y H:i:s \G\M\T', time() + 3600));
    header("Cache-Control: max-age=0, no-cache, must-revalidate, proxy-revalidate");
    header("Last-Modified: {$now} GMT");

    // force download  
    header("Content-Type: application/force-download");
    header("Content-Type: application/octet-stream");
    header("Content-Type: application/download");

    // disposition / encoding on response body
    header("Content-Disposition: attachment;filename={$filename}");
    header("Content-Transfer-Encoding: binary");
}

//ARRAY TO CSV
function array2csv(array &$array,$options=[]) {
    global $prefix;
    $live_api_url = $prefix.'.com/webservices/csv/array_to_csv.php';
    
    // Convert the array to JSON format
    $jsonData = json_encode($array);

    // Initialize cURL session
    $ch = curl_init($live_api_url);

    // Set cURL options
    curl_setopt($ch, CURLOPT_POST, 1); // Use POST method
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData); // Attach JSON payload
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return response as a string
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json", // Set content type to JSON
        "Content-Length: " . strlen($jsonData) // Set content length
    ]);

    // Execute the request and get response
    $response = curl_exec($ch);

    // Check for errors
    if (curl_errno($ch)) {
        echo "cURL error: " . curl_error($ch);
    } else {
        echo $response;
    }

    // Close cURL session
    curl_close($ch);
}

$return = [];
$prefix = 'https://www.franchiseportals';
$subdomain_check = explode('.',$_SERVER['HTTP_HOST']);
if (!in_array($subdomain_check[0], ['www','old'])){
    $prefix = 'https://'.$subdomain_check[0].'.franchiseportals';   
}
$profile_results = get_json('/60/fv-searchresults.php?paid=0&min=0&max=500000&units_min=0&units_max=8000&filter=directory',false);
$results = json_decode($profile_results,true);
$results = $results['data']['data'];
$conceptIds = array_column($results, 'concepts_id');
$start = (isset($_GET['start'])) ? $_GET['start'] : 0;
$end = (isset($_GET['end'])) ? $_GET['end'] : 10;
foreach($conceptIds as $masterkey => $concept_id){
    if($masterkey >= $end){
        break;
    }else if($masterkey < $start){
        continue;
    }
    $description = $logo = $profile_results = '';
    $json = $media = $finance = $total_investment = $temp_array = $details = $address = $data = array();
    $logo = null;
    $profile_results = '/api/get_profile/60/'.$concept_id;
    $profileServices = get_json($profile_results);
    $profileServices = json_decode($profileServices,true);
    $loops = [
        'data' => ['name','fbo_id','category_name','investment'],
        'brochure' => ['intro_description','long_description','home_based','year_founded','franchising_since','franchise_units','military_discount','military_veteran_details','brochure_url','financing_available','is_training_available','is_mobile_franchise','total_investment_min','total_investment_max','min_net_worth','franchise_fee','royalty_fee'],
        'address' => ['city','state']
    ]; 
    $data = @$profileServices['data'][0];

    if (isset($data['brochure'][0]['images']) && is_array($data['brochure'][0]['images'])) {
        foreach ($data['brochure'][0]['images'] as $key => $value) {
            if($value['type'] == 'background'){
                $logo = $value['image_url']; 
                break;
            }
        }
    }
    if(is_null($logo)){
        $logo = 'https://franchise-ventures-general.s3.amazonaws.com/global/images/checkered.png'; 
    }
    
    if (isset($_GET['get_image'])) {
        $json['image_url'] = $logo;
        $json['name'] = @$data['name'];
        $json['fbo_id'] = @$data['fbo_id'];
        $json['id'] = @$concept_id;
    }else{
        if (isset($data['brochure'][0]) && is_array($data['brochure'][0])) {
            foreach($data['brochure'][0] as $key => $value){
                if(in_array($key,['total_investment_min','total_investment_max'])){
                    $temp_array[$key] = @$value;
                }
            }
        }

        foreach($loops as $key => $value){
            foreach($value as $item){
                if($key == 'data'){
                    $json[$item] = @$data[$item];
                }else if($key == 'brochure'){
                    $temp_data = @$data['brochure'][0][$item];
                    if(in_array($item,['total_investment_min','total_investment_max'])){
                        if(!is_null($temp_array[$item] ?? null)){
                            $temp_number = ($item == 'total_investment_min') ? 0 : 1;
                            $total_investment[$temp_number] = '$'.number_format($temp_data);
                        }
                    }
                    $json[$item] = $temp_data;
                }else if($key == 'address'){
                    $address[$item] = @$data['addresses'][0][$item];
                }
            }
        }

        $description = trim($data['brochure'][0]['long_description'] ?? '');
        $description = @html_entity_decode($description);
        $description = $shorts[0] = str_replace(array("\r", "\n"), '', $description);
        $description = preg_replace('/(<[^>]*) style=("[^"]+"|\'[^\']+\')([^>]*>)/i', '$1$3', $description);
        $description = preg_replace('#<a.*?>(.*?)</a>#i', '', $description);
        $json['locations'] = (isset($data['locations'][0]['states']) && $data['locations'][0]['states']) ? implode(', ',$data['locations'][0]['states']) : '';
        $json['long_description'] =  $description;
        $json['image_url'] = $logo;
        $json['address'] = (!is_null($address['city'])) ? implode(', ',$address) : null;
        $json['total_investment'] = implode(' - ',$total_investment);
        if($json['total_investment'] == ''){
            $json['total_investment'] = null;
        }
        $json['new_franchise_units'] = (is_numeric($json['franchise_units']) && !is_null($json['franchise_units'])) ? number_format($json['franchise_units']) : null;
        foreach(['investment','min_net_worth','franchise_fee'] as $item){
            $json['new_'.$item] = (is_numeric($json[$item]) && !is_null($json[$item])) ? '$'.number_format($json[$item]) : null;
        }
    }
    $return[$masterkey] = $json;
}
$total = count($return);
if(isset($_GET['type'])){
    if($_GET['type'] == 'table'){
        $title = $total.' Listing(s) &mdash; '.$start.' - '.($end - 1);
        echo '<html><head><title>'.$title.'</title></head><body><h1>'.$title.'</h1><p>&nbsp;</p>'.create_table_from_array($return).'</body></html>';
    }else if($_GET['type'] == 'csv'){
        $csv_name = 'myaccount-franchises.csv';
        download_send_headers($csv_name);
        echo array2csv($return);
    }
}else{
    $final = ['details' => ['start' => $start, 'end' => ($end - 1), 'total' => $total, 'data' => $return]];
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($final);
}
die;
?>