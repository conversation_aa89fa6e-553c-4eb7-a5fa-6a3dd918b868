<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $postData = file_get_contents('php://input');
    $data = json_decode($postData, true);
    if($data && $data['comments']){
        $sanitized_a = filter_var($data['email'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $fields['email'] = strip_tags($sanitized_a);
        $fields['name'] = strip_tags($data['first_name']).' '.strip_tags($data['last_name']);
        $fields['phone'] = strip_tags($data['phone']);
        $fields['message'] = strip_tags($data['comments']);
        $fields['ip_address'] = $ipAddress;
        $fields['geo_located_country'] = $geo['country'] == 'ca' ? 'CAN' : 'USA';
        $fields['url'] = @$relative_path;
        $st = '';
        if (isset($geo) && $geo && $geo['state']) {
            $st = strtoupper($geo['state']);
        }else if(isset($_SESSION['myac_state_code']) && $_SESSION['myac_state_code']){
            $st = strtoupper($_SESSION['myac_state_code']);
        }
        $fields['state'] = $st;
        $fields['inquiry_type'] = 'help_feedback';
        $fields['site_id'] = 60;

        $url = $api_url.'/api/feedback_save';

        $fields_string = json_encode($fields);

        //open connection
        $ch = curl_init();

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);

        //So that curl_exec returns the contents of the cURL; rather than echoing it
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

        //execute post
        $result = curl_exec($ch);
        $returned = json_decode($result,true);
    }else{
        $returned = ['status'=>'failure','message'=>'Comments filed is required'];
    }
    echo json_encode($returned);
?>