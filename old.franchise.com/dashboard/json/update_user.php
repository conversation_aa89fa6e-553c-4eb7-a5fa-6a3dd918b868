<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $postData = file_get_contents('php://input');
    $data = json_decode($postData, true);

    if($data){
        $fields['id'] = $_SESSION['myac_login_contact_id'];
        $fields['first_name'] = strip_tags($data['first_name']);
        $fields['last_name'] = strip_tags($data['last_name']);
        $fields['phone'] = strip_tags($data['phone']);
        $fields['zip_code'] = strip_tags($data['zip_code']);
        $fields['state_code'] = strip_tags($data['state_code']);
        $fields['site_id'] = 60;

        $url = $api_url.'/api/myaccount/updateuser';

        $fields_string = json_encode($fields);

        //open connection
        $ch = curl_init();

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);

        //So that curl_exec returns the contents of the cURL; rather than echoing it
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

        //execute post
        $result = curl_exec($ch);
        $returned = json_decode($result,true);
        if ($returned['status'] == 'success') {
            $data = $returned['data'];
            $_SESSION['myac_first_name'] = $data['first_name'];
            $_SESSION['myac_last_name'] = $data['last_name'];
            $_SESSION['myac_state_code'] = $data['state_code'];
            $_SESSION['myac_phone'] = $data['phone'];
            $_SESSION['myac_zip_code'] = substr("00000{$data['zip_code']}", -5);
            $_SESSION['myac_email_unsubscribe'] = $data['email_unsubscribe'];
        }
    }else{
        $returned = ['status'=>'failure'];
    }
    echo json_encode($returned);
?>