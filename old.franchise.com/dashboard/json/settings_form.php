<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
    $temp_zip_code = isset($_SESSION['myac_zip_code']) ? (($_SESSION['myac_zip_code'] == 0) ? '' : substr("00000{$_SESSION['myac_zip_code']}", -5)) : null;

    $passwordSection = '<div class="row form_field">
                            <label>Current Password</label>
                            <div class="row_item">
                                <input type="password" class="form-control" name="current_password" placeholder="Current Password" autocomplete="off">
                                <div class="error_msg hide"></div>
                            </div>
                        </div>
                        <div class="row form_field">
                            <label>New Password</label>
                            <div class="row_item">
                                <input type="password" class="form-control" name="new_password" placeholder="New Password" autocomplete="off" oninput="this.value = correctPwdPattern(this);">
                                <div class="error_msg hide"></div>
                            </div>
                        </div> 
                        <div class="row form_field">
                            <label>Confirm Password</label>
                            <div class="row_item">
                                <input type="password" class="form-control" name="confirm_password" placeholder="Confirm Password" autocomplete="off" oninput="this.value = correctPwdPattern(this);">
                                <div class="error_msg hide"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="row_item">
                                '.radio_checkbox('checkbox','show_change_password','yes','Show Password',false).'
                            </div>
                        </div>
                        <div class="row form_field btn_field">
                            <button type="button" class="button" onclick="modal(\'settings_modal\',\'close\');">Cancel</button>
                            <button type="button" class="button solid" onclick="changePassword();">Update</button>
                        </div>
                        <br>
                        <p class="bold">Note :</p>
                        <div class="row form_field password_note">
                            <p><span class="bold">Password must be At least</span> <ul class="style_ul"><li>One uppercase letter</li><li>One of the ! @ $ characters</li><li>7 characters long</li></ul></p>
                        </div>';

                        // <input type="checkbox" id="email_unsubscribe" class="form-control" name="email_unsubscribe" value="" '.($_SESSION['myac_email_unsubscribe'] == 0 ? 'checked' : '').'> Receive Email Notifcations
    $notificationSection = '<div class="row form_field">
                            <p class="bold">Receive Email Notifcations</p>
                            <div class="row_item">
                                <label class="switch">
                                    <input id="email_unsubscribe" name="email_unsubscribe" type="checkbox" '.((isset($_SESSION['myac_email_unsubscribe']) && ($_SESSION['myac_email_unsubscribe'] == 0)) ? 'checked' : '').'>
                                    <span class="slider round"></span>
                                </label>
                                
                            </div>
                        </div>
                        <div class="row form_field btn_field">
                            <button type="button" class="button" onclick="modal(\'settings_modal\',\'close\');">Cancel</button>
                            <button type="button" class="button solid" onclick="updateEmailSetting();">Update</button>
                        </div>';

    
    $settings_form = '<h2>My Information</h2>
        <p class="text-center">Manage settings for your profile</p>
        <br>
        <form method="POST">
            <div class="settings_form">
                <div class="settings_item settings_menu">
                    <ul class="settings_nav">
                        <li class="settings_nav_item active" data-item="personal_info" onclick="toggleSettingsNav(this);">
                            Personal Info 
                            <div class="settings_toggle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
                            </div>
                        </li>
                        <li class="settings_nav_item" data-item="notifications" onclick="toggleSettingsNav(this);">
                            Notifications
                            <div class="settings_toggle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
                            </div>
                        </li>
                        <li class="settings_nav_item" data-item="change_password" onclick="toggleSettingsNav(this);">
                            Change Password
                            <div class="settings_toggle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="settings_item">
                    <div id="settings_pi">
                        <div class="row form_field">
                            <label>First Name</label>
                            <div class="row_item">
                                <input type="text" class="form-control" name="first_name" placeholder="First Name" value="'.@$_SESSION['myac_first_name'].'">
                                <div class="error_msg hide"></div>
                            </div>
                        </div>
                        <div class="row form_field">
                            <label>Last Name</label>
                            <div class="row_item">
                                <input type="text" class="form-control" name="last_name" placeholder="Last Name" value="'.@$_SESSION['myac_last_name'].'">
                                <div class="error_msg hide"></div>
                            </div>
                        </div>
                        <div class="row form_field">
                            <label>Email</label>
                            <div class="row_item">
                                <input type="email" class="form-control readonly" name="email" placeholder="Email" value="'.@$_SESSION['myac_email'].'">
                                <div class="error_msg hide">Email is required.</div>
                            </div>
                        </div>
                        <div class="row form_field">
                            <label>Phone</label> 
                            <div class="row_item">
                                <input type="text" class="form-control" name="phone" placeholder="Phone" value="'.format_phone(@$_SESSION['myac_phone']).'" oninput="this.value=formatPhoneNumber(this.value);">
                                <div class="error_msg hide">Phone is required.</div>
                            </div>
                        </div>
                        <div class="row form_field">
                            <label>Your ZIP Code</label> 
                            <div class="row_item">
                                <input type="text" class="form-control" name="zip_code" maxlength="5" placeholder="ZIP Code" value="'.$temp_zip_code.'" oninput="this.value=formatNumber(this.value);">
                                <div class="error_msg hide"></div>
                            </div>
                        </div>
                        <div class="row form_field">
                            <label>Desired Franchise Location</label>
                            <div class="row_item">
                                '.select(getStates(),'state',@$_SESSION['myac_state_code'],'state').' 
                                <div class="error_msg hide"></div>
                            </div>
                        </div>
                        <div class="row form_field btn_field">
                            <button type="button" class="button" onclick="modal(\'settings_modal\',\'close\');">Cancel</button>
                            <button type="button" class="button solid" onclick="updateUser();">Update</button>
                        </div>
                    </div>
                    <div id="settings_nt" class="settings_nt hide">
                        '.$notificationSection.'
                    </div>
                    <div id="settings_pw" class="hide">
                        '.$passwordSection.'
                    </div>
                </div>
                <h2 class="change_pw_title hidden-lg">Notifications</h2>
                <div class="settings_item settings_nt hidden-lg">
                    '.$notificationSection.'
                </div>
                <h2 class="change_pw_title hidden-lg">Change Password</h2>
                <div class="settings_item hidden-lg">
                    '.$passwordSection.'
                </div>
            </div>
        </form>';


    echo json_encode($settings_form);