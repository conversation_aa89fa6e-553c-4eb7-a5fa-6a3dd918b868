<?php

// Global Includes
include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

$notifications = '';
if (isset($_GET['count'])) {
    $queryParam = '?message_read=0';
}else{
    $queryParam = '?message_read='.$_GET['is_read'];
}
$notifications_arr = get_json('/api/my_account_notification/get_notifications/'.$_SESSION['myac_login_contact_id'].$queryParam);
$notifications_arr = json_decode($notifications_arr,true);
if (isset($_GET['count'])) {
    $i=0;
    foreach ($notifications_arr['data'] as $key => $value) {
        if($value['message_read']==0){
            $i++;
        }
    }
    echo $i;die;
    // echo count($notifications_arr['data']);die;
}

if ($notifications_arr['data'] && count($notifications_arr['data']) > 0) {
    foreach ($notifications_arr['data'] as $key => $value) {
        $initials = getInitials($value['display_name']);
        $boldClass = $value['message_read'] == 0 ? 'bold' : '';
        $notifications .= '<li onclick="modalNotification('.$value['myac_notification_messages_id'].','.$value['id'].');">
                <div class="notification_image"><div class="avatar '.$value['type_name'].'">'.$initials.'</div></div>
                <div class="notification_text">
                    <p id="notification_'.$value['id'].'" class="'.$boldClass.'">'.$value['message_title'].'</p>
                    <small>'.date("M j, Y",strtotime($value['create_date'])).'</small>
                </div>
                <div class="notification_toggle">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
                </div>
        </li>';
    }
}else{
    $notifications = '<li class="d-block text-center">No Notifications Found</li>';
}
$notification_list = '<ul>'.$notifications.'</ul>';
header('Content-Type: application/json; charset=utf-8');
echo json_encode($notification_list);
?>