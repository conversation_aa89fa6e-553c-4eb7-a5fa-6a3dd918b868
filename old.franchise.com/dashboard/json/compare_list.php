<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $concept_ids = $_GET['concept_ids'];
    
    $temp_array = get_json('/api/get_compare_profile/60?concept_ids='.$concept_ids);
    $temp_array = json_decode($temp_array,true);
    $temp_array = $temp_array['data'];

    $temp = '';
    $side = $_GET['side'];
    $compare = $_GET['compare'];
    $compare_with = $_GET['compare_with'];

    if ($temp_array && count($temp_array) > 0) {
        foreach ($temp_array as $key => $value) {
            if($value['images'][0]['image_url']){
                $logo = $value['images'][0]['image_url'];
            }else{
                $logo = 'https://franchise-ventures-general.s3.amazonaws.com/global/images/checkered.png'; 
            }
            $banner = '';
            $is_delete = '';
            $shadow = '';
            $is_checked = '';
            $input_name = 'change_list_ids';
            if (in_array($value['id'],[$compare,$compare_with])) {
                $banner = '<div class="tick_bg"><i class="fa fa-check"></i></div>';
                $is_delete = 'no_show';
                $is_checked = 'checked';
                if ($value['id'] == $_GET['compare_with']) {
                    $shadow = 'shadow';
                    $input_name = 'change_list_ids_shadow';
                }
            }
            $temp .= '<div data-id="'.$value['id'].'" class="compare_change_item '.$shadow.'">
                    <div class="compare_change_img">
                        <input type="radio" class="change_list_ids" onchange="select_new_compare('.$value['id'].','.$compare_with.',\''.$side.'\');" data-id="'.$value['id'].'" name="'.$input_name.'" '.$is_checked.'>
                        <img src="'.$logo.'" alt="">
                        <div class="tick_bg"><i class="fa fa-check"></i></div>
                    </div>
                    <div class="compare_change_title">
                        <p class="change_name" onclick="select_new_compare_name('.$value['id'].','.$compare_with.',\''.$side.'\');" data-id="'.$value['id'].'">'.$value['name'].'</p>
                        <div class="compare_change_actions">
                            <div class="listing_favorites">
                                <input type="checkbox" class="favorites" data-fboid="'.$value['fbo_id'].'" onclick="favorites('.$value['fbo_id'].');">
                            </div>
                            <i id="delete_'.$value['id'].'" class="fa fa-trash '.$is_delete.'" onclick="removeFromCompareModal('.$value['id'].');"></i>
                        </div>
                    </div>
                </div>';
        }
        $temp .= '<button class="compare_change_btn" onclick="compare_change_selected();">COMPARE SELECTED</button>';
    }

    echo json_encode($temp);

?>