<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    if (isset($_GET['id'])) {
        $temp_url = '/api/myaccount_articles/'.$_GET['id'];
        if (isset($_GET['url'])) {
            $temp_url .= '?url='.ltrim($_GET['url'], '/');
        }
        $temp_data = get_json($temp_url);
        $temp_data = json_decode($temp_data,true);
        $temp_data = $temp_data['data'];
        // debug($temp_data);
        $return = [];
        if ($temp_data) {
            $return = [
                'title'=>$temp_data['title'],
                'url'=> '/dashboard/resources/'.$temp_data['url']
            ];
            $image_url = $temp_data['og_image'] ? $temp_data['og_image'] : '/images/solid.png';
            $return['article'] = '<div class="guts">
                <div class="modal_banner">
                    <div class="close_modal" onclick="modal(\'article_modal\',\'close\');">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/></svg>
                    </div>
                    <div id="listing_favorites">
                        <span>&nbsp;Saved</span>
                        <div class="listing_favorites saved">
                            <input type="checkbox" class="favorites" data-articleid="'.$_GET['id'].'" onclick="favorites('.$_GET['id'].',true);">
                        </div>
                    </div>
                </div>
                <div class="content">
                    <article class="article">
                        <div class="article_banner" style="--bg-image: url(\''.$image_url.'\');"></div>
                        <h1>'.$temp_data['title'].'</h1>
                        '.$temp_data['body_content'].'
                    </article>
                </div>
            </div>';
            $article_arr['title'] = $temp_data['title'];
            $article_arr['content'] = $article_info ?? '';
        }
        echo json_encode($return);
    }


?>