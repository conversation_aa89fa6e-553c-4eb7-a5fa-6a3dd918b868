<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $postData = file_get_contents('php://input');
    $data = json_decode($postData, true);

    if($data && $data['email']){
        $sanitized_a = filter_var($data['email'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        if (filter_var($sanitized_a, FILTER_VALIDATE_EMAIL)) {
            $fields['email'] = strip_tags($data['email']);
            $fields['password'] = strip_tags($data['password']);
            $fields['token'] = strip_tags($data['token']);
            $fields['site_id'] = 60;
    
            $url = $api_url.'/api/oneview/reset';
    
            $fields_string = json_encode($fields);
    
            //open connection
            $ch = curl_init();
    
            //set the url, number of POST vars, POST data
            curl_setopt($ch,CURLOPT_URL, $url);
            curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch,CURLOPT_POST, true);
            curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);
    
            //So that curl_exec returns the contents of the cURL; rather than echoing it
            curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 
    
            //execute post
            $result = curl_exec($ch);
            $returned = json_decode($result,true);
           
        }else{
            $returned = ['status'=>'failure'];
        }
    }
    echo json_encode($returned);
?>