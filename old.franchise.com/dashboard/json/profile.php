<?php
header('Content-Type: application/json; charset=utf-8');
header('User-Agent: FVAgent/1.0');
$geo_ignore = true;
    if((strlen($_GET['id'] ?? '') == 6 && is_numeric($_GET['id']))){
        $concept_id = $_GET['id'];
        $json = $return = $media = $finance = $total_investment = $temp_array = $details = $address = array();
        $logo = null;
        include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
        if(isset($_GET['url'])){
            $temp_checker = get_json('/api/profiles_mapping/60');
            $checker = json_decode($temp_checker,true);
            $concept_id = $checker['data'][$_GET['url']];
        }
        $profile_results = '/api/get_profile/60/'.$concept_id;
        $profileServices = get_json($profile_results);
        $profileServices = json_decode($profileServices,true);
        $loops = [
            'data' => ['name','fbo_id','category_name','investment'],
            'brochure' => ['intro_description','long_description','home_based','year_founded','franchising_since','franchise_units','military_discount','military_veteran_details','brochure_url','financing_available','is_training_available','is_mobile_franchise','total_investment_min','total_investment_max','min_net_worth','franchise_fee','royalty_fee'],
            'address' => ['city','state']
        ]; 
        $data = @$profileServices['data'][0];

        if (isset($data['brochure'][0]['images']) && is_array($data['brochure'][0]['images'])) {
            foreach ($data['brochure'][0]['images'] as $key => $value) {
                if($value['type'] == 'background'){
                    $logo = $value['image_url']; 
                    break;
                }
            }
        }
        if(is_null($logo)){
            $logo = 'https://franchise-ventures-general.s3.amazonaws.com/global/images/checkered.png'; 
        }
        
        if (isset($_GET['get_image'])) {
            $json['image_url'] = $logo;
            $json['name'] = @$data['name'];
            $json['fbo_id'] = @$data['fbo_id'];
            $json['id'] = @$concept_id;
        }else{
            if (isset($data['brochure'][0]) && is_array($data['brochure'][0])) {
                foreach($data['brochure'][0] as $key => $value){
                    if(in_array($key,['total_investment_min','total_investment_max'])){
                        $temp_array[$key] = @$value;
                    }
                }
            }

            foreach($loops as $key => $value){
                foreach($value as $item){
                    if($key == 'data'){
                        $json[$item] = @$data[$item];
                    }else if($key == 'brochure'){
                        $temp_data = @$data['brochure'][0][$item];
                        if(in_array($item,['total_investment_min','total_investment_max'])){
                            if(!is_null($temp_array[$item] ?? null)){
                                $temp_number = ($item == 'total_investment_min') ? 0 : 1;
                                $total_investment[$temp_number] = '$'.number_format($temp_data);
                            }
                        }
                        $json[$item] = $temp_data;
                    }else if($key == 'address'){
                        $address[$item] = @$data['addresses'][0][$item];
                    }
                }
            }
    
            $description = trim($data['brochure'][0]['long_description'] ?? '');
            $description = @html_entity_decode($description);
            $description = $shorts[0] = str_replace(array("\r", "\n"), '', $description);
            $description = preg_replace('/(<[^>]*) style=("[^"]+"|\'[^\']+\')([^>]*>)/i', '$1$3', $description);
            $description = preg_replace('#<a.*?>(.*?)</a>#i', '', $description);
            $json['locations'] = (isset($data['locations'][0]['states']) && $data['locations'][0]['states']) ? implode(', ',$data['locations'][0]['states']) : '';
            $json['long_description'] =  $description;
            $json['image_url'] = $logo;
            $json['address'] = (!is_null($address['city'])) ? implode(', ',$address) : null;
            $json['total_investment'] = implode(' - ',$total_investment);
            if($json['total_investment'] == ''){
                $json['total_investment'] = null;
            }
            $json['new_franchise_units'] = (is_numeric($json['franchise_units']) && !is_null($json['franchise_units'])) ? number_format($json['franchise_units']) : null;
            foreach(['investment','min_net_worth','franchise_fee'] as $item){
                $json['new_'.$item] = (is_numeric($json[$item]) && !is_null($json[$item])) ? '$'.number_format($json[$item]) : null;
            }
        }
        if (json_last_error() === JSON_ERROR_NONE) {
            echo json_encode($json);
        }else{
            echo '{"deleted":["'.$_GET['id'].'"],"results":[],"status":{"code":1,"message":"success"},"remove":[],"disclaimer":""}';
        }
    }else{
        die;
    }
?>