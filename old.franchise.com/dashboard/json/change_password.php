<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $postData = file_get_contents('php://input');
    $data = json_decode($postData, true);

    if($data && $data['current_password']){
        $fields['email'] = $_SESSION['myac_email'];
        $fields['current_password'] = strip_tags($data['current_password']);
        $fields['new_password'] = strip_tags($data['new_password']);
        $fields['site_id'] = 60;

        $url = $api_url.'/api/oneview/change';

        $fields_string = json_encode($fields);

        //open connection
        $ch = curl_init();

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);

        //So that curl_exec returns the contents of the cURL; rather than echoing it
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

        //execute post
        $result = curl_exec($ch);
        $returned = json_decode($result,true);
    }else{
        $returned = ['status'=>'failure'];
    }
    echo json_encode($returned);
?>