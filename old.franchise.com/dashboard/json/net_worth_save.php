<?php
    // Beta Domain Check
    $beta_check = explode('.',$_SERVER['HTTP_HOST']);
    $dashboard = (!in_array('beta',$beta_check)) ? '/dashboard' : '';
    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].$dashboard.'/includes/global.php');

    $postData = file_get_contents('php://input');
    $data = json_decode($postData, true);

    if($data && $data['net_worth']){
        foreach ($data as $key => $value) {
            $fields[$key] = $value;
        }

        if (empty($fields['login_contact_id'])) {
            $fields['login_contact_id'] = $_SESSION['myac_login_contact_id'];
        }

        $url = $api_url.'/api/create_net_worth_calculator';

        $fields_string = json_encode($fields);

        //open connection
        $ch = curl_init();

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);

        //So that curl_exec returns the contents of the cURL; rather than echoing it
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

        //execute post
        $result = curl_exec($ch);
        $returned = json_decode($result,true);
    }else{
        $returned = ['status'=>'failure'];
    }
    echo json_encode($returned);
?>