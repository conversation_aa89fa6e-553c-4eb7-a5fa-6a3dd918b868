<?php
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global_live_dev.php');
    if (isset($_GET['email'])) {
        $email = strip_tags($_GET['email']);
        $site = 60;
        if (isset($_GET['type']) && $_GET['type'] == 'login') {
            $url = $api_url.'/api/oneview/check?email='.$email.'&site='.$site;
        }else{
            $url = $api_url.'/api/checkEmail/'.$email.'?site='.$site;
        }
        $ch = curl_init();  
 
        curl_setopt($ch,CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch,CURLOPT_HEADER, false);  
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    
        $output=curl_exec($ch);
    
        curl_close($ch);
        $array = json_encode(json_decode($output,true));
        echo $array;
    } 

?>