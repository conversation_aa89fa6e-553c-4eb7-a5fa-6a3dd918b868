<?php
    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

    $postData = file_get_contents('php://input');
    $data = json_decode($postData, true);
    if($data && $data['email']){
        $sanitized_a = htmlspecialchars($data['email']);
        if ($sanitized_a) {
            if(isset($_COOKIE['fv_campaign'])){
                $temp_fv_campaign = json_decode($_COOKIE['fv_campaign'],true);
                foreach($temp_fv_campaign as $key => $value){
                    $fields[$key] = $value;
                }
            }
            
            $fields['first_name'] = strip_tags($data['first_name']);
            $fields['last_name'] = strip_tags($data['last_name']);
            $fields['email'] = strip_tags( $sanitized_a);
            $fields['password'] = strip_tags($data['password']);
            $fields['state_code'] = strip_tags($data['state'] ?? "");
            $fields['domain'] = $domain;
            $fields['login_type'] = strip_tags($data['login_type'] ?? '');
            
            $url = $api_url.'/api/myaccount/createuser';
    
            $fields_string = json_encode($fields);
    
            //open connection
            $ch = curl_init();
    
            //set the url, number of POST vars, POST data
            curl_setopt($ch,CURLOPT_URL, $url);
            curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch,CURLOPT_POST, true);
            curl_setopt($ch,CURLOPT_POSTFIELDS, $fields_string);
    
            //So that curl_exec returns the contents of the cURL; rather than echoing it
            curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 
    
            //execute post
            $result = curl_exec($ch);
            $returned = json_decode($result,true);
            if (($returned['status'] ?? '') == 'success') {
                $data = $returned['data'];
                $_SESSION['myac_email'] = $data['email_address'];
                $_SESSION['myac_first_name'] = $data['first_name'];
                $_SESSION['myac_last_name'] = $data['last_name'];
                $_SESSION['myac_login_contact_id'] = $data['id'];
                $_SESSION['myac_state_code'] = $data['state_code'];
                $_SESSION['myac_phone'] = $data['phone'];
                $_SESSION['myac_email_unsubscribe'] = @$data['email_unsubscribe'];
                $_SESSION['myac_utm_campaign'] = @$data['utm_campaign'];
                $_SESSION['myac_utm_medium'] = @$data['utm_medium'];
                $_SESSION['myac_utm_source'] = @$data['utm_source'];
                $_SESSION['myac_utm_type'] = @$data['utm_type'];
                $_SESSION['myac_gclid_mlclkid'] = @$data['gclid_mlclkid'];
                $_SESSION['welcome'] ="YES";
            }
        }else{
            $returned = ['status'=>'failure','message'=>'Email is required'];
        }
    }else{
        $returned = ['status'=>'failure','message'=>'data missing'];
    }

    header('Access-Control-Allow-Methods: POST, GET, DELETE, PUT, PATCH, OPTIONS');
    header('Access-Control-Allow-Headers: token, Content-Type');
    header('Access-Control-Allow-Origin: *');
    header('Content-Type: application/json');
    echo json_encode($returned);
?>