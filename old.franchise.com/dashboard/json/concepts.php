<?php
$returned = ['status'=>'failure'];
$query = [];
$geo_ignore = true;
$include_ignore = 'yes';
include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
$returned = ['status'=>'success','total'=>''];
foreach($_GET as $key => $value){
    $query[$key] = $value;
}
$array = return_results_array('&'.http_build_query($query));
$returned['total'] = $array['pagination']['current_page']['total_count'];
header('Content-Type: application/json');
echo json_encode($returned);
?>