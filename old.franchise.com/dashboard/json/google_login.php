<?php
    // Beta Domain Check
    $beta_check = explode('.',$_SERVER['HTTP_HOST']);
    $dashboard = (!in_array('beta',$beta_check)) ? '/dashboard' : '';
    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].$dashboard.'/includes/global.php');
    if(isset($_GET['idToken'])){
        $idToken = $_GET['idToken'];
        $url = $prefix.'/webservices/socialLogins/callbackGoogle.php?idToken='.$idToken;
        $result = file_get_contents($url);
        echo $result;
        exit;
    }else{
        echo json_encode(['error' => 'ID Token is missing']);
        exit;
    }
?>