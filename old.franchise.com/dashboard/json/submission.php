<?php

    // Global Includes
    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
    $return = ['status'=>'fail','messges'=>'Not submitted','data'=>[]];
    $session_id = uniqid(rand(), true);

    if(isset($_GET['p']) || isset($_GET['z'])){
        $info_update = [
            'id' => $_SESSION['myac_login_contact_id'],
            'first_name' => $_SESSION['myac_first_name'],
            'last_name' => $_SESSION['myac_last_name'],
            'state_code' => $_SESSION['myac_state_code'],
            'phone' => $_SESSION['myac_phone'],
            'zip_code' => substr("00000{$_SESSION['myac_zip_code']}", -5),
            'site_id' => 60
        ];
        if(isset($_GET['p'])){
            $_SESSION['myac_phone'] = $info_update['phone'] = preg_replace("/[^0-9]/", '', $_GET['p']);
        }
        if(isset($_GET['z'])){
            $_SESSION['myac_zip_code'] = $info_update['zip_code'] = preg_replace("/[^0-9]/", '', $_GET['z']);
        }
        $info_update['id'] = $_SESSION['myac_login_contact_id'];
        $info_update_url = $api_url.'/api/myaccount/updateuser';
        $info_updated = httpPost($info_update_url,$info_update);
    }

    //DETECT IF MOBILE
    function is_mobile(){
        $useragent=$_SERVER['HTTP_USER_AGENT'];
        $return = 'desktop';
        if(preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i',$useragent)||preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i',substr($useragent,0,4))){
            $return = 'mobile';
        }
        return $return;
    }

    //open connection
    $parameters = array(
        'firstname' => $_SESSION['myac_first_name'],
        'lastname' => $_SESSION['myac_last_name'],
        'email' => $_SESSION['myac_email'],
        'phone' => $_SESSION['myac_phone'],
        'zipcode' => substr("00000{$_SESSION['myac_zip_code']}", -5),
        'ip_address' => $ipAddress,
        'country' => 'USA',
        'preferred_state' => $_SESSION['myac_state_code'],
        'fbolist' => $_GET['f'],
        'investment' => $_GET['i'],
        'newsletter' => 0,
        'lead_type' => 'multi',
        'udid' => $session_id,
        'session_id' => $session_id,
        'source' => 'web',
        'lead_source' => 'WEB',
        'browser_type' => is_mobile(),
        'submission_url' => null, 
        'form_url' => null,
        'utm_source' => @$_SESSION['myac_utm_source'],
        'utm_medium' => @$_SESSION['myac_utm_medium'],
        'utm_campaign' => @$_SESSION['myac_utm_campaign'],
        'utm_type' => @$_SESSION['myac_utm_type'],
        'gclid_mlclkid' => @$_SESSION['myac_gclid_mlclkid'],
        'lrq' => 1,
        'NEWLEADSYS' => 1,
        'platform_type' => 'WEB',
        'paid' => 0
    );

    // if(isset($_COOKIE['fv_campaign'])){
    //     $temp_fv_campaign = json_decode($_COOKIE['fv_campaign'],true);
    //     foreach($temp_fv_campaign as $key => $value){
    //         $parameters[$key] = $value;
    //     }
    // }

    $request_URL = $prefix . '/60/leadmanagement2.php';
    $return = httpPost($request_URL,$parameters);

    $requested_url = $api_url.'/api/my_account_activity/';
    $requested_parameters = [
        'login_contact_id' => $_SESSION['myac_login_contact_id'],
        'master_types_id' => 316,
        'value' => @$_GET['f']
    ]; 
    $request = httpPost($requested_url.'create',$requested_parameters);    

    header('Content-Type: application/json');
    echo json_encode($return);