<?php 

include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');

$request_URL = $api_url.'/api/oneview/login';
if(isset($_GET['uid']) && isset($_GET['login_token'])){
    $fields = [    
        "email" => $_GET['uid'],
        "password" => base64_decode($_GET['login_token']),
        "tokenLogin" => true,
        "site_id"=>60
    ];
    $array = httpPost($request_URL,$fields);
    // $_SESSION['logged_in'] = false;
    if($array['status'] == 'fail' || $array['status'] == 'error'){
        header('Location: /dashboard/login');
    }else{
        $_SESSION['myac_email'] = $array['data']['email'];
        $_SESSION['myac_first_name'] = $array['data']['first_name'];
        $_SESSION['myac_last_name'] = $array['data']['last_name'];
        $_SESSION['myac_login_contact_id'] = $array['data']['id'];
        $_SESSION['myac_state_code'] = $array['data']['state_code'];
        $_SESSION['welcome'] = 'yes';

        $trigger_msg_URL = '/api/myac_notification_messages?login_contact_id='.$array['data']['id'];
        $msg_data = get_json($trigger_msg_URL);

        header('Location: /');
    }
}

?>