<?php

include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
$copy = '';
$sClass = '';

if (isset($_GET['e'])) {
    if (isset($_SESSION['myac_login_contact_id'])) {
        session_destroy();
    }
    $user = get_json('/api/myaccount/getuser?type=email&search_value='.$_GET['e']);
    $temp_data = json_decode($user,true);
    $data = $temp_data['data'][0];
    $user_id = $data['id'];
}else if (isset($_POST['email_unsubscribe'])) {
    $parameter['email_unsubscribe'] = $_POST['email_unsubscribe'];
    $login_id = $_POST['login_id'];
    $url = $api_url.'/api/myaccount/update_email_setting/'.$login_id;
    $copyObj = httpPost($url,$parameter);
    if ($copyObj['status'] == 'success') {
        $sClass = 'success';
        $copy = $copyObj['data'];
    }else{
        $err_msg = '<p class="red text-center usub_error">Unable to find user.</p>';
    }
}else{
    header('Location: /dashboard/login');
}

?>

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Unsubscribe | MyAccount</title>
        <style>
            html, body{
                font-family: sans-serif;
                font-size: 14px;
            }
            .unsubscribe_div{
                display: flex;
                justify-content: center;
                margin-top: 2rem;
                flex-direction: column;
            }
            strong{
                font-weight: 400;
            }
            form{
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            #email_unsubscribe{
                width: 1rem;
                height: 1rem;
            }

            .row_item{
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .row_item strong{
                font-size: 1.25rem;
            }

            button,.button{
                border-radius: .25rem;
                display: inline-flex;
                height: 2rem;
                padding: 0 1rem;
                color: #1a73e8;
                background-color: #ffffff;
                border: 2px solid #1a73e8;
                box-sizing: border-box;
                text-decoration: none;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-weight: 400;
                font-size: 1.1428571428571428rem;
                margin-top: 1rem;
            }
            :is(button,.button).solid{
                color: #ffffff;
                background-color: #1a73e8;
            }

            .success h2{
                font-size: 1.25rem;
                text-align: center;
            }

            .success p{
                margin-top: 0;
                text-align: center;
            }
        </style>
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-PLWKFTJB');</script>
        <!-- End Google Tag Manager -->
    </head>
    <body>
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/body.php'); ?>
        <div class="unsubscribe_section <?=$sClass?>">
            <div class="unsubscribe_div">
                <?php if ($copy) { ?>
                    <h2>You Have Been Successfully Unsubscribed</h2>
                    <br>
                    <p>You won't receive any further email notifications from the Message Center.</p>
                    <p>Feel free to resubscribe in your settings and never miss an import update.</p>
                <?php }else { ?>
                    <form method="POST" action="<?php echo $dashboard?>/unsubscribe">
                        <input type="hidden" name="login_id" value="<?=$user_id?>">
                        <div class="row_item">
                            <input type="checkbox" id="email_unsubscribe" class="form-control" name="email_unsubscribe" value="1" required> <strong>Unsubscribe me from Message Center email notifications</strong>
                        </div>
                        <button type="submit" class="button solid">Confirm</button>
                    </form>
                    <?php echo @$err_msg; ?>
                <?php } ?>
            </div>
        </div>
    </body>
</html>