<?php
// Beta Domain Check
$beta_check = explode('.',$_SERVER['HTTP_HOST']);
$dashboard = (!in_array('beta',$beta_check)) ? '/dashboard' : '';
?>
<?php if(!is_null($signin)){ ?> 
    
    <!-- SignIn Form -->
    <form id="signin" role="form" class="login_forms" action="javascript:signIn(<?php echo $signin; ?>);" method="POST" data-step="<?php echo $signin; ?>">
        <input type="hidden" name="open_message" value="<?php echo @$openMessageCenter; ?>">
        <input type="hidden" name="login_page" value="<?php echo @$_GET['lp']; ?>">
        <div class="logo text-center">
            <img src="/images/beta_logo.svg" />
        </div>
        <hr/>
        <div id="signin1" class="child">
            <input type="hidden" id="is_error" name="is_error">
            <div class="row login_details">
                <h1>Sign In</h1>
            </div>
            <div class="row label_inline">
                <label>Email</label>
                <div class="row_item">
                    <!-- <input type="email" class="form-control" name="email" placeholder="Email" autofocus onblur="checkEmailExists(this,'signin');"> -->
                    <input type="email" class="form-control" name="email" placeholder="Email" autofocus>
                    <div class="error hide">Email is required.</div>
                </div>
            </div>
            <div id="tooltip" class="tooltip" display="none" style="position: absolute; display: none;"></div>
            <div class="row label_inline">
                <label>Password <?php echo toolTip(); ?></label>
                <div class="row_item">
                    <input type="password" class="form-control" name="password" placeholder="Password" autofocus>
                    <div class="error hide">Password is required.</div>
                </div>
            </div>
            <div class="row">
                <div class="row_item">
                    <?php echo radio_checkbox('checkbox','show_signin_password','yes','Show Password',false); ?>
                    <a class="forgot_pwd_link" href="/dashboard/forgot_password?forgot=password">Forgot password?</a>
                </div>
            </div>
            <div class="row label_inline">
                <button type="submit" class="button solid" onclick="signIn();">Login</button>
            </div>
                <div class="row label_inline">
                    <p style="text-align: center;font-weight: 600;">OR</p>
                </div>
                <div class="row label_inline social-login" >
                    <!-- Google Sign-In Button -->
                    <div id="g_id_onload"
                        data-client_id="87966341561-j7d5uufdc1i3b5dg4a8p2c038nlg84cd.apps.googleusercontent.com" 
                        data-login_uri='<?php echo $dashboard."/google_login.json";?>' 
                        data-callback="onSignIn"
                        data-auto_prompt="false" >
                    </div>
                    <div id="g_id_signin" data-logo_alignment="center" class="g_id_signin" data-type="standard" data-width="350" data-height="40" data-longtitle="true" data-size="large" data-id="signin"></div>
                    
                </div>                
                <!-- Apple Sign-In Button -->
                <div class="row label_inline social-login">
                    <div id="appleid-signin" data-color="black" data-border="true" data-type="sign in" data-width="350" data-height="40" data-id="signin"></div>
                </div>
              <?php /*  <div class="row label_inline social-login">
                    <button type="button" id="linkedin-login-button">Sign in with LinkedIn</button>
                    <div id="user-info"></div>
                </div>        */  ?>       
            <hr/>
            <div class="row label_inline text-center">
            <?php if(isset($_GET['social']) && $_GET['social'] == "yes") { ?>
                Don't have an account? <a href="<?php echo $dashboard; ?>/signup?signup=1&social=yes">Register Now</a><br>
            <?php } else{?>
                Don't have an account? <a href="<?php echo $dashboard; ?>/signup?signup=1">Register Now</a><br>
            <?php } ?>
            </div>
        </div>
    </form>
<?php } ?>