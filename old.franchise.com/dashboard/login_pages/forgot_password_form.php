<?php if(!is_null($forgot)){ ?>
    <!-- Forgot Password Form -->
    <form id="forgot_password" class="login_forms" method="POST" role="form" data-step="<?php echo $forgot; ?>">
        <div class="logo text-center">
            <img src="/images/beta_logo.svg" />
        </div>
        <hr/>
        <div id="forgot_password1" class="child">
            <div class="row login_details">
                <h1>Forgot Password</h1>
                <p>Enter your email address and a password reset email will be sent</p>
            </div>
            <div class="row label_inline">
                <label>Email</label>
                <div class="row_item">
                    <input type="email" class="form-control" name="email" placeholder="Email" autofocus>
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row label_inline">
                <button type="button" class="button solid" onclick="forgetPassword();">Submit</button>
            </div>
        </div>
    </form>
<?php } ?>
<?php if(!is_null($update)){ ?>
    <!-- Update Password Form -->
    <form id="update_password" class="login_forms" role="form" data-step="<?php echo $forgot; ?>">
        <div class="logo text-center">
            <img src="/images/beta_logo.svg" />
        </div>
        <hr/>
        <div id="update_password1" class="child">
            <input type="hidden" name="email" value="<?php echo $_GET['email']; ?>">
            <input type="hidden" name="token" value="<?php echo $_GET['token']; ?>">
            <div class="row login_details">
                <h1>Update Password</h1>
                <p>Make sure to create a strong password that does not contain any personal information</p>
            </div>
            <div id="tooltip" class="tooltip" display="none" style="position: absolute; display: none;"></div>
            <div class="row label_inline">
                <label>Password <?php echo toolTip(); ?></label>
                <div class="row_item">
                    <input type="password" class="form-control" name="update_password" autocomplete="off" placeholder="Password" oninput="this.value = correctPwdPattern(this);" autofocus>
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row label_inline">
                <label>Confirm Password</label>
                <div class="row_item">
                    <input type="password" class="form-control" name="update_password2" autocomplete="off"  placeholder="Confirm" oninput="this.value = correctPwdPattern(this);">
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row">
                <div class="row_item">
                    <?php echo radio_checkbox('checkbox','show_update_password','yes','Show Password',false); ?>
                </div>
            </div>
            <div class="row label_inline">
                <button type="button" class="button solid" onclick="updatePassword();">Submit</button>
            </div>
        </div>
    </form>
<?php } ?>