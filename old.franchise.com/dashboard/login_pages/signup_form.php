<?php
    $temp_geo = @json_decode(@$_COOKIE['geo'],true);
    $state = strtoupper($temp_geo['state'] ?? '');

    // Get States
    $states = getStates();

    $backUrl = "window.location.href='/signup?signup=".($signup-1)."'";
    $beta_check = explode('.',$_SERVER['HTTP_HOST']);
    $dashboard = (!in_array('beta',$beta_check)) ? '/dashboard' : '';
?>
<!-- SignUp Form -->
<form id="signup" class="login_forms" role="form" action="javascript:signUp(event,<?php echo $signup; ?>);" method="POST" data-step="<?php echo $signup; ?>">
    <div class="mobile_promo text-center">
        <div class="logo">
            <img src="/images/beta_logo.svg" />
        </div>
    </div>
    <input type="hidden" id="is_error" name="is_error">
    <?php if((isset($signup) && ($signup == 1)) || !isset($signup)){ ?>
        <input type="hidden" name="login_page" value="<?php echo @$_GET['lp']; ?>">
        <div id="signup1" class="child">
            <div class="row login_details">
                <h1>Start Your Free<br>Account Today</h1>
                <p class="text-center">No credit card required. Free forever.</p>
            </div>
            <div class="row label_inline">
                <label>Email</label>
                <div class="row_item">
                    <input type="email" class="form-control" name="email" placeholder="Email" autofocus>
                    <!-- <input type="email" class="form-control" name="email" placeholder="Email" autofocus onblur="checkEmailExists(this,'signup');"> -->
                    <div class="error hide"></div>
                </div>
            </div>
            <div id="tooltip" class="tooltip" display="none" style="position: absolute; display: none;"></div>
            <div class="row label_inline">
                <label>Password <?php echo toolTip(); ?></label>
                <div class="row_item">
                    <input type="password" class="form-control" name="create_password" autocomplete="off" placeholder="Password" oninput="this.value = correctPwdPattern(this);" autofocus>
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row label_inline">
                <label>Confirm Password</label>
                <div class="row_item">
                    <input type="password" class="form-control" name="confirm_password" autocomplete="off" oninput="this.value = correctPwdPattern(this);" placeholder="Confirm">
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row">
                <div class="row_item">
                    <?php echo radio_checkbox('checkbox','show_signup_password','yes','Show Password',false); ?>
                </div>
            </div>
            <div class="row label_inline">
                <button type="submit" class="button solid signup_button" id="signup_1" onclick="signUp(event,1);">Next</button>
            </div>
            <?php if(isset($_GET['social']) && $_GET['social'] == "yes") { ?>
                <div class="row label_inline">
                    <p style="text-align: center;font-weight: 600;">OR</p>
                </div>
                <div class="row label_inline social-login" >
                    <!-- Google Sign-In Button -->
                    <div id="g_id_onload"
                        data-client_id="87966341561-j7d5uufdc1i3b5dg4a8p2c038nlg84cd.apps.googleusercontent.com" 
                        data-login_uri='<?php echo $dashboard."/google_login.json";?>'
                        data-callback="onSignIn"
                        data-auto_prompt="false" >
                    </div>
                    <div id="g_id_signin" data-logo_alignment="center" class="g_id_signin" data-type="standard" data-width="350" data-height="40" data-longtitle="true" data-size="large" data-id="signup"></div>                    
                </div>
                <!-- Apple Sign-In Button -->
                <!-- <div class="row label_inline social-login">
                    <div id="appleid-signin" data-color="black" data-border="true" data-type="sign in" data-width="350" data-height="40" data-id="signup"></div>
                </div> -->
                <!-- <div class="row label_inline social-login">
                    <button type="button" id="linkedin-login-button">Sign in with LinkedIn</button>
                    <div id="user-info"></div>
                </div> -->
            <?php } ?>
            <hr/>
            <div class="row label_inline text-center">
            <?php if(isset($_GET['social']) && $_GET['social'] == "yes") { ?>
                Already have an account? <a href="<?php echo $dashboard; ?>/login?social=yes">Sign In</a>
            <?php } else { ?>
                Already have an account? <a href="<?php echo $dashboard; ?>/login">Sign In</a>
            <?php } ?>
            </div>
        </div>
        <div class="mobile_promo">
            <h2>Empowering Your<br>Franchise Journey</h2>
        </div>
    <?php } ?>
    <?php if(isset($signup) && ($signup == 2)){ 
            $temp_pass = '';
            if ($_POST['create_password']) {
                $temp_pass = base64_encode($_POST['create_password']);
            }else if($_COOKIE['beta_password']){
                $temp_pass = $_COOKIE['beta_password'];
            }
            ?><input type="hidden" name="login_page" value="<?php echo $_POST['login_page']; ?>"><?php
        ?>
        <input type="hidden" name="email" value="<?php echo $_POST['email']; ?>">
        <input type="hidden" name="password" value="<?php echo $temp_pass; ?>">
        <div id="signup2" class="child">
            <div class="row login_details">
                <h1>Create a New Account</h1>
                <p>Enter your name</p>
            </div>
            <div class="row label_inline">
                <label>First Name</label>
                <div class="row_item">
                    <input type="text" class="form-control" name="first_name" placeholder="First Name" autofocus>
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row label_inline">
                <label>Last Name</label>
                <div class="row_item">
                    <input type="text" class="form-control" name="last_name" placeholder="Last Name">
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row label_inline">
                <label>Desired Franchise Location</label>
                <div class="row_item">
                <?php echo select($states,'state',null,'state'); ?>
                    <div class="error hide"></div>
                </div>
            </div>
            <div class="row login_buttons">
                <button type="button" class="button" onclick="history.back();">Back</button>
                <button type="submit" class="button solid signup_button" id="signup_2" onclick="signUp(event,2);">Sign Up</button>
            </div>
        </div>
    <?php } ?>
</form>