<?php
    $url = $domain.'/dashboard/requested.json?login_contact_id='.$_SESSION['myac_login_contact_id'];
    $json = get_json($url,false);
    $array = json_decode($json,true); 
    if(empty($array)){
        $page_listings = '';
    }else{
        foreach($array as $key => $value){
            $requested[$value['fboid']] = date("M j, Y",strtotime($value['date']));
        }
        $values = array_column($array, 'fboid');
        $filter_query = http_build_query($default_filter);
        $filter = '&'.$filter_query.'&idlist='.implode(',',$values);
        $search_results = return_results_array($filter);
        $page_listings = franchise_results($search_results['data']['data'],true);
    }
?>
<div id="other_main" data-type="favorites">
    <div id="other_title">
        <h1 class="h1_counter">Request History<span></span></h1>
    </div>
    <div id="franchises"><?php echo $page_listings; ?></div>
    <div id="empty">You have not requested any additional information about businesses. <a href="/dashboard/franchise">Explore franchises</a> to discover the ones that are right for you.</div>
</div>