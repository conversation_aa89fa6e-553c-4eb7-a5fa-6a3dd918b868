<?php
    // Variables
    $categories = $filter = $states = [];
    // $categories = [['text'=>'Any Industry', 'value' => '']];
    $post_loop = ['state_code','cat_id','min','max','net_worth_min','net_worth_max','units_min','units_max','home_based','franchising_since','military_discount','requestlist'];
    $sort_by = [
        ['text'=>'Not Sorted', 'value' => 'default'],
        ['text'=>'Franchise Name A-Z', 'value' => 'franchise_az'],
        ['text'=>'Franchise Name Z-A', 'value' => 'franchise_za'],
        ['text'=>'Industry Name A-Z', 'value' => 'industry_az'],
        ['text'=>'Industry Name Z-A', 'value' => 'industry_za'],
        ['text'=>'Cash Required High to Low', 'value' => 'mcr_high'],
        ['text'=>'Cash Required Low to High', 'value' => 'mcr_low'],
        ['text'=>'Franchising Oldest to Newest', 'value' => 'mcr_old'],
        ['text'=>'Franchising Newest to Oldest', 'value' => 'mcr_new'],
        ['text'=>'Franchise Size Max to Min', 'value' => 'units_high'],
        ['text'=>'Franchise Size Min to Max', 'value' => 'units_low']
    ];
    $homeState = isset($_POST['state_code']) ? $_POST['state_code'] : return_home_state();

    // Get States
    $temp_states_json = get_json('/api/location?site_id=60');
    $temp_states_array = json_decode($temp_states_json,true);
    foreach($temp_states_array['data']['states'] as $key => $value){
        $states[] = ['text' => $value['state_name'], 'value' => $value['state_code']];
    }

    // Get Categories
    $temp_categories_json = get_json('/api/categories/60');
    $temp_categories_array = json_decode($temp_categories_json,true);
    if (isset($temp_categories_array['data']) && is_array($temp_categories_array['data'])) {
        foreach($temp_categories_array['data'] as $key => $value){
            $categories[] = ['text' => $value['category_name'], 'value' => $value['category_id']];
        }
        uasort($categories, fn($a, $b) => $a['text'] <=> $b['text']);
    }
    
    $mx_net_worth = $default_filter['max_net_worth'];
    if (!isset($_POST['net_worth_max'])) {
        $net_worth_obj = get_json('/api/get_net_worth_calculator?login_contact_id='.$_SESSION['myac_login_contact_id']);
        $net_worth_array = json_decode($net_worth_obj,true); 
        $net_worth = isset($net_worth_array['data'][0]['net_worth']) ? $net_worth_array['data'][0]['net_worth'] : null;
        $liquid_capital = isset($net_worth_array['data'][0]['liquid_capital']) ? $net_worth_array['data'][0]['liquid_capital'] : 0;
        $net_worth_date = isset($net_worth_array['data'][0]['last_update']) ? $net_worth_array['data'][0]['last_update'] : 0;
        if ($liquid_capital > 500000) {
            $liquid_capital = 500000;
        }
    }

    $filter_query = '';
    if (isset($net_worth)) {
        $mx_net_worth = $net_worth;
        $filter_query = 'net_worth_min=0&net_worth_max='.$net_worth.'&';
        if ($liquid_capital > 0) {
            $filter_query .= 'min=0&max='.$liquid_capital.'&';
        }
    }


    // Create Filter Variable for Search Results
    if(empty($_POST) || isset($_POST['password']) || isset($_POST['create_password'])){
        // Recent Search
        $recent_search_url = $domain.'/dashboard/recent_search.json?login_contact_id='.$_SESSION['myac_login_contact_id'].'&search=true';
        $recent_search_json = get_json($recent_search_url,false);
        $recent_search_array = json_decode($recent_search_json,true); 
        if(empty($recent_search_array)){
            $default_filters = $default_filter;
            $default_filters['state_code'] = $homeState;
            if ($net_worth) {
                $default_filters['net_worth_min'] = 0;
                $default_filters['net_worth_max'] = $net_worth;
                if ($liquid_capital > 0) {
                    $default_filters['min'] = 0;
                    $default_filters['max'] = $liquid_capital;
                }
            }
            $filter_query = http_build_query($default_filters);
        }else{
            $filter_query = $recent_search_array[array_key_last($recent_search_array)];
            $recent_date = array_key_last($recent_search_array);
            parse_str($filter_query, $temp_loop_new);
            if ($net_worth) {
                if (strtotime($net_worth_date) > strtotime($recent_date)) {
                    $temp_loop_new['net_worth_min'] = 0;
                    $temp_loop_new['net_worth_max'] = $net_worth;
                    if ($liquid_capital > 0) {
                        $temp_loop_new['max'] = $liquid_capital;
                    }
                    $filter_query = http_build_query($temp_loop_new);
                }
            }
        }
        parse_str($filter_query, $temp_loop);
        foreach($temp_loop as $key => $value){
            if($key == 'cat_id'){
                $_POST['cat_id'] = explode(',',$value);
            }else{
                $_POST[$key] = $value;
            }
        }
    }else{
        if(empty($_POST['state_code'])){
            $default_filters = $default_filter;
            $default_filters['state_code'] = $homeState;
            $filter_query = http_build_query($default_filters);
        }else{
            foreach($post_loop as $value){
                if(isset($_POST[$value]) && !is_null($_POST[$value]) && $_POST[$value] != ''){
                    $temp_value = (is_array($_POST[$value])) ? implode(',',$_POST[$value]) : $_POST[$value];
                    $filter[$value] = $temp_value;
                }
            }
            $mx_net_worth = isset($_POST['net_worth_max']) ? $_POST['net_worth_max'] : $mx_net_worth;
            $filter_query = http_build_query($filter);
            $new_recent_search_url = $api_url.'/api/my_account_activity/';
            $parameters = [
                'login_contact_id' => $_SESSION['myac_login_contact_id'],
                'master_types_id' => 297,
                'value' => $filter_query
            ];
            $new_recent_search_query = httpPost($new_recent_search_url.'create',$parameters);
        }
    }
    
    $cat_name_arr = [];
    if (isset($_POST['cat_id']) && is_array($temp_categories_array['data'] ?? '')) {
        foreach($temp_categories_array['data'] as $key => $value){
            if (in_array($value['category_id'],$_POST['cat_id'])) {
                $cat_name_arr[] = $value['category_name'];
            }
        }
    }
    // Removing hashing on user_id for testing
    $gtm_data = "if (typeof dataLayer !== 'undefined') {window.dataLayer.push({
        'event': 'franchise_search',
        'user_id': '".$_SESSION['myac_login_contact_id']."',
        'state_code': '".$homeState."',
        'cat_id': '".json_encode($cat_name_arr)."',
        'min': '".@$_POST['min']."',
        'max': '".@$_POST['max']."',
        'units_min': '".@$_POST['units_min']."',
        'units_max': '".@$_POST['units_max']."',
        'home_based': '".((isset($_POST['home_based']) && $_POST['home_based']) ? $_POST['home_based'] : 0)."',
        'military_discount': '".((isset($_POST['military_discount']) && $_POST['military_discount']) ? $_POST['military_discount'] : 0)."',
        'franchising_since': '".@$_POST['franchising_since']."'
    });};";

    // Get Search Results
    $search_results = return_results_array('&'.$filter_query);
    $count = $search_results['pagination']['current_page']['total_count'];

?>
<div id="franchise_buttons">
    <ul>
        <li data-type="filter" onclick="modal('filter_popup');">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M14 16.825l.007.008c0-5.326 5.459-13.114 9.008-16.833h-4.39c-1.534 1.458-3.225 3.646-4.625 6v-6h-3.993v6c-1.4-2.354-3.067-4.542-4.602-6h-4.39c3.549 3.718 8.985 11.506 8.985 16.833l.007-.008v1.175h-3.007l5 6 5-6h-3v-1.175z"/></svg>
            <span>Filter</span>
        </li>
        <li data-type="sort" onclick="modal('sort_popup');">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M8 10v4h4l-6 7-6-7h4v-4h-4l6-7 6 7h-4zm16 5h-10v2h10v-2zm0 6h-10v-2h10v2zm0-8h-10v-2h10v2zm0-4h-10v-2h10v2zm0-4h-10v-2h10v2z"/></svg>
            <span>Sort</span>
        </li>
    </ul>
</div>
<div id="franchise_columns">
    <div id="franchise_filtering">
        <form id="franchise_filter" method="post" onchange="check_count('#franchise_filter');">
            <input type="hidden" id="filtered" name="filtered" value="default">
            <input type="hidden" id="min" name="min">
            <input type="hidden" id="max" name="max">
            <input type="hidden" id="units_min" name="units_min">
            <input type="hidden" id="units_max" name="units_max">
            <input type="hidden" id="net_worth_min" name="net_worth_min">
            <input type="hidden" id="net_worth_max" name="net_worth_max">
            <h3></h3>
            <div class="row label_inline">
                <label>Industry</label>
                <div class="row_item">
                    <?php echo select($categories,'cat_id',@$_POST['cat_id'],'cat_id',true); ?>
                </div>
            </div>
            <div class="row label_inline">
                <label>State</label>
                <div class="row_item">
                    <?php echo select($states,'state_code',@$_POST['state_code'],'state'); ?>
                </div>
            </div>
            <div class="row">
                <label>Minimum Cash Required<input type="text" name="investment" id="investment_amount" readonly disabled="disabled"></label>
                <div class="row_item">
                    <div id="investment_range"></div>
                </div>
            </div>
            <div class="row">
                <label>Net Worth Required<input type="text" name="min_net_worth" id="net_worth_amount" readonly disabled="disabled"></label>
                <div class="row_item">
                    <div id="min_net_worth"></div>
                </div>
            </div>
            <div class="row_item buttons">
                <button type="button" class="filter_nw_btn button" onclick="calculator_modal();">Calculate your Net Worth</button>
            </div>
            <div class="row label_inline">
                <label>Franchising Since</label>
                <div class="row_item">
                    <input type="text" id="franchising_since" name="franchising_since" min="1880" max="2024" maxlength="4" pattern="[0-9]{4}"  placeholder="Ex: 1980" value="<?php echo @$_POST['franchising_since']; ?>"  oninput="this.value=formatNumber(this.value);" />
                </div>
            </div>
            <div class="row">
                <div class="row_item">
                    <?php echo radio_checkbox('checkbox','military_discount','1','Veteran Discount',@$_POST['military_discount']); ?>
                    <?php echo radio_checkbox('checkbox','home_based','1','Home-based',@$_POST['home_based']); ?>
                </div>
            </div>
            <div class="row">
                <label>Number of Existing Units<input type="text" name="franchise_units" id="franchise_units_amount" readonly disabled="disabled"></label>
                <div class="row_item">
                    <div id="franchise_units"></div>
                </div>
            </div>
            <div class="row">
                <div class="row_item buttons">
                    <button class="button solid" type="submit" data-count="<?php echo $count; ?>">See <span class="franchise_count"><?php echo $count; ?></span> Result</button>
                </div>
            </div>
        </form>
    </div>
    <div id="franchise_main">
        <div class="franchise_wrap">
            <div id="franchise_title">
                <h1 class="h1_counter">Your Franchise Results<span></span></h1>
                <div class="sort_section">
                    <div class="sort_by">
                        <label>Sort By</label>
                        <div class="row_item">
                            <?php echo select($sort_by,'sort_by',@$_POST['filtered'],'sort_by'); ?>
                        </div>
                    </div>
                    <div class="compare_by">
                        <span>Compare</span>
                        <label class="switch">
                            <input id="compare_check" type="checkbox" onchange="toggle_compare();">
                            <span class="slider round"></span>
                        </label>
                    </div> 
                </div>
            </div>
            <div id="compare_div" class="compare_section compare-flex hide">
                <div class="compare_container compare-flex">
                    <div class="compare_thumbnail"></div>
                    <div class="compare_thumbnail"></div>
                    <div class="compare_thumbnail"></div>
                    <div class="compare_thumbnail"></div>
                    <div class="compare_thumbnail"></div>
                </div>
                <div class="compare_button">
                    <button id="btn_compare" class="btn_compare" onclick="go_compare();">Compare</button>
                </div>
            </div> 
        </div>
        <?php if(isset($_GET['showsynd']) && ($_GET['showsynd'] == 'yes')){echo return_results_array('&'.$filter_query,true);} ?>
        <div id="franchises"><?php echo franchise_results($search_results['data']['data']); ?></div>
    </div>
</div>
<script>
$(document).ready(function () {
    if (window.location.hash === '#franchise-feedback') {
        modal('feedback_popup');
    }
});
</script>