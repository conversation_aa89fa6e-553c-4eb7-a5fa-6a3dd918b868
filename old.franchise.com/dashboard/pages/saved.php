<?php
    // Articles Results
    $article_url = $domain.'/dashboard/article_favorites.json?login_contact_id='.$_SESSION['myac_login_contact_id'];
    $article_json = get_json($article_url,false);
    $article_array = json_decode($article_json,true); 
    if(empty($article_array)){
        $article_list = '';
    }else{
        $article_list_url = '/api/myaccount_articles/list?article_list='.implode(',',$article_array).'&active=1';
        $article_list_json = get_json($article_list_url);
        $article_list_array = json_decode($article_list_json,true); 
        $article_list = '<div class="articles">'.return_articles($article_list_array['data']).'</div>';
    }
?>
<div id="other_main" data-type="favorites">
    <div id="other_title">
        <h1 class="h1_counter"><em>Your Saved Articles</em><span></span></h1>
    </div>
    <div id="franchise_container">
        <div id="favorites_articles" class="tabbed active" data-before="Articles">
            <div id="articles_content"><?php echo $article_list; ?></div>
            <div id="empty">You current have no favorite articles. <a href="/dashboard/resources">Peruse resources</a> to find articles that rivet you.</div>
        </div>
    </div>
</div>