<?php
    // Franchise Results
    $franchise_url = $domain.'/dashboard/favorites.json?login_contact_id='.$_SESSION['myac_login_contact_id'];
    $franchise_json = get_json($franchise_url,false);
    $franchise_array = json_decode($franchise_json,true); 
    if(empty($franchise_array)){
        $page_listings = '';
    }else{
        $filter_query = http_build_query($default_filter);
        $filter = '&'.$filter_query.'&idlist='.implode(',',$franchise_array);
        $search_results = return_results_array($filter);
        $page_listings = franchise_results($search_results['data']['data'],true);
    }
?>
<div id="other_main" data-type="favorites">
    <div id="other_title">
        <h1 class="h1_counter"><em>Your Favorite Franchises</em><span></span></h1>
    </div>
    <div id="franchise_container">
        <div id="favorites_franchises" class="tabbed active" data-before="Franchises">
            <div id="franchises"><?php echo $page_listings; ?></div>
            <div id="empty">You current have no favorite franchises. <a href="/dashboard/franchise">Explore franchises</a> to discover the ones that are right for you.</div>
        </div>
    </div>
</div>