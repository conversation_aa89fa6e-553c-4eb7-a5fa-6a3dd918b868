<?php
    $franchise_url=$api_url.'/api/get_net_worth_calculator?login_contact_id='.$_SESSION['myac_login_contact_id'];
    $franchise_json = get_json($franchise_url,false);
    $franchise_array = json_decode($franchise_json,true);
    $net_worth_data = isset($franchise_array['data'][0]) ? $franchise_array['data'][0] :null;
?>
<div class="content">
    <h2>Liquid Capital & Net Worth Calculator</h2>
    <p class="text-center calc_intro">Knowing your net worth is important when buying a franchise because it helps determine your financial capacity to invest, operate, and sustain the business through its early stages.</p>
    <p class="text-center calc_intro" style="padding-bottom:0px">Complete the Net Worth Calculator below to determine your net worth and find franchises that fit your financial profile.</p>
    <br>     
    <form action="/dashboard/franchise" method="POST" id="calculator_form">
        <div class="calculator_main">
            <div class="asset_liab">
                <!-- Assets--> 
                <div class="box asset" id="calculator_asset">
                    <input type="hidden" id="login_contact_id" data-key="login_contact_id" value="<?php echo $_SESSION['myac_login_contact_id']?>">
                    <input type="hidden" id="liquid_capital_total" data-key="liquid_capital" value="<?php echo isset($net_worth_data['liquid_capital']) ? $net_worth_data['liquid_capital'] : 0?>">
                    <input type="hidden" id="net_worth_total" data-key="net_worth" value="<?php echo isset($net_worth_data['net_worth']) ? $net_worth_data['net_worth'] : 0?>">
                    <p class="calc_heading">Assets</p>
                    <div class="row label_inline">
                        <label>Total home value</label>
                        <input type="text" inputmode="numeric" id="home_value" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id);this.type='text';" placeholder="0" data-key="home" value="<?php echo (isset($net_worth_data['home']) && $net_worth_data['home']) ? number_format($net_worth_data['home']) :''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Checking accounts</label>
                        <input type="text" inputmode="numeric" id="checking_account" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)"  placeholder="0" data-key="checking" value="<?php echo (isset($net_worth_data['checking']) && $net_worth_data['checking']) ? number_format($net_worth_data['checking']) :''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Savings accounts</label>
                        <input type="text" inputmode="numeric" id="savings_account" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)"  placeholder="0" data-key="savings" value="<?php echo (isset($net_worth_data['savings']) && $net_worth_data['savings']) ? number_format($net_worth_data['savings']):''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Retirement accounts</label>
                        <input type="text" inputmode="numeric" id="retirement_account" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" placeholder="0" onblur="net_worth_calculateFloat(this.id)" data-key="retirement" value="<?php echo (isset($net_worth_data['retirement']) && $net_worth_data['retirement']) ? number_format($net_worth_data['retirement']):''?>">
                    </div>
                    <div class="row label_inline">
                        <div id="tooltip1" class="tooltip" display="none" style="position: absolute; display: none;"></div>
                        <label>Other liquid 
                            <svg class="svg-icon" onmousemove="showNWTooltip(evt, 'For example, Stocks, Bonds, Money Market Accounts, Mutual Funds, ETFs, Cash Value Life Insurance','tooltip1');" onmouseout="hideTooltip('tooltip1');" style="width: 1em; height: 1em;vertical-align: middle;fill: #324972;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M512 81.408a422.4 422.4 0 1 0 422.4 422.4A422.4 422.4 0 0 0 512 81.408z m26.624 629.76a45.056 45.056 0 0 1-31.232 12.288 42.496 42.496 0 0 1-31.232-12.8 41.984 41.984 0 0 1-12.8-30.72 39.424 39.424 0 0 1 12.8-30.72 42.496 42.496 0 0 1 31.232-12.288 43.008 43.008 0 0 1 31.744 12.288 39.424 39.424 0 0 1 12.8 30.72 43.008 43.008 0 0 1-13.312 31.744z m87.04-235.52a617.472 617.472 0 0 1-51.2 47.104 93.184 93.184 0 0 0-25.088 31.232 80.896 80.896 0 0 0-9.728 39.936v10.24h-64v-10.24a119.808 119.808 0 0 1 12.288-57.344A311.296 311.296 0 0 1 555.52 460.8l10.24-11.264a71.168 71.168 0 0 0 16.896-44.032A69.632 69.632 0 0 0 563.2 358.4a69.632 69.632 0 0 0-51.2-17.92 67.072 67.072 0 0 0-58.88 26.112 102.4 102.4 0 0 0-16.384 61.44h-61.44a140.288 140.288 0 0 1 37.888-102.4 140.8 140.8 0 0 1 104.96-38.4 135.68 135.68 0 0 1 96.256 29.184 108.032 108.032 0 0 1 36.352 86.528 116.736 116.736 0 0 1-25.088 73.216z"></path></svg>
                        </label>
                        <input type="text" inputmode="numeric" id="other_liquid" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)"  placeholder="0" data-key="liquid" value="<?php echo (isset($net_worth_data['liquid']) && $net_worth_data['liquid']) ? number_format($net_worth_data['liquid']) :''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Total car value</label>
                        <input type="text" inputmode="numeric" id="car_value" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)" placeholder="0" data-key="car" value="<?php echo (isset($net_worth_data['car']) && $net_worth_data['car']) ? number_format($net_worth_data['car']):''?>">
                    </div>
                    <div class="row label_inline">
                        <div id="tooltip2" class="tooltip" display="none" style="position: absolute; display: none;"></div>
                        <label>Other non-liquid
                        <svg class="svg-icon" onmousemove="showNWTooltip(evt, 'For example, Jewelry, Investment Properties, Business Equity, Collectibles, Private Investments','tooltip2');" onmouseout="hideTooltip('tooltip2');" style="width: 1em; height: 1em;vertical-align: middle;fill: #324972;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M512 81.408a422.4 422.4 0 1 0 422.4 422.4A422.4 422.4 0 0 0 512 81.408z m26.624 629.76a45.056 45.056 0 0 1-31.232 12.288 42.496 42.496 0 0 1-31.232-12.8 41.984 41.984 0 0 1-12.8-30.72 39.424 39.424 0 0 1 12.8-30.72 42.496 42.496 0 0 1 31.232-12.288 43.008 43.008 0 0 1 31.744 12.288 39.424 39.424 0 0 1 12.8 30.72 43.008 43.008 0 0 1-13.312 31.744z m87.04-235.52a617.472 617.472 0 0 1-51.2 47.104 93.184 93.184 0 0 0-25.088 31.232 80.896 80.896 0 0 0-9.728 39.936v10.24h-64v-10.24a119.808 119.808 0 0 1 12.288-57.344A311.296 311.296 0 0 1 555.52 460.8l10.24-11.264a71.168 71.168 0 0 0 16.896-44.032A69.632 69.632 0 0 0 563.2 358.4a69.632 69.632 0 0 0-51.2-17.92 67.072 67.072 0 0 0-58.88 26.112 102.4 102.4 0 0 0-16.384 61.44h-61.44a140.288 140.288 0 0 1 37.888-102.4 140.8 140.8 0 0 1 104.96-38.4 135.68 135.68 0 0 1 96.256 29.184 108.032 108.032 0 0 1 36.352 86.528 116.736 116.736 0 0 1-25.088 73.216z"></path></svg>
                        </label>
                        <input type="text" inputmode="numeric" id="non-liquid" class="assets" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)" placeholder="0" data-key="non_liquid" value="<?php echo (isset($net_worth_data['non_liquid']) && $net_worth_data['non_liquid']) ? number_format($net_worth_data['non_liquid']):''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Asset Total</label>
                        <input type="text" id="total_assets" placeholder="0" readonly data-key="assets" value="<?php echo (isset($net_worth_data['assets']) && $net_worth_data['assets']) ? '$'.number_format($net_worth_data['assets']):0?>">
                    </div>
                </div>
                    
                <!-- Liabilities--> 
                <div class="box liabilities" id="calculator_liabilities">
                    <p class="calc_heading">Liabilities</p>
                    <div class="row label_inline">
                        <label >Total mortgage balance</label>
                            <input type="text" inputmode="numeric" id="mortgage_bal" class="liability" onkeyup="this.value = formatOnlyNumber(this.value)"onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)" placeholder="0" data-key="mortgage" value="<?php echo (isset($net_worth_data['mortgage']) && $net_worth_data['mortgage']) ? number_format($net_worth_data['mortgage']):''?>">
                    </div>
                    <div class="row label_inline">
                        <label >Student Loans</label>
                            <input type="text" inputmode="numeric" id="Stud_loans" class="liability" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)" placeholder="0" data-key="studnt_loans" value="<?php echo (isset($net_worth_data['studnt_loans']) && $net_worth_data['studnt_loans']) ? number_format($net_worth_data['studnt_loans']):''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Total car loan balance</label>
                            <input type="text" inputmode="numeric" id="car_loan_balance" class="liability" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)" placeholder="0" data-key="car_loan" value="<?php echo (isset($net_worth_data['car_loan']) && $net_worth_data['car_loan']) ? number_format($net_worth_data['car_loan']) :''?>">
                    </div>
                    <div class="row label_inline">
                        <label>Other debt</label>
                            <input type="text" inputmode="numeric" id="other_debt" class="liability" onkeyup="this.value = formatOnlyNumber(this.value)" onfocus="this.value = this.value.replace(/,/g, '');" onblur="net_worth_calculateFloat(this.id)" placeholder="0" data-key="debt" value="<?php echo (isset($net_worth_data['debt']) && $net_worth_data['debt']) ? number_format($net_worth_data['debt'])  :''?>">   
                    </div>
                    <div class="row label_inline">
                        <label>Liability Total</label>
                            <input type="text" id="total_liabilities" placeholder="0" readonly data-key="liabilites" value="<?php echo (isset($net_worth_data['liabilites']) && $net_worth_data['liabilites']) ? '$' . number_format($net_worth_data['liabilites']):'$0'?>">
                    </div>
                    <!-- Result-->      
                    <!-- <div class="box calc_result">
                        <p class="" id="liquid_capital" style="color:var(--logo_blue);font-weight:600" >Liquid Capital <span data-key="liquid_capital"><?php echo (isset($net_worth_data['liquid_capital']) && $net_worth_data['liquid_capital']) ? '$' . number_format($net_worth_data['liquid_capital']):'$0'?><span></p>
                        <p class="calc_heading" id="net_worth">Net worth <span data-key="net_worth"><?php echo (isset($net_worth_data['net_worth']) && $net_worth_data['net_worth']) ? '$' . number_format($net_worth_data['net_worth']) : '$0'; ?><span></p>
                        <button type="button" id="see_franchise_button">Revise Franchise Search</button>
                    </div>   -->
                </div>
            </div>
        </div>
    </form>
    <div class="net_worth_totals">
        <p id="liquid_capital">Liquid Capital : <span data-key="liquid_capital"><?php echo (isset($net_worth_data['liquid_capital']) && $net_worth_data['liquid_capital']) ? '$' . number_format($net_worth_data['liquid_capital']):'$0'?><span></p>
        <p id="net_worth">Net Worth : <span data-key="net_worth"><?php echo (isset($net_worth_data['net_worth']) && $net_worth_data['net_worth']) ? '$' . number_format($net_worth_data['net_worth']) : '$0'; ?><span></p>
        <button type="button" id="see_franchise_button">See Matching Franchises</button>
    </div>
</div>


<script>
    function net_worth_calculateFloat(id){
        var valNew = document.getElementById(id).value.replace('$','');
        valNew = Math.round(valNew);
        // var valNew = parseFloat(flVal);
        if (valNew > 0) {
            document.getElementById(id).value = number_formatting(valNew);
        }
    }

    function formatOnlyNumber(num) {
        return num.replace(/[^0-9,]/g, '').replace(/^0+/, '');
    }

    function number_formatting(number) {
        return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $(document).ready(function () {
        // Function to calculate total assets
        function calculateTotalAssets() {
            total_asset = 0;
            $('#calculator_asset div .assets').each(function () {
                let val = $(this).val().replace(/\D+/g, "");
                total_asset = total_asset+Math.round(val);
            });
            $('#total_assets').val('$' + number_formatting(total_asset));
            updateTotal();
            liquidtotal();
        }
        // Function to calculate total liabilities
        function calculateTotalLiabilities() {
            total_liabilities = 0;
            $('#calculator_liabilities div .liability').each(function () {
                let val = $(this).val().replace(/\D+/g, "");
                total_liabilities = total_liabilities+Math.round(val);
            });
            $('#total_liabilities').val('$' + number_formatting(total_liabilities));
            updateTotal();
        }
        // Event listeners for changes
        $('#calculator_asset div .assets').on('change', calculateTotalAssets);
        $('#calculator_liabilities div .liability').on('change', calculateTotalLiabilities);

        // Execute calculations on page load
        calculateTotalAssets();
        calculateTotalLiabilities();

        // Total net worth calculation
        function updateTotal() {
            let total = Math.round(total_asset) - Math.round(total_liabilities);
            total = Math.round(total);
            $("#net_worth span").text('$' + number_formatting(total));
            $("#net_worth_total").val(total);
            // $("#net_worth_total").val(total.toFixed(2));
        }

        // Total liquid capital calculation
        function liquidtotal() {
            let checking =  Math.round($("#checking_account").val().replace(/\D+/g, ""));
            let savings = Math.round($("#savings_account").val().replace(/\D+/g, ""));
            let other = Math.round($("#other_liquid").val().replace(/\D+/g, ""));
            let liquid_total = checking + savings + other;
            liquid_total = Math.round(liquid_total);
            $("#liquid_capital span").text('$' + number_formatting(liquid_total));
            $("#liquid_capital_total").val(liquid_total);
        }
    });
</script>