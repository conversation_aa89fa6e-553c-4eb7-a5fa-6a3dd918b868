html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}

/* Base CSS */
html,html body{
    font-size: 16px;
    line-height: 1.5;
    background-color: #FFF;
    color: #000;
    font-family: "Poppins", sans-serif;
	font-weight: 200;
	font-style: normal;
}

/* Root colors */
:root {
    --black: #000;
	--header: #2c2c2c;
    --white: #FFF;
    --blue: #324972;
    --gray: #AEAEAE;
    --light_gray: #F0F0F0;
    --dark_gray: #7A7A7A;
    --dark_white: #F9F9F9;
    --light_gray: #F0F0F0;
    --green: #84bd00;
    --red: #cc1209;
	--light_blue: #add8e6;
	--button_blue: #1a73e8;
	--logo_blue: #324972;
	--logo_red: #CC1209;
	--main_blue: #E2E9F3;
	--border_blue: #7F9AC7;
	--settings_blue: #f5f9ff;
}
