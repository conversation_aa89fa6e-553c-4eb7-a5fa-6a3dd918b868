.logo img{
    width: 20rem;
    height: auto;
    max-width: 100%;
}
#login {
    padding-top: 1rem;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--white);
    flex-direction: column;
    box-sizing: border-box;
}
#login form{
    width: 100%;
}
#login form h1{
    text-align: center;
}
.promo{
    background: var(--main_blue);
    display: none;
    box-sizing: border-box;
}
.mobile_promo{
    width: 100%;
    max-width: 37.5rem;
    padding: 1rem 0;
    box-sizing: border-box;
}
hr{
    width: 100%;
    border: 1px solid var(--gray);
    border-bottom: none;
    margin: 1rem 0;
}
.forgot_pwd_link{
    font-size: .85rem;
    float: right;
    padding: 0.25rem 0 0;
}
.logo_strong{
    font-weight: 500;
    display: block;
}
@media only screen and (min-width: 48rem) {
    #login{
        padding: 2rem;
    }
    #login form h1{
        font-size: 1.25rem;
    }
}
.row.login_buttons {
    margin: 1.5rem 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
button{
    width: 100%;
}
.row.login_buttons button{
    max-width: calc(50% - .5rem);
}
main#signup_main .mobile_promo h2 {
    font-size: 1.75rem;
    font-weight: 500;
}

/* Sign Up */
main#signup_main form{
    max-width: 100%;
    width: 37.5rem;
    padding: 1rem;
    box-sizing: border-box;
}
main#signup_main form h1{
    font-size: 1.75rem;
    font-weight: 600;
}
main#signup_main h2{
    font-size: 1.75rem;
    font-weight: 500;
    line-height: 1.25;
}
@media only screen and (min-width: 64rem) {
    main#signup_main form h1{
        font-size: 2.5rem;
    }
    main#signup_main h2{
        font-size: 2.5rem;
        padding-top: 4rem;
    }
    .mobile_promo{
        display: none;
    }
    main#signup_main{
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
    }
    main#signup_main form{
        border-radius: .75rem;
        border: 1px solid #c7c9c78c;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        padding: 2rem;
    }
    main#signup_main .promo{
        width: 37.5rem;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: start;
        padding-top: 6rem;
    }
    main#signup_main #login{
        width: calc(100vw - 37.5rem);
        padding: 2rem;
        height: 100vh;
        overflow-x: hidden;
        overflow-y: auto;
    }
}

/* Login */
#login_main{
    padding: 1rem;
}
main#login_main #login{
    max-width: 100%;
    width: 37.5rem;
    padding: 1rem;
    box-sizing: border-box;
    margin: 0 auto;
}
@media only screen and (min-width: 64rem) {
    #login_main{
        padding: 0;
    }
    main#login_main{
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
    }
    main#login_main #login{
        width: 32rem;
        padding: 2rem;
        height: 100vh;
        overflow-x: hidden;
        overflow-y: auto;
    }
    main#login_main .promo{
        width: calc(100vw - 32rem);
        padding: 2rem;
        display: block;
    }
}

/* Other */
main#other_main{
    justify-content: center;
}
main#other_main form{
    max-width: 100%;
    width: 37.5rem;
    padding: 1rem;
    box-sizing: border-box;
}
@media only screen and (min-width: 64rem) {
    main#other_main form{
        border-radius: .75rem;
        border: 1px solid #c7c9c78c;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        padding: 2rem;
    }
}

/* Footer */
#footer {
    font-size: .85rem;
    font-weight: 400;
}
#footer ul {
    display: flex;
    flex-direction: row;
    text-align: center;
    line-height: 1.2;
    padding-bottom: 1rem;
}
#footer ul li:not(:first-child){
    padding-left: 1rem;
}
#footer ul span{
    cursor: pointer;
}
@media only screen and (min-width: 64rem) {
    #footer ul {
        padding-bottom: 0;
    }
}

/* Logins */
.social-login{
    display: flex;
    justify-content: center;
    width: 100%;
}
#linkedin-login-button{
    max-width: 350px;
}