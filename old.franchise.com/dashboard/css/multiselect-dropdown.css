.multi-select {
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    width: 100%;
    user-select: none;
  }
  #filter_popup .multi-select {
    position: relative;
  }
  .multi-select .multi-select-header {
    border: 2px solid var(--gray);
    padding: 7px 30px 7px .35rem;
    overflow: hidden;
    gap: .5rem;
    min-height: 1.5rem;
    max-height: 11.25rem;
    overflow-y: auto;
    overflow-x: hidden;
    user-select: none;
  }
  .multi-select .multi-select-header::after {
    content: "";
    display: block;
    position: absolute;
    top: calc(50% + .25rem);
    right: .5rem;
    transform: translateY(-50%);
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA3LjMzbDIuODI5LTIuODMgOS4xNzUgOS4zMzkgOS4xNjctOS4zMzkgMi44MjkgMi44My0xMS45OTYgMTIuMTd6Ii8+PC9zdmc+);
    background-size: .5rem;
    background-repeat: no-repeat;
    background-position-x: 50%;
    height: .5rem;
    width: .625rem;
  }
  .multi-select .multi-select-header.multi-select-header-active {
    border-color: #c1c9d0;
  }
  .multi-select .multi-select-header.multi-select-header-active::after {
    transform: translateY(-50%) rotate(180deg);
  }
  .multi-select .multi-select-header.multi-select-header-active + .multi-select-options {
    display: flex;
  }
  .multi-select .multi-select-header .multi-select-header-placeholder {
    font-weight: 400;
  }
  .multi-select .multi-select-header .multi-select-header-option {
    display: inline-flex;
    align-items: center;
    background-color: var(--gray);
    font-size: .85rem;
    padding: .25rem .5rem;
    border-radius: .25rem;
    font-weight: 400;
    color: var(--white);
  }
  .multi-select .multi-select-header .multi-select-header-max {
    font-size: .85rem;
    color: var(--gray);
  }
  .multi-select .multi-select-options {
    display: none;
    box-sizing: border-box;
    flex-flow: wrap;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 999;
    margin-top: 5px;
    padding: 5px;
    background-color: #fff;
    border-radius: .25rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    max-height: 15rem;
    overflow-y: auto;
    overflow-x: hidden;
    border: 2px solid var(--gray);
    padding-top: 0;
    scrollbar-width: thin;
  }
  .multi-select .multi-select-options::-webkit-scrollbar {
    width: 5px;
  }
  .multi-select .multi-select-options::-webkit-scrollbar-track {
    background: #f0f1f3;
  }
  .multi-select .multi-select-options::-webkit-scrollbar-thumb {
    background: #cdcfd1;
  }
  .multi-select .multi-select-options::-webkit-scrollbar-thumb:hover {
    background: #b2b6b9;
  }
  .multi-select .multi-select-options .multi-select-option, .multi-select .multi-select-options .multi-select-all {
    padding: 4px 12px;
    height: 42px;
  }
  .multi-select .multi-select-options .multi-select-option .multi-select-option-radio, .multi-select .multi-select-options .multi-select-all .multi-select-option-radio {
    margin-right: .85rem;
    height: 1rem;
    width: 1rem;    
    border: 2px solid var(--gray);
    border-radius: .25rem;
    box-sizing: border-box;
  }
  .multi-select .multi-select-options .multi-select-option .multi-select-option-text, .multi-select .multi-select-options .multi-select-all .multi-select-option-text {
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: inherit;
    font-size: .85rem;
    line-height: 1;
    font-weight: 400;
  }
  .multi-select .multi-select-options .multi-select-option.multi-select-selected .multi-select-option-radio, .multi-select .multi-select-options .multi-select-all.multi-select-selected .multi-select-option-radio {
    border-color: var(--logo_blue);
    background-color: var(--logo_blue);
  }
  .multi-select .multi-select-options .multi-select-option.multi-select-selected .multi-select-option-radio::after, .multi-select .multi-select-options .multi-select-all.multi-select-selected .multi-select-option-radio::after {
    content: "";
    display: block;
    width: 0.25rem;
    height: .5rem;
    margin: -.0125rem 0 0 0.25rem;
    border: solid #fff;
    border-width: 0 0.15em 0.15em 0;
    transform: rotate(45deg);
  }
  .multi-select .multi-select-options .multi-select-option.multi-select-selected .multi-select-option-text, .multi-select .multi-select-options .multi-select-all.multi-select-selected .multi-select-option-text {
    font-weight: 500;
  }
  .multi-select .multi-select-options .multi-select-option:hover, .multi-select .multi-select-options .multi-select-option:active, .multi-select .multi-select-options .multi-select-all:hover, .multi-select .multi-select-options .multi-select-all:active {
    background-color: #f3f4f7;
  }
  .multi-select .multi-select-options .multi-select-all {
    border-bottom: 1px solid var(--logo_blue);
    border-radius: 0;
  }
  .multi-select .multi-select-options .multi-select-search {
    padding: 7px 10px;
    border: 1px solid #dee2e6;
    border-radius: .25rem;
    margin: 10px 10px 5px 10px;
    width: 100%;
    outline: none;
    font-size: 16px;
  }
  .multi-select .multi-select-options .multi-select-search::placeholder {
    color: #b2b5b9;
  }
  .multi-select .multi-select-header, .multi-select .multi-select-option, .multi-select .multi-select-all {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    align-items: center;
    border-radius: .25rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 100%;
    font-size: .85rem;
  }
  
  .multi-select-remove{
    margin-left: 0.2rem;
    font-size: large;
    font-weight: bold;
    padding: 0rem 0.2rem;
  }
  .multi-select-remove:hover{
    color: #000;
  }  

  .multi-select-close-button {
    border: none;
    padding: 5px 10px;
  }

  .multi-select-relative{
    width: 100%;
    position: sticky;
      top: 0;
      background-color: white;
      z-index: 9;
  }
  .multi-select-absolute{
    position: absolute;
    top: 0.25rem;
    right: 0;
    z-index: 9;
  }
