/* Base */
h1 {
    margin-bottom: 1rem;
}
.sticky{
    top: -2rem;
}

/* Tabs */
.tabs li {
    height: 3rem;
    font-size: 1rem;
}

/* Shell */
#shell {
    flex-direction: row;
}

/* Header */
.header_left {
    display: flex;
}
.desktop_nav{
    display: flex;
    align-items: center;
}
.desktop_nav ul {
    display: flex;
    flex-direction: row;
    width: 50vw;
    max-width: 40rem;
    justify-content: space-evenly;
}
.desktop_nav a{
    text-decoration: none;
    color: var(--black);
    font-weight: 400;
}
.desktop_nav li.active a{
    color: var(--button_blue);
}

/* Aside */
#aside {
    display: none;
}

/* Main */
#main{
    min-height: 100vh;
    border-top: 2rem solid var(--main_blue);
    padding: 4rem 2rem 2rem;
}

/* Dashboard */
#dashboard{
    grid-template-columns: repeat(4, 1fr);
}
/* #dashboard .dash_item:not(:first-child) h3{
    background-color: var(--dark_gray);
}
#dashboard .dash_item:not(:first-child) h3 svg{
    fill: var(--light_gray);
} */
.dash_item h3 {
    font-size: 1.25rem;
}
#resource_tabs.tabs {
    padding-top: 2rem;
    margin-top: -2rem;
}
.articles{
    grid-auto-columns: minmax(0, 1fr);
    grid-template-columns: 1fr 1fr 1fr 1fr;
}
.articles_title {
    font-size: 1.25rem;
    margin: 1rem .5rem;
    font-weight: 400;
}

/* Franchise */
#franchise_filter{
    display: block;
}
#franchise_filtering{
    position: sticky;
    top: 6rem;
    align-self: start;
    max-height: calc(100vh - 6rem);
    overflow-y: scroll;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}
#franchise_filtering::-webkit-scrollbar {
    display: none;
}
#franchise_columns{
    display: grid;
    grid-auto-columns: minmax(0, 1fr);
    grid-auto-flow: column;
    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 1rem;
    grid-row-gap: 0px;
}
#franchise_filtering { 
    grid-area: 1 / 1 / 2 / 2;
}
#franchise_main {
    grid-area: 1 / 2 / 2 / 4;
}
#franchise_buttons{
    display:none;
}
.franchise_wrap{
    position: sticky;
    top: 4rem;
    z-index: 2;
    background: var(--main_blue);
    padding-top: 2rem;
    margin-top: -2rem;
}
.sort_by {
    display: flex;
    align-items: center;
    margin-top: 1rem;
}
.sort_by label{
    margin-right: .5rem;
}
.listing_options li.break i:not(:first-child) {
    margin-left: .5rem;
}
@media only screen and (min-width: 75rem) {
    .listings {
        grid-auto-columns: minmax(0, 1fr);
        grid-template-columns: 1fr 1fr 1fr;
    }
    #franchise_main {
        grid-area: 1 / 2 / 2 / 5;
    }
    #franchise_title {
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    .sort_by {
        margin-top: 0;
    }
}
@media only screen and (min-width: 100rem) {
    .listing_name {
        font-size: 1.25rem;
        padding: 1rem .5rem;
        font-weight: 400;
    }
    .listing_options li {
        font-size: .9rem;
    }
    .listing_details{
        font-size: .9rem;
        line-height: 1.5;
    }
    .read_more {
        padding-bottom: 1rem;
    }
}

/* Profile */
.profile_banner{
    min-height: 239px;
}

/* Questions */
main#main iframe:first-child:last-child{
    height: calc(100vh - 4rem);
}

/* Favorites and Requests */
.other_listings {
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

/* Articles & Resources */
@media only screen and (min-width: 75rem) {
    .latest_title {
        font-size: 1.5rem;
    }
    .latest_details {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
}
@media only screen and (min-width: 100rem) {
    .latest_title {
        font-size: 1.75rem;
    }
    .latest_details {
        font-size: 1.5rem;
        line-height: 2rem;
    }
}

.hidden-lg{
    display: none;
} 
@media only screen and (min-width: 100rem) {
    .request_time{
        font-size: .9rem;
    }
}
article.question[data-id="153"] .answers li {
    width: calc(50% - .5rem);
    float: left;
    margin: 0 .25rem .25rem;
}

/* net_worth_calculator */
.calc_intro{
    padding:6px 36px 0px 36px;
}
.calculator_main{
    width: 80%;
}
.asset_liab{
    flex-direction: row;
}
#calculator_asset,#calculator_liabilities{
    gap:0px
}
.calc_result button{
    width:fit-content;
}

.compare_page .go_back_search{
    margin-bottom: 0.5rem;
    display: inline-block;
    text-decoration: none;
    border: 1px solid;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
}