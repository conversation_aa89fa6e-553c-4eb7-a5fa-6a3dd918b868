/* Base */
h1 {
    font-size: 1.5rem;
    text-align: left;
}
h1 + h2{
    text-align: left;
}
#notification_popup.popup .guts{
    width: 50rem;
}
#listing_favorites span{
    display: block;
    padding-right: .5rem;
}
#listing_favorites span:before{
    content: 'Add to';
}

/* Tabs */
.tabs{
    padding: 1rem 0;
}
.tabs li:not([data-before=""]) span:before{
    content: attr(data-before);
    margin-right: .25rem;
}
.tabs li:not([data-after=""]) span:after{
    content: attr(data-after);
    margin-left: .25rem;
}

/* Columns */
.columns svg{
    height: 2rem;
    width: 2rem;
}
.columns li{
    border-radius: 2rem;
}
.columns li span:after{
    content: ' Your Results';
}

/* Header */
header#header .logo img{
    height: 2rem;
}
#header_nav{
    width: 6rem;
}
.h1_counter{
    flex-direction: row;
}
.h1_counter span{
    padding: 0;
}
.h1_counter span:before{
    content: '|';
    padding: 0 .25rem;
    font-size: 1.5rem;
    color: var(--header);
}
.h1_counter em{
    font-size: 1.5rem;
}


/* Aside */
#aside {
    height: 6rem;
}
#aside a {
    font-size: 1rem;
}
#aside svg{
    height: 2rem;
}

/* Dashboard */
#dashboard{
    grid-template-columns: repeat(2, 1fr);
}
.articles{
    grid-template-columns: 1fr 1fr;
}

.articles_title{
    height: 50px;
    overflow: hidden;
}

/* Franchise */
.listings {
    grid-template-columns: 1fr 1fr;
}
#franchise_filter{
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    padding: 1rem;
}
#franchise_filter h3:before{
    content: 'Find Your Franchise';
}
#franchise_filter button.cancel{
    display: none;
}

/* Profile */
#profile_header{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
}
.profile_header_item:first-child { grid-area: 1 / 1 / 2 / 2; }
.profile_header_item:last-child { grid-area: 1 / 2 / 2 / 3; }
#profile_columns{
    border: none;
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
    padding: 1rem 0;
    margin: 0;
}
#profile_columns .profile_column:not(:first-child) h2{
    margin-top: -1rem;
    border-top: none;
}
.profile_header_item{
    align-items: baseline;
    height: fit-content;
}
.profile_banner{
    min-height: 179px;
}
.profile_column{
    border-radius: 0 0 .5rem .5rem;
}
.profile_column,.profile_column:not(:last-child){
    border: 1px solid var(--gray);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
}
#profile_columns:has(.profile_column:last-child:nth-child(2)){
    grid-template-columns: 1fr 1fr;
}
#profile_columns:has(.profile_column:last-child:nth-child(3)){
    grid-template-columns: 1fr 1fr 1fr;
}

/* Questions */
main#main iframe:first-child:last-child{
    height: calc(100vh - 10rem);
}

/* Favorites and Requests */
.other_listings {
    grid-template-columns: 1fr 1fr 1fr;
}

/* Articles & Resources */
#latest_article{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 1rem;
    grid-row-gap: 0;
}
#latest_article .latest_item:last-child { 
    grid-area: 1 / 1 / 2 / 2;
    padding: 1rem;
    box-sizing: border-box;
}
#latest_article .latest_item:first-child { 
    grid-area: 1 / 2 / 2 / 3;
    display: flex;
}
.latest_banner,.latest_title,.latest_details{
    border-bottom: none;
    padding-left: 0;
}
.latest_banner{
    width: 100%;
    border-bottom: none;
    border-left: 1px solid var(--border_blue);
}
.latest_title {
    font-size: 1.25rem;
}
.latest_details {
    font-size: 1rem;
    line-height: 1.5rem;
}
.latest_item .articles_read_more{
    text-align: left;
    padding: 0;
}
.article_banner {
    width: 50%;
    float: right;
    margin-left: 1rem;
}
/* net_worth_calculator */
@media  only screen and (min-width: 768px) {
    .calculator_main{
        width: 80%;
    }
    .asset_liab{
        flex-direction: row;
    }
    #calculator_asset,#calculator_liabilities{
        gap:0px
    }
    .calc_result button{
        width:fit-content;
    }

    .compare_page.cmp_scroll .cmp_header_section{
        top: 4rem;
    }
}

#main.main_compare{
    padding-top: 3rem !important;
}