/* Base */
a{
    color: var(--button_blue);
}
h1,h2,h3 {
    line-height: 1;
    margin-bottom: .5rem;
    text-align: center;
    font-weight: 400;
    font-size: 1.25rem;
    color: var(--header);
}
#main:has(h1 + h2) h1:first-child{
    margin-bottom: .25rem;
}
h1 + h2{
    font-size: 1rem;
    font-weight: 300;
    margin-bottom: 1rem;
    text-align: center;
}
h1:not(:first-of-type){
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px dashed var(--border_blue);
    position: relative;
    z-index: 3;
}
button,.button{
    border-radius: .25rem;
    display: inline-flex;
    height: 2rem;
    padding: 0 1rem;
    color: var(--button_blue);
    background-color: var(--white);
    border: 2px solid var(--button_blue);
    box-sizing: border-box;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 1rem;
}
:is(button,.button).solid{
    color: var(--white);
    background-color: var(--button_blue);
}
.buttons{
    text-align: center;
    margin-top: 1rem;
}
.button.delete{
    background-color: var(--red);
    border-color: var(--red);
}
span.button.disabled{
    color: var(--white);
    background-color: var(--gray);
    border-color: var(--gray);
    cursor: not-allowed;
}
.sticky{
    position: sticky;
    top: 0rem;
    z-index: 2;
    background-color: var(--main_blue);
}
.text-center{
    text-align: center;
}

.hide{
    display: none !important;
}

.bold{
    font-weight: bold;
}

.d-block{
    display: block !important;
}

body:has(#franchise_columns) .columns.sticky li{
    width: calc(50vw - 2rem);
    margin: 0 .5rem 1rem;
}

.red{
    color: var(--red);
}

.small{
    font-size: 80%;
}

.error,.error_msg{
    color: var(--red);
    font-size: .85rem;
    margin: 0.5rem 0.25rem 0;
    line-height: 1.5;
    font-weight: 400;
}

.error-outline{
    border-color: var(--red) !important;
}
.error-outline + .error{
    display: block;
}

.readonly{
    background: var(--main_blue);
    pointer-events: none;
}

.success_msg{
    text-align: center;
    background: var(--green);
    padding: 0.5rem;
    color: var(--white);
}

.error strong, .error_msg strong {
    font-weight: bold;
}

.style_ul{
    list-style: disc;
    padding-left: 1rem;
}

/* Form Elements */
input:is([type="number"],[type="email"],[type="password"],[type="text"]){
    border-radius: .25rem;
    border: 2px solid var(--gray);
    font-size: .85rem;
    height: 2rem;
    width: 100%;
    padding-left: .5rem;
    box-sizing: border-box;
}
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA3LjMzbDIuODI5LTIuODMgOS4xNzUgOS4zMzkgOS4xNjctOS4zMzkgMi44MjkgMi44My0xMS45OTYgMTIuMTd6Ii8+PC9zdmc+) 98% 50% no-repeat #fff;
    background-size: .5rem;
    border-radius: .25rem;
    border: 2px solid var(--gray);
    font-size: .85rem;
    height: 2rem;
    width: 100%;
    padding-left: .85rem;
    transition: all .1s ease;
    text-transform: none;
    word-wrap: normal;
}
:is(.radio_button,.checkbox_button){
    display: inline-flex;
    position: relative;
    margin-right: 1rem;
    height: 2rem;
}
:is(.radio_button,.checkbox_button) input {
    height: 2rem;
    width: 1.5rem;
    opacity: 0;
    position: absolute;
    top: .5rem;
    left: 0;
    z-index: 10;
    margin: 0;
    cursor: pointer;
}
:is(.radio_button,.checkbox_button) label {
    cursor: pointer;
}
:is(.radio_button,.checkbox_button) span {
    padding-left: 1.25rem;
    display: block;
    line-height: 2rem;
    position: relative;
    font-size: .85rem;
}
.radio_button input + span::before {
    display: flex;
    background-color: var(--white);
    position: absolute;
    left: 0;
    top: .5rem;
    user-select: none;
    z-index: 1;
    height: 1rem;
    width: 1rem;
    border-radius: 2rem;
    content: '';
    cursor: pointer;
    align-content: center;
    justify-content: center;
    border: 2px solid var(--gray);
    box-sizing: border-box;
}
.radio_button input:checked + span::after {
    content: '';
    background-color: var(--gray);
    height: .5rem;
    width: .5rem;
    border-radius: .5rem;
    position: absolute;
    left: .25rem;
    top: .75rem;
    z-index: 2;
}
.checkbox_button input + span::before {
    display: flex;
    background-color: var(--white);
    position: absolute;
    left: 0;
    top: .5rem;
    user-select: none;
    z-index: 1;
    height: 1rem;
    width: 1rem;
    border-radius: .25rem;
    content: '';
    cursor: pointer;
    align-content: center;
    justify-content: center;
    border: 2px solid var(--gray);
    box-sizing: border-box;
}
.checkbox_button input:checked + span::before {
    background-color: var(--logo_blue);
    border-color: var(--logo_blue);
}
.checkbox_button input:checked + span::after {
    display: block;
    content: '';
    z-index: 2;
    position: absolute;
    left: .4rem;
    top: .7rem;
    width: 0.25rem;
    height: .5rem;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    box-sizing: border-box;
}
.label_inline{
    position: relative;
    padding-top: .35rem;
    margin-top: 1rem;
}
.label_inline:not(:first-of-type), .help_feedback_section .label_inline{
    margin-top: 1.5rem;
}
.label_inline label{
    background-color: #FFF;
    padding: 0 .25rem;
    inset: -0.35rem auto auto .75rem;
    position: absolute;
    display: inline-block;
    line-height: 1;
    font-size: .85rem;
}
.label_inline :is(select,input){
    height: 2.5rem;
}

/* Slider */
.ui-slider {
    position: relative;
    text-align: left;
}
.ui-slider-horizontal {
    height: 0.8em;
}
.ui-widget {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.ui-widget-content {
    border: 1px solid var(--gray);
    background: var(--white);
}
.ui-corner-all{
    border-radius: 3px;
}
.ui-widget.ui-widget-content {
    background: var(--gray);
    height: .5rem;
    border-radius: 1rem;
    border: none;
    position: relative;
    margin: .5rem .5rem 1rem;
}
.ui-widget-header {
    background: var(--logo_blue);
}
.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0;
}
.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}
.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1.2em;
    height: 1.2em;
    cursor: pointer;
    -ms-touch-action: none;
    touch-action: none;
}
.ui-slider-horizontal .ui-slider-handle {
    top: -0.3em;
    margin-left: -0.6em;
}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
    font-weight: normal;
    outline: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    cursor: pointer;
    position: absolute;
    border: 2px solid var(--logo_blue);
    box-sizing: border-box;
    box-shadow: none;
    background: var(--white);
}
input#investment_amount,input#franchise_units_amount,input#investment_amount2,input#franchise_units_amount2,input#net_worth_amount,input#net_worth_amount2 {
    border: none;
    padding-left: .5rem;
    color: var(--logo_blue);
    font-weight: 500;
}

/* PopUp */
body:has(.popup.show,.modal.show){
    overflow:hidden;
}
.popup:not(.show){
    display:none;
}
.popup.show{
    display: flex;
    inset: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 5;
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(.5rem);
}
#welcome_popup.popup.show{
    z-index: 20;
}
#survey_modal.modal.show,#quiz_modal.modal.show{
    z-index: 15;
}
.popup .guts{
    background: var(--white);
    display: block;
    padding: 1rem 0;
    width: 25rem;
    max-width: calc(100% - 2rem);
    box-sizing: border-box;
    margin: 0 auto;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
    border-radius: .5rem;
    position: relative;
    max-height: calc(100% - 2rem);
}
.popup .guts .content {
    padding: 0 1rem;
    overflow: auto;
    height: 100%;
}
.popup h3{
    margin-bottom: 1.5rem;
}
.popup label{
    font-weight: 500;
}
.popup .row:not(:last-child){
    margin-bottom: .5rem;
}

/* Alert Modal */
#alert{
    display: none;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 150;
    background: rgba(0,0,0,.5);
    backdrop-filter: blur(.5rem);
}
#alert.show{
    display: flex;
}
.alert{
    display: block;
    background: var(--white);
    padding: 1rem;
    width: 25rem;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    border-radius: .5rem;
    position: relative;
}
.alert h3{
    text-align: left;
}
.alert p{
    margin: 1rem 0;
}

/* Modal */
body:has(.modal.show) #main{
    overflow:hidden;
}
.modal:not(.show){
    display:none;
}
.modal.show{
    display: flex;
    inset: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 5;
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(.5rem);
}
.modal .guts{
    background: var(--white);
    display: block;
    padding: 0 1rem 1rem;
    width: 67.5rem;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
    position: relative;
    overflow-y: auto;
    height: 100vh;
}
.modal h3{
    margin-bottom: 1.5rem;
}
.modal_banner {
    position: sticky;
    padding: 1rem;
    top: 0;
    margin: 0 -1rem 1rem;
    background: var(--white);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    border-bottom: 1px dashed var(--gray);
}
.close_modal{
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
}
.close_modal:after{
    content: attr(data-before);
    display: inline-block;
    padding-left: .5rem;
}
.close_modal svg{
    height: 1.5rem;
    width: 1.5rem;
    fill: var(--gray);
}
#calculator_modal.modal .guts{
    padding-bottom: 0;
}

/* Tabs */
.tabs{
    padding: .5rem 0;
}
.tabs ul{
    display: grid;
    grid-auto-columns: minmax(0, 1fr);
    grid-auto-flow: column;
    width: 100%;
}

.tabs li {
    height: 2.5rem;
    padding: 0 .5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    font-weight: 500;
    font-size: .75rem;
    cursor: pointer;
}
.tabs li.active {
    background-color: var(--blue);
    color: var(--white);
    position: relative;
}
.tabs li.active:after {
	bottom: 0;
	left: 50%;
	border: solid transparent;
	content: "";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-bottom-color: var(--main_blue);
	border-width: .5rem;
	margin-left: -.5rem;
}
.tabbed:not(.active){
    display: none;
}
.modal .terms h3 {
    font-size: 1rem;
}
.modal .terms strong {
    font-weight: 600;
}
.modal .terms :is(p,ul) {
    padding-bottom: 1rem;
}

/* Menu */
.overlay, .overlay_compare{
    background: rgba(0,0,0,0);
    position: fixed;
    top: 0;
    left: 0;
    right: 100%;
    bottom: 0;
    z-index: 100;
    transition: background .2s ease;
    display: none;
}
.overlay.show, .overlay_compare.show{
    right: 0;
    display: block;
    background: rgba(0,0,0,.5);
}
.overlay:before, .overlay_compare:before{
    content: '\00D7';
    position: fixed;
    top: 1rem;
    right: 18.15rem;
    color: var(--white);
    font-size: 2rem;
    line-height: 1;
    transition: opacity .2s ease-in;
    opacity: 0;
    background: rgba(255, 255, 255, .25);
    display: inline-flex;
    width: 1.5rem;
    height: 1.5rem;
    align-items: center;
    justify-content: center;
    border-radius: .25rem;
}
.overlay.show:before, .overlay_compare.show:before{
    opacity: 1;
    transition-delay: .1s;
    transition-property: opacity;
    transition: opacity .1s ease-out;
}
#menu_modal, #compare_modal {
    transition: all .5s ease;
    position: fixed;
    background: #FFF;
    top: 0;
    bottom: 0;
    right: -18rem;
    width: 18rem;
    z-index: 100;
}
#menu_modal.show, #compare_modal.show {
    right: 0 !important;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
}
.menu_header{
    padding: 1rem;
    color: var(--white);
    background-color: var(--header);
    line-height: 1.2;
    text-align: center;
}
.menu_header .avatar {
    background: var(--gray);
    display: inline-flex;
    width: 3rem;
    height: 3rem;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    border-radius: 3rem;
    margin-bottom: .5rem;
    color: var(--header);
}
.menu_header .name{
    font-weight: 300;
}
.menu_header .email {
    color: var(--gray);
    font-size: .85rem;
    word-break: break-all;
}
#menu_modal ul{
    padding: 1.5rem 1rem 1rem;
}
#menu_modal li {
    padding: 0 0 .5rem;
}
#menu_modal li a {
    color: #000;
    text-decoration: none;
    font-weight: 300;
}
#menu_modal li svg {
    height: 1rem;
    width: 1rem;
    vertical-align: middle;
    fill: var(--gray);
    margin-right: .5rem;
}
#menu_modal .button{
    margin: 0 1rem 1rem;
}

/* Header */
#header {
    background-color: var(--white);
    border-bottom: 1px solid var(--logo_blue);
    height: 4rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    box-sizing: border-box;
    position: fixed;
    inset: 0 0 auto 0;
    z-index: 2;
}
#header svg{
    height: 1.5rem;
    width: auto;
}
#header img{
    height: 1.5rem;
    width: auto;
}
@media only screen and (min-width: 24rem) {
    #header img{
        height: 1.75rem;
    }
}
.desktop_nav{
    display: none;
}
#header_nav{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
#header_nav{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 1rem;
    grid-row-gap: 0px;
    width: 6rem;
}
#header_nav div{
    cursor: pointer;
}
.notifications_nav{ 
    grid-area: 1 / 1 / 2 / 2;
    position: relative;
}
.favorites_nav{
    grid-area: 1 / 2 / 2 / 3;
    position: relative;
}
.menu{
    grid-area: 1 / 3 / 2 / 4;
}
#header_nav svg{
    fill: var(--button_blue);
    height: 1.5rem;
    width: auto;
}
:is(.notifications_nav:not([data-count="0"]),.favorites_nav:not([data-count="0"])):before{
    content: attr(data-count);
    position: absolute;
    right: -.25rem;
    top: -.25rem;
    background: var(--logo_red);
    color: var(--white);
    display: inline-flex;
    line-height: 1;
    font-size: .75rem;
    font-weight: 600;
    padding: .1rem .2rem;
    border-radius: .5rem;
    min-width: .5rem;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

/* Shell */
#shell { 
    height: 100vh;
    display: flex;
    flex-direction: column-reverse;
}
/* .bottom-align{
    z-index: 999;
    position: fixed;
    bottom :0rem;
} */
/* Aside */
#aside {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4rem;
    padding: 0;
    border-top: 1px solid var(--gray);
    background: var(--white);
    z-index: 2;
    position: fixed;
    inset: auto 0 0 0;
}
#aside ul{
   display: grid;
   grid-auto-columns: minmax(0, 1fr);
   grid-auto-flow: column;
   width: 100%;
}
#aside a {
    text-decoration: none;
    color: var(--dark_gray);
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: .65rem;
    padding: .25rem;
}
#aside svg{
    height: 1.5rem;
    width: auto;
    fill: var(--dark_gray);
}
#aside li.active :is(a,svg){
    fill: var(--button_blue);
    color: var(--button_blue);
}

/* Main */
#main{
    min-height: 100vh;
    border-top: 1.5rem solid var(--main_blue);
    padding: 4rem 1rem 5rem;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
    background: var(--main_blue);
    width: 100%;
}

/* Dashboard */
#dashboard{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    padding: 1rem 0;
}
#dashboard + h1:not(:first-of-type){
    padding-top: 2rem;
}
.dash_item{
    border: 1px solid var(--gray);
    padding: 1rem;
    border-radius: .5rem;
    overflow: hidden;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.dash_item h3 {
    border-radius: .5rem;
    fill: var(--white);
    color: var(--white);
    background: var(--logo_blue);
    margin: -1rem -1rem 1rem;
    border-radius: .25rem;
    padding: .5rem;
    display: flex;
    align-items: center;
    font-weight: 200;
    font-size: 1rem;
}
.dash_item h3 svg{
    fill: var(--main_blue);
    height: 1.5rem;
    width: 1.5rem;
    margin-right: .5rem;
}
.dash_item p{
    margin: 0 0 1rem;
}
.dash_item .learn_more{
    text-align: right;
}
@media only screen and (max-width: 47.98rem) {
    .dash_item:has(h3:not(.show)) {
        padding-bottom: 0;
    }
    .dash_item h3{
        position: relative;
        padding-right: 2rem;
        cursor: pointer;
    }
    .dash_item h3:not(.show){
        margin-bottom: 0;
        background: var(--white);
        color: #000;
    }
    .dash_item h3:after{
        position: absolute;
        right: .5rem;
        font-size: 2rem;
        content: '-';
        font-weight: 500;
        color: var(--white);
    }
    .dash_item h3:not(.show):after{
        content: '+';
        color: var(--button_blue);
    }
    .dash_item:has(h3:not(.show)) :is(.dash_lower,.dash_content){
        overflow: hidden;
        height: 0;
    }

    .hide-sm{
        display: none;
    }
}
.articles{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
}
.articles .item{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.articles_banner{
    aspect-ratio: 2 / 1;
    display: block;
    overflow: hidden;
    background-image: var(--bg-image);
    background-size: cover;
    background-position: top center;
    border-bottom: 1px solid var(--border_blue);
}
.articles_title{
    display: block;
    margin: .5rem;
    padding: 0;
    color: var(--black);
    font-weight: 400;
    line-height: 1.2;
}
.articles_details{
    padding: 0 .5rem .5rem;
    font-size: .95rem;
    line-height: 1.2;
    margin-bottom: 1rem;
}
.articles_read_more {
    padding: 0 1rem 1rem;
    text-align: right;
}

.articles .item .articles_read_more{
    padding: 0 1rem 1rem;
}

/* Franchise */
.listings {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
}
.listings .item {
    position: relative;
}
.listing_banner{
    aspect-ratio: 2 / 1;
    display: block;
    overflow: hidden;
    background-image: var(--bg-image);
    /* background-size: cover;
    background-position: top center; */
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    border-bottom: 1px solid var(--border_blue);
    position: relative;
}
.listing_name{
    display: block;
    padding: .5rem;
    color: var(--black);
    font-weight: 600;
    line-height: 1;
    width: 80%;
}
.listing_options {
    padding: 0 0 .5rem .5rem;
}
.listing_options li {
    padding-right: .5rem;
    color: var(--logo_blue);
    font-size: .75rem;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}
.listing_options li.break{
    width: 100%;
}
.listing_options li.break i:not(:first-child) {
    margin-left: .25rem;
}
.listing_options li svg{
    height: 1.5rem;
    width: 1.5rem;
    fill: var(--gray);
    margin-right: .25rem;
}
.listing_details{
    padding: 0 .5rem 1rem;
    font-size: .95rem;
    line-height: 1.2;
}
.read_more {
    padding: 0 .5rem .5rem;
    text-align: right;
}
.listing_favorites {
    position: absolute;
    top: .5rem;
    left: .5rem;
    padding: .25rem;
    background: rgba(0,0,0,.5);
    line-height: 1;
    border-radius: .25rem;
    display: flex;
}
.listing_favorites input{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}
.listing_favorites:before{
    content: '';
    display: inline-block;
    height: 1.5rem;
    width: 1.5rem;
    -webkit-mask: url('data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
    mask: url('data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'); 
    -webkit-mask-size: contain;
    mask-size: contain;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: var(--white);
}
.listing_favorites:has(input:checked):before{
    background-color: var(--red);
    -webkit-mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
    mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'); 
    -webkit-mask-size: contain;
    mask-size: contain;
}
.listing_favorites.saved:before{
    content: '';
    display: inline-block;
    height: 1.5rem;
    width: 1.5rem;
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTYgMnYxNy41ODJsLTQtMy41MTItNCAzLjUxMnYtMTcuNTgyaDh6bTItMmgtMTJ2MjRsNi01LjI2OSA2IDUuMjY5di0yNHoiLz48L3N2Zz4=');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTYgMnYxNy41ODJsLTQtMy41MTItNCAzLjUxMnYtMTcuNTgyaDh6bTItMmgtMTJ2MjRsNi01LjI2OSA2IDUuMjY5di0yNHoiLz48L3N2Zz4='); 
    -webkit-mask-size: contain;
    mask-size: contain;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: var(--white);
}
.listing_favorites.saved:has(input:checked):before{
    background-color: var(--red);
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTggMjRsLTYtNS4yNjktNiA1LjI2OXYtMjRoMTJ2MjR6Ii8+PC9zdmc+');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTggMjRsLTYtNS4yNjktNiA1LjI2OXYtMjRoMTJ2MjR6Ii8+PC9zdmc+'); 
    -webkit-mask-size: contain;
    mask-size: contain;
}
#franchise_columns .listings .item{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
#franchise_filter{
    display: none;
}
#franchise_filtering h3{
    text-align: center;
    margin: -1rem -1rem 1rem;
    padding: 1rem;
    color: var(--white);
    background: var(--logo_blue);
    border-bottom: 1px solid var(--border_blue);
}
#franchise_filter label{
    font-weight: 500;
    font-size: .85rem;
}
#franchise_filter .row:not(.label_inline) label{
    padding-left: 1rem;
    margin-top: 1rem;
    display: block;
}
#franchise_filter .row:not(.label_inline) label input{
    display: inline-flex;
    width: fit-content;
}
#franchise_filter .row:not(:last-child){
    margin-bottom: .5rem;
}
.h1_counter {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin: 0;
}
.h1_counter span{
    font-size: 1rem;
    padding-top: .5rem;
    color: var(--logo_blue);
}
.h1_counter em{
    font-size: 1.25rem;
    color: var(--header);
}
#franchise_title {
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
}
#sort_by{
    min-width: 15rem;
}
.sort_by{
    display: none;
}
:is(#franchise_filtering,#franchise_filter2) button[type="submit"]:not([data-count="1"]):after{
    content: 's';
}
.franchise_count{
    padding: 0 .25rem;
}
:is(#franchise_filtering,#franchise_filter2) button[type="submit"][data-count="0"]{
    background-color: var(--gray);
    border-color: var(--gray);
    cursor: not-allowed;
}
#empty{
    padding: 1rem;
}
#franchises:not(:empty) + #empty,#articles_content:not(:empty) + #empty{
    display: none;
}
.filter_nw_btn{
    font-size: 0.75rem;
    width: auto;
    /* width: 100%; */
    /* background: var(--settings_blue); */
    /* border: none; */
    /* text-decoration: underline; */
    /* justify-content: left;
    color: var(--logo_blue); */
}

@media only screen and (max-width:767px) {
    .franchise_wrap{
        position: sticky;
        top: 7.2rem;
        margin-top: -1rem;
        z-index: 2;
        background: var(--main_blue);
        backdrop-filter: blur(1rem);
        padding: 0.5rem 0 1rem;
        max-width: 100%;
    }
    filter_nw_btn{
        margin-bottom: 1rem;
    }
}

/* Questions */
main#main:has(iframe){
    border: none;
    padding: 4rem 0 0;
    overflow: hidden;
    background: var(--white);
}
main#main iframe{
    border: none;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Favorites and Requests */
.other_listings {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
}
.other_listings .item {
    border: 1px solid var(--gray);
    box-sizing: border-box;
    background-color: var(--white);
    position: relative;
}
#other_title {
    border: 1px solid var(--gray);
    background-color: var(--white);
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}
#other_title h1{
    margin-bottom: 0;
}
#other_main[data-type="favorites"] .tabs{
    position: sticky;
    top: 0;
    z-index: 2;
    padding-top: 0 !important;

}

/* Profile */
#franchise_buttons{
    position: sticky;
    top: 4rem;
    margin-top: -1rem;
    z-index: 2;
    background: var(--main_blue);
    backdrop-filter: blur(1rem);
    padding: 1rem 0;
    max-width: 100%;
}
#franchise_buttons ul {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 100%;
}
#franchise_buttons li {
    display: flex;
    align-items: center;
    color: var(--logo_blue);
    cursor: pointer;
    background: var(--white);
    width: 40%;
    border-radius: .25rem;
    padding: .25rem 1rem;
    box-sizing: border-box;
    justify-content: center;
    border: 1px solid var(--logo_blue);
    font-weight: 400;
}
#franchise_buttons svg{
    fill: var(--gray);
    margin-right: .5rem;
    height: 1rem;
    width: 1rem;
}
#franchise_buttons li.open :is(span,svg){
    fill: var(--green);
    color: var(--green);
}
#listing_favorites {
    display: flex;
    align-items: center;
    justify-content: center;
}
#listing_favorites span{
    display: none;
}
#listing_favorites:has(.listing_favorites input:checked) span:before{
    content: 'Remove from';
}
#listing_favorites .listing_favorites {
    background: rgba(0, 0, 0, .25);
    position: relative;
    width: 2rem;
    height: 2rem;
    box-sizing: border-box;
    top: auto;
    right: auto;
    cursor: pointer;
}
#listing_favorites .listing_favorites input{
    margin: 0;
}

#profile_header{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
}
.profile_header_item:first-child { grid-area: 2 / 1 / 3 / 2; }
.profile_header_item:last-child { grid-area: 1 / 1 / 2 / 2; }
.profile_header_item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.profile_header_item.add_border{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--main_blue);
    min-height: 143px;
}
.profile_header_item.add_border:has(.profile_video){
    border-radius: 0 0 .5rem .5rem;
}
.profile_logo {
    margin-bottom: 1rem;
    text-align: center;
    box-sizing: border-box;
    background-color: var(--white);
    padding: .5rem;
    background: linear-gradient(to bottom,  var(--light_gray) 0%,var(--white) 100%); 
    border: 1px solid var(--light_gray);
    border-bottom: none;
}
.profile_logo img {
    display: block;
    margin: 0 auto;
    max-width: 50%;
    max-height: 7rem;
    padding: .5rem;
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    border-radius: .5rem;
}
#profile_header .profile_title{
    margin: 0;
}
.profile_banner{
    position: relative;
    min-height: 143px;
}
.slider {
    position: absolute;
    top: calc(50% - .75rem);
    background: rgba(0, 0, 0, .25);
    padding: .5rem 0;
    border-radius: .25rem;
}
.slider.left {
    left: .25rem;
}
.slider.right {
    right: .25rem;
}
.slider svg{
    height: 1.5rem;
    display: flex;
    fill: var(--white);
}
.slider.left svg{
    transform: rotate(180deg);
}
.profile_banner img{
    object-fit: cover;
    width: 100%;
    max-height: 100%;
    display: block;
}
.profile_video {
    text-align: center;
    width: 100%;
    box-sizing: border-box;
    padding: .5rem;
    border-top: 1px solid var(--border_blue);
}
.profile_video a{
    color: var(--white);
    background-color: var(--button_blue);
    border-radius: 1rem;
    display: inline-flex;
    height: 1.5rem;
    padding: 0 1rem;
    font-size: .85rem;
    font-weight: 500;
    text-decoration: none;
    align-items: center;
}
.profile_details{
    margin: .5rem 0 1rem;
    font-size: .9rem;
}
#profile_columns{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
    margin: .5rem 0 1rem;
}
.profile_column:not(:last-child){
    padding: 0 0 1rem;
}
.profile_column h2 {
    text-align: left;
    margin: -1rem -1rem 1rem;
    padding: 1rem;
    color: var(--white);
    background: var(--logo_blue);
    border-bottom: 1px solid var(--border_blue);
}
#profile_columns .profile_column:not(:first-child) h2{
    margin-top: 0;
    border-top: 1px solid var(--gray);
}
.profile_column p{
    margin-bottom: .5rem;
}
.profile_column table{
    width: 100%;
    font-weight: 500;
    font-size: .85rem;
    padding-top: .25rem;
}
.profile_column table tr td{
    border-bottom: 1px dashed var(--border_blue);
    padding-bottom: .25rem;
    padding-top: .25rem;
}
.profile_column table tr:first-child td{
    padding-top: 0;
}
.profile_column table tr:last-child td{
    padding-bottom: 0;
    border: none;
}
.profile_column .subtitle {
    font-weight: 400;
}
.profile_column .title {
    font-size: .75rem;
}
h2.profile_category {
    margin-top: .5rem;
}
.profile_column .value {
    padding-left: .5rem;
    color: var(--logo_blue);
}
.profile_learn_more{
    text-align: center;
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
}
.profile_learn_more h2{
    text-align: center;
}
.profile_learn_more p{
    margin-bottom: .5rem;
}
.profile_learn_more strong{
    font-weight: 500;
}
.profile_long_description{
    padding: 1rem 0 0;
}
.profile_long_description :is(strong,b){
    font-weight: 500;
}
.profile_long_description :is(em,u){
    font-style: italic;
}
.profile_long_description p{
    margin-bottom: 1rem;
}
.profile_long_description :is(ol,ul) {
    list-style-type: initial;
    margin: 0 0 1rem 1rem;
}
.profile_long_description :is(ol,ul) li:not(:last-child){
    padding-bottom: .5rem;
}
.profile_scrolltop {
    display: none;
    text-align: center;
    position: sticky;
    top: 4rem;
}
.profile_scrolltop span {
    border-radius: 1rem;
    display: inline-flex;
    height: 1.5rem;
    padding: 0 1rem;
    font-size: .85rem;
    font-weight: 500;
    background: rgba(0, 0, 0, .5);
    color: var(--white);
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.profile_footer_button{
    text-align: center;
    position: sticky;
    bottom: 0;
    box-shadow: 0 0 1rem rgba(255, 255, 255, .75);
    background-color: rgba(255, 255, 255, .75);
    -webkit-backdrop-filter: blur(.625rem);
    backdrop-filter: blur(.15rem);
    padding: 1rem 0;
    margin: 0 -1rem -1rem;
}

/* Notifications */
#notifications:not(.popped){
    margin: 0 -1rem;
}
#notifications li{
    display: flex;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid var(--gray);
    margin-bottom: .5rem;
}
#notifications ul li:last-child{
    border-bottom: 0px;
    margin-bottom: 0rem;
}
.notification_image{
    width: 3rem;
}
.notification_image svg{
    width: 2.5rem;
    height: 2.5rem;
    fill: var(--blue);
}
.notification_text{
    width: calc(100% - 5rem);
    font-size: .85rem;
}
.notification_text strong{
    font-weight: 700;

}
.notification_text p{
    line-height: 1.2;
    font-size: .85rem;
}
.notification_toggle svg{
    width: 1.5rem;
    height: 1.5rem;
    fill: var(--gray);
    transform: scaleX(-1);
}
#notification_popup .message :is(h1,h2,h3){
    text-align: left;
}
#notification_popup .message :is(strong,b){
    font-weight: 500;
}
#notification_popup .message :is(em,u){
    font-style: italic;
}
#notification_popup .message p{
    margin-bottom: 1rem;
}
#notification_popup .message :is(ol,ul) {
    list-style-type: initial;
    margin: 0 0 1rem 1rem;
}
#notification_popup .message :is(ol,ul) li:not(:last-child){
    padding-bottom: .5rem;
}
.notification_toggle {
    padding-right: .5rem;
}

/* Articles & Resources */
#resource_tabs.tabs{
    position: sticky;
    top: 4rem;
    z-index: 2;
    padding-top: 1rem;
    background: var(--main_blue);
    margin-top: -1rem;

}
#latest_article{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
}
.latest_banner{
    aspect-ratio: 2 / 1;
    display: block;
    overflow: hidden;
    background-image: var(--bg-image);
    background-size: cover;
    background-position: top center;
    border-bottom: 1px solid var(--border_blue);
}
.latest_title {
    display: block;
    padding: .5rem;
    color: var(--black);
    font-weight: 400;
    line-height: 1;
}
.latest_details {
    padding: 0 .5rem .5rem;
    font-size: .95rem;
    line-height: 1.2;
}
.article_banner {
    aspect-ratio: 2 / 1;
    display: block;
    overflow: hidden;
    background-image: var(--bg-image);
    background-size: cover;
    background-position: top center;
    margin-bottom: 1rem;
}
article.article :is(h1,h2,h3){
    text-align: left;
}
article.article :is(strong,b){
    font-weight: 500;
}
article.article :is(em,u){
    font-style: italic;
}
article.article p{
    margin-bottom: 1rem;
}
article.article :is(ol,ul) {
    list-style-type: initial;
    margin: 0 0 1rem 1rem;
}
article.article :is(ol,ul) li:not(:last-child){
    padding-bottom: .5rem;
}

/* Loading */
#loading{
    position: fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index: 999999;
    display: none;
}

#loading.show{
    display: block;
}

#loading div {
    width: 100%;
    height: 100%;
    display: flex !important;
    justify-content: center;
    align-items: center;
    background: rgba(51,60,78,.75);
}

@keyframes rotating {
    from{
        transform: rotate(0deg);
    }
    to{
        transform: rotate(360deg);
    }
}

#loading div svg{
    width: 80px;
    height: 80px;
    animation: rotating 2s linear infinite;
}

#article_modal table{
    border: 1px solid #A9B9D1;
    width: 100%;
    margin-bottom: 1rem;
}

#article_modal table td{
    padding: .8rem;
}

#article_modal table tr:first-child td{
    font-weight: 400;
}

#article_modal tbody tr:nth-of-type(even) {
    background-color: #e9effa !important;
}

.article_anchor{
    text-decoration: none;
    color: #000;
}

/* notifications modal */
#notification_tabs {
    padding: 0 0 1rem;
}
#notification_tabs ul li:not(.active){
    background-color: var(--main_blue);
}
#notification_tabs li.active::after{
    border-bottom-color: var(--white);
}
#notifications_modal:has(#notification_tabs li[data-id="1"].active) #notifications p{ 
    font-weight: 400;
}

/* Quiz Modal */
:is(#survey_modal,#quiz_modal)  .modal_banner{
    opacity: 0;
    pointer-events: none;
    display: none;
}

:is(#survey_modal,#quiz_modal)  .guts{
    position: relative;
}

:is(#survey_modal,#quiz_modal)  .guts .close_btn{
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    color: var(--white);
    font-size: 2rem;
    line-height: 1;
    background: var(--gray);
    display: inline-flex;
    width: 1.5rem;
    height: 1.5rem;
    align-items: center;
    justify-content: center;
    border-radius: .25rem;
    cursor: pointer;
}

.content #quiz_header{
    display: none !important;
}
.content .question_title{
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 1.5rem;
    padding-bottom: 0.5rem;
}

.content #quiz_main{
    margin-top: 1rem;
}

.content #quiz_main .quiz_btns{
    display: flex;
    gap: 1rem;
}

.content #quiz_main button {
    border-radius: .25rem;
    display: table;
    height: 2rem;
    padding: 0 1rem;
    color: var(--white);
    background-color: var(--button_blue);
    border: 2px solid var(--button_blue);
    /* background-color: var(--white);
    border: 2px solid var(--button_blue); */
    box-sizing: border-box;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 1rem;
    margin: 1rem auto;
    max-width: 15rem;
}

.content #quiz_main button.skip{
    background-color: var(--gray);
    border-color: var(--gray);
}

.content #progress span{
    background-color: var(--logo_red);
}
.content .checker input:checked + label {
    border: solid 1px var(--logo_blue);
    color: var(--logo_blue);
}

@media only screen and (max-width:992px) {
    .content #quiz_main{
        margin-top: 2rem;
    }

    :is(#survey_modal,#quiz_modal)  .guts .close_btn{
        top: 1rem;
        right: 0.5rem;
    }
}

@media only screen and (max-width:767px) {
    .content #quiz_main button{
        max-width: 8rem;
    }
}

.notification_image{
    padding: 0.5rem;
    color: var(--white);
    line-height: 1.2;
    text-align: center;
}
.avatar {
    background: #324972;
    display: inline-flex;
    width: 2.5rem;
    height: 2.5rem;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 0.5rem;
    margin-bottom: .5rem;
    color: var(--white);
}
.avatar.feedback_request{
    background-color: var(--logo_blue);
}
.avatar.welcome_series{
    background-color: var(--light_blue);
}
.avatar.education_series{
    background-color: var(--gray);
}
.notification_div, .compare_page{
    max-width: 64rem;
    margin: auto;
}
.notification_div #other_title{
    background-color: #374571 !important;
    border-radius: 0.25rem !important;
    position: sticky;
    top: 0;
    z-index: 2;
}
.notification_div .h1_counter em{
    color:#fff;
}
.notification_div li{
    background: #fff;
    border-radius: 0.25rem;
    padding: 0 0.5rem 0 0;
}
#notification_popup .notification_image{
    padding: 0 1rem 0 0;
}
#notification_popup .notification_text h4{
    font-size: 1rem;
    line-height: 1.2;
}
#notification_popup .notification_text h6{
    font-size: 0.7 !important;
}
#notification_popup .notification_image .avatar {
    width: 3rem;
    height: 3rem;
    font-size: 1.2rem;
}
.notification_text small{
    font-size: 0.7rem;
}

/* settings */
.settings_form{
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-gap: 1rem;
    padding: 0;
}

.settings_form .settings_item{
    padding: 1rem;
    border: 1px solid var(--border_blue);
}

.settings_form .settings_item:first-child{
    /* background: var(--main_blue); */
    background: var(--settings_blue);
}

.settings_item.settings_menu ul li{
    background: #fff;
    padding: 0.6rem 0.6rem 0.6rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border_blue);
    color: var(--blue);
    cursor: pointer;
}

.settings_item.settings_menu ul li.active{
    background: var(--blue);
    color: var(--white);
    font-weight: 500;
}

.settings_item .form_field{
    margin-bottom: 1rem;
}

.settings_item .form_field input.readonly{
    color: var(--gray);
    background-color: var(--white);
    border-style: dashed;
}

.settings_item .form_field label:not(label.switch){
    font-size: 0.75rem;
    padding: 0.25rem;
    font-weight: bold;
}

.settings_item .btn_field{
    text-align: right;
    padding-top: 1rem;
}

.settings_item .btn_field button{
    margin-left: 0.5rem;
}

.settings_toggle{
    float: right;
    transform: rotate(180deg);
    margin-top: -0.3rem;
}

.settings_item ul li.active .settings_toggle svg{
    fill: var(--white);
}

.settings_nt .row_item{
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings_nt .row_item input{
    width: 1.5rem;
    height: 1.5rem;
}

/*  */
.settings_nt .form_field p{
    margin-bottom: 0.5rem;
}

.settings_nt .switch {
    position: relative;
    display: inline-block;
    width: 55px;
    height: 30px;
}
  
.settings_nt .switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}
  
.settings_nt .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}
  
.settings_nt .slider::before {
    position: absolute;
    content: "";
    height: 23px;
    width: 23px;
    left: 4px;
    bottom: 4px;
    background-color: var(--white);
    -webkit-transition: .4s;
    transition: .4s;
}
  
.settings_nt input:checked + .slider {
    background-color: var(--logo_blue);
}
  
.settings_nt input:focus + .slider {
    box-shadow: 0 0 1px var(--logo_blue);
}
  
.settings_nt input:checked + .slider:before {
    -webkit-transform: translateX(23px);
    -ms-transform: translateX(23px);
    transform: translateX(23px);
}
  
/* Rounded sliders */
.settings_nt .slider.round {
    border-radius: 34px;
}
  
.settings_nt .slider.round::before {
    border-radius: 50%;
}

.password_note{
    background: var(--settings_blue);
    padding: 1rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    border: 1px solid var(--border_blue);
}

@media only screen and (max-width:992px) {
    .settings_menu{
        display: none;
    }

    .settings_form{
        grid-template-columns: 1fr;
    }

    .change_pw_title{
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
}

@media only screen and (max-width:767px) {
    .settings_form{
        padding: 0;
    }
}

/* Request Modal */
#request_popup .checkbox_button{
    height: auto;
}
#request_popup .checkbox_button span{
    line-height: 1.2;
    font-weight: 400;
}
#request_popup .checkbox_button input + span::before,#request_popup .checkbox_button input{
    top: 0;
}
#request_popup .checkbox_button input:checked + span::after{
    top: .2rem;
}
#request_popup p strong {
    font-weight: 500;
}
#request_popup .text-center{
    margin-top: 1rem;
}
#request_popup input[type="tel"] {
    width: 100%;
    box-sizing: border-box;
    border-radius: .25rem;
    border: 2px solid var(--gray);
    font-size: .85rem;
    height: 2rem;
    padding-left: .5rem;
}
#request_popup:not(:has(input[name="request_info"]:checked)) button.button.solid{
    background-color: var(--gray);
    border-color: var(--gray);
    cursor: not-allowed;
}
#request_popup input[type="tel"].error-outline {
    border-color: var(--red) !important;
}
#request_popup .error:not(:empty) {
    color: var(--red);
    font-size: .85rem;
    margin: 0.5rem 0.25rem 0;
    line-height: 1.5;
    font-weight: 400;
    display: block;
}

/* Help & Feedback */
/* .help_feedback_section form{
    max-width: 40rem;
    width: 100%;
    margin: auto;
    margin-bottom: 2rem;
} */

.help_feedback_section form .help_form_row{
    display: grid;
   grid-auto-columns: minmax(0, 1fr);
   grid-auto-flow: column;
   width: 100%;
   gap: 1rem;
}

#feedback_popup textarea{
    border: 2px solid var(--gray);
    width: 100%;
    box-sizing: border-box;
    border-radius: .25rem;
    padding: .5rem;
    font-size: 0.8rem;
}

h2.feedback_modal_h2{
    padding: 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 1.5rem;
    font-weight: bold;
}

#feedback_popup h2{
    margin-bottom: 1rem;
}

#feedback_popup button{
    width: auto;
}

/* Contact Information */
.contact_info {
    border: 1px solid var(--gray);
    padding: 1rem;
    border-radius: .5rem;
    overflow: hidden;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.contact_info:has(strong:not(.show)) {
    padding-bottom: 0;
}
.contact_info + .checkbox_button {
    margin-top: 1rem;
}
.contact_info strong {
    border-radius: .5rem;
    fill: var(--white);
    color: var(--white);
    background: var(--logo_blue);
    margin: -1rem -1rem 1rem;
    border-radius: .25rem;
    padding: .5rem;
    display: flex;
    align-items: center;
    font-weight: 200;
    font-size: 1rem;
    position: relative;
    padding-right: 2rem;
    cursor: pointer;
}
.contact_info strong:not(.show){
    margin-bottom: 0;
    background: var(--white);
    color: #000;
}
.contact_info strong:after {
    position: absolute;
    right: .5rem;
    font-size: 2rem;
    content: '-';
    font-weight: 500;
    color: var(--white);
}
.contact_info strong:not(.show):after {
    content: '+';
    color: var(--button_blue);
}
.contact_info:has(strong:not(.show)) :is(#contact_list) {
    overflow: hidden;
    height: 0;
}
#contact_list {
    font-size: .85rem;
}
#contact_list .title{
    font-weight: 400;
    padding-right: .5rem;
}
#contact_list .value{
    font-size: .75rem;
    word-break: break-all;
}
#contact_list p{
    margin: 1rem 0 0;
}

/* tooltip */
.tooltip {
    background: var(--logo_blue);
    border: 1px solid var(--border_blue);
    color: #fff;
    border-radius: 5px;
    padding: 0.5rem;
    z-index: 9;
    font-size: 0.8rem;
}

.tooltip ul{
    list-style: disc;
    padding-left: 1rem;
}

.tooltip strong{
    font-weight: bold;
}

/* REQUESTED */
.requested {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.request_time{
    padding: 0 0 1rem .5rem;
    color: var(--logo_blue);
    font-size: .75rem;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}

.question_lower_content:not(:empty) {
    font-weight: 300;
}
.answers .checker label {
    font-weight: 300;
}

/*  */
.compare_section{
    width: 100%;
    padding-bottom: 1.5rem;
}

.compare-flex{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.compare_container .compare_thumbnail{
    width: 7rem;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDUwIDUwIiBmaWxsPSIjN0Y5QUM3Ij4KPHBhdGggZD0iTSAyNSAyIEMgMTIuMzA5NTM0IDIgMiAxMi4zMDk1MzQgMiAyNSBDIDIgMzcuNjkwNDY2IDEyLjMwOTUzNCA0OCAyNSA0OCBDIDM3LjY5MDQ2NiA0OCA0OCAzNy42OTA0NjYgNDggMjUgQyA0OCAxMi4zMDk1MzQgMzcuNjkwNDY2IDIgMjUgMiB6IE0gMjUgNCBDIDM2LjYwOTUzNCA0IDQ2IDEzLjM5MDQ2NiA0NiAyNSBDIDQ2IDM2LjYwOTUzNCAzNi42MDk1MzQgNDYgMjUgNDYgQyAxMy4zOTA0NjYgNDYgNCAzNi42MDk1MzQgNCAyNSBDIDQgMTMuMzkwNDY2IDEzLjM5MDQ2NiA0IDI1IDQgeiBNIDM0Ljk4ODI4MSAxNC45ODgyODEgQSAxLjAwMDEgMS4wMDAxIDAgMCAwIDM0LjE3MTg3NSAxNS40Mzk0NTMgTCAyMy45NzA3MDMgMzAuNDc2NTYyIEwgMTYuNjc5Njg4IDIzLjcxMDkzOCBBIDEuMDAwMSAxLjAwMDEgMCAxIDAgMTUuMzIwMzEyIDI1LjE3NzczNCBMIDI0LjMxNjQwNiAzMy41MjUzOTEgTCAzNS44MjgxMjUgMTYuNTYwNTQ3IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAzNC45ODgyODEgMTQuOTg4MjgxIHoiPjwvcGF0aD4KPC9zdmc+");
    background-origin: content-box;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 30px;
    height: 4rem;
    border: 2px dashed var(--logo_blue);
    border-radius: 8px;
}

.compare_container .compare_thumbnail.has_image{
    background-color: var(--white);
    background-size: cover;
    border: none;
    position: relative;
}

.compare_container .compare_thumbnail.has_image svg{
    position: absolute;
    right: -8px;
    top: -8px;
    width: 20px;
    fill: #be0000;
    cursor: pointer;
}

.compare_section .btn_compare{
    background-color: var(--gray);
    border: none;
    color: var(--white);
    pointer-events: none;
}

#btn_compare.active{
    background-color: var(--logo_blue);
    pointer-events: all;
}


.icon_round_bg{
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    height: 30px;
    width: 30px;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--border_blue);
}

.compare_thumbnail .compare-icon {
    color: var(--border_blue);
    opacity: 0.5;
    border-width: 3px;
}

.results_tile_icon{
    background-color: var(--white);
    position: absolute;
    /* bottom: -1rem; */
    margin-top: -1rem;
    right: 0.5rem;
    height: 35px;
    width: 35px;
    cursor: pointer;
}

.results_tile_icon input{
    opacity: 0;
    position: absolute;
    top: 0;
    height: 30px;
    width: 30px;
    cursor: pointer;
}

.results_tile_icon:has(input:checked){
    background-color: var(--logo_blue);
}

.results_tile_icon input:checked + svg{
    fill: var(--white);
}

.more_modal {
    position: absolute;
    right: 0.5rem;
    /* height: 3rem; */
    /* width: 7rem; */
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 5px -3px rgba(0, 38, 77, .14), 0 8px 10px rgba(0, 38, 77, .1), 0 3px 14px 2px rgba(0, 38, 77, .08);
    color: #565d65;
    line-height: 1.5;
    outline: none;
    padding: 0.75rem;
    width: 200px;
    z-index: 1;
    margin-top: 1.5rem;
}

.more_modal .fa-retweet{
    font-size: 1.3rem;
    color: var(--gray);
    width: 24px;
    height: 24px;
}

.more_modal .cash::before{
    margin-right: 0;
}

.more_modal i.heart:before {
    -webkit-mask: url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    mask: url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    -webkit-mask-size: contain;
    mask-size: contain;
    content: '';
    display: inline-flex;
    height: 1.5rem;
    width: 1.5rem;
    background-color: var(--gray);
}

.more_modal i.heart.checked:before {
    background-color: var(--red);
    -webkit-mask: url(data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    mask: url(data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    -webkit-mask-size: contain;
    mask-size: contain;
}

.more_modal ul li{
    display: flex;
    gap: 0.25rem;
    line-height: 1;
    align-items: center;
    color: var(--text-color);
    font-weight: 400;
    font-size: 0.85rem;
    cursor: pointer;
}

.more_modal ul li:not(:last-child){
    padding-bottom: 0.5rem;
}

.modal_fav svg.checked{
    fill: #ca201b;
}


@media only screen and (max-width:1024px) {
    .compare_section.compare-flex{
        display: block;
    }

    .compare_section .compare_button{
        text-align: center;
    }

    .compare_section button{
        margin-top: 1rem;
    }
}

@media only screen and (max-width:767px) {
    .compare_container{
        /* display: inline-flex; */
        /* flex-wrap: nowrap; */
        overflow-x: scroll;
        justify-content: flex-start;
        padding-bottom: 0.5rem;
    }

    .compare_container .compare_thumbnail{
        flex: 0 0 80px;
    }

    .compare_container .compare_thumbnail.has_image svg{
        right: -5px;
        top: -3px;
        width: 15px;
    }

    .compare_section button{
        margin-top: 0.5rem;
    }

    .compare_section{
        padding-bottom: 0;
    }

    #main.main_compare{
        padding: 3.25rem 0.5rem 5rem;
    }
}

.sort_section{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

/*  */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    margin-left: 0.5rem;
}
  
.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}
  
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 34px;
}
  
.slider::before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 6px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
}
  
.switch input:checked + .slider {
    background-color: var(--logo_blue);
}
  
.switch input:focus + .slider {
    box-shadow: 0 0 1px var(--logo_blue);
}
  
.switch input:checked + .slider::before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

@media only screen and (max-width:992px) {
    .switch{
        width: 50px;
        height: 25px;
        margin: 0;
    }

    .slider::before{
        height: 20px;
        width: 20px;
        left: 0;
        bottom: 3px;
    }

    .switch input:checked + .slider::before {
        -webkit-transform: translateX(28px);
        -ms-transform: translateX(26px);
        transform: translateX(28px);
    }

    #franchise_title{
        flex-direction: row;
        justify-content: space-between;
    }
}

@media only screen and (max-width:767px) {
    .compare_by{
        display: flex;
        flex-direction: column;
        justify-content: end;
        border-left: 1px solid var(--light_gray);
        padding-left: 0.25rem;
    }

    .compare_by span{
        font-size: 0.8rem;
        margin-bottom: 0.1rem;
    }

    #franchise_title{
        margin-bottom: 0.8rem;
    }

    .slider::before{
        left: 2px;
        bottom: 2px;
    }

    .switch input:checked + .slider::before {
        -webkit-transform: translateX(24px);
        -ms-transform: translateX(26px);
        transform: translateX(24px);
    }
}


/*  */
.dashed{
    width: 100%;
    height: 1px;
    border-bottom: 1px dashed var(--button_blue);
    margin-bottom: 1rem;
}
.cmp_thumbnails_container{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.cmp_thumbnail, .cmp_thumbnail_img{
    width: 100%;
}

.cmp_thumbnail .cmp_thumbnail_item{
    border: 1px solid var(--dark_gray);
    border-radius: 6px;
    overflow: hidden;
}

.cmp_thumbnail .cmp_thumbnail_button{
    text-align: center;
    padding: 0.5rem;
}

.cmp_thumbnail_img{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
    overflow: hidden;
    position: relative;
}

.cmp_thumbnail_img img{
    width: 100%;
    height: 100%;
    /* display: flex;
    justify-content: center;
    align-items: center; */
    /* background-size: cover;
    background-position: center;
    background-repeat: no-repeat; */
}

.cmp_thumbnail_title{
    background-color: var(--logo_blue);
    color: var(--white);
    text-align: center;
    padding: 0.75rem;
}

.cmp_thumbnail_title h3{
    color: var(--white);
    margin-bottom: 0;
}

.cmp_thumbnail_title p{
    font-size: 0.8rem;
    font-weight: 400;
}

.cmp_thumbnail_change{
    background-color: var(--white);
    position: relative;
    width: 100%;
    /* padding: 0.5rem; */
}

.cmp_thumbnail_change input{
    position: absolute;
    width: 100%;
    height: 100%;
    margin: 0;
    opacity: 0;
    cursor: pointer;
}

.cmp_thumbnail_change .change_icon{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85rem;
    color: var(--button_blue);
    font-weight: 400;
    cursor: pointer;
    padding: 0.5rem;
}

.cmp_thumbnail_change .change_icon::before{
    /* content: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAACJklEQVR4nJVVTaiMYRR+/SwkP5FI0TDGvM/znO8bMjupWRC5VhZcSpGsbNBdsLzsble6+VmwkFKIyIKlslBKia2Un63kL7Ggrs4385lrZr6Zceos5nvf9znnOT/PhFBg9XJ5sQH7jDhvxB0T7ku8ZMCRJElWhGFN0lIJkyb+NPGVkRclnDTgeOv7UxG/jLxmZqsHgW004r3IJ2Zxc9G9GOMaCddFfDKLO3peSmNMTfwmYSKEMHs4Ntgv4jvJkX8OKpXKIhPfGnku/KeR3CvxS5quL//9aOQZI17sCWFOv8cJsKtX3Yy8LOFW9qNUKs1zql1pdz4Cjkr8TXKrZ+PutQwhBAArJf6oVatrs6gSP/TJbpaRZ02c7nQRn/NLEh9IOOHpjou8W5SZB/KODgaMYxJvZvx9eIsAveuNRmNuVmdxOgF2p0Dd3ccstOs4KvFxFyCATSS35e61adLRAikeyurUq8ZsA45LvNc+wKNe9Ex8Xq1WlxUziWNG3vA5GjHxo9PqB+hB6/X6/CJAEx4aeSwfm6/ebT+IMS5M03RJ7r4JvsP9tgczx6bF/7SIl3mWHU05WAQ0o35Xsg7n5llJfCNhatDjHmCjPj5dzQKQtMRhctAK5paQB1risLN3NLMNJrzLNE/aUpiV2Tqn2JQvbO8bNWuEMOFFNuK1q7SEU75WrkYinjUFFlfTtLJqGCaZuaQ1pQlTJtz2vwAjLyTk4Vqttrzo4R/VsMMd1HySSQAAAABJRU5ErkJggg=='); */
    content: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDIwMDEwOTA0Ly9FTiIKICJodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAxL1JFQy1TVkctMjAwMTA5MDQvRFREL3N2ZzEwLmR0ZCI+CjxzdmcgdmVyc2lvbj0iMS4wIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCA0NC4wMDAwMDAgNDQuMDAwMDAwIgogcHJlc2VydmVBc3BlY3RSYXRpbz0ieE1pZFlNaWQgbWVldCI+CjxtZXRhZGF0YT4KQ3JlYXRlZCBieSBwb3RyYWNlIDEuMTYsIHdyaXR0ZW4gYnkgUGV0ZXIgU2VsaW5nZXIgMjAwMS0yMDE5CjwvbWV0YWRhdGE+CjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMDAwMDAwLDQ0LjAwMDAwMCkgc2NhbGUoMC4xMDAwMDAsLTAuMTAwMDAwKSIKZmlsbD0iIzFhNzNlOCIgc3Ryb2tlPSJub25lIj4KPHBhdGggZD0iTTU3IDM4MiBjLTI2IC0yNiAtNDcgLTUyIC00NyAtNTggMCAtMjMgODQgLTEwNCAxMDcgLTEwNCAzMCAwIDMxIDMwCjIgNTYgLTIxIDE5IC0xOSAxOSAxNDIgMjQgMTU2IDUgMTY0IDYgMTY0IDI1IDAgMTkgLTggMjAgLTE2NCAyMyBsLTE2NCAzIDIzCjI0IGMyMyAyNSAyMiA1NSAtMyA1NSAtNyAwIC0zNCAtMjIgLTYwIC00OHoiLz4KPHBhdGggZD0iTTMwMCAyMDIgYzAgLTExIDkgLTI3IDIxIC0zOCAyMSAtMTkgMTkgLTE5IC0xNDIgLTI0IC0xNTYgLTUgLTE2NAotNiAtMTY0IC0yNSAwIC0xOSA4IC0yMCAxNjQgLTI1IDE2MSAtNSAxNjMgLTUgMTQyIC0yNCAtMjQgLTIyIC0yOCAtNTEgLTcKLTU5IDE0IC02IDExNiA5MCAxMTYgMTA5IDAgNiAtMTkgMzIgLTQyIDU3IC00NCA1MCAtODggNjQgLTg4IDI5eiIvPgo8L2c+Cjwvc3ZnPgo=');
    margin-bottom: -0.25rem;
    margin-right: 0.5rem;
}

.comaprision_table h3{
    text-align: left;
}

.comaprision_table .comparision_info{
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* gap: 1rem; */
}

.comparision_info > div{
    margin-bottom: 0.5rem;
}

.comaprision_table .comparision_info_label{
    width: 250px;
    font-weight: 500;
    font-size: 0.85rem;
}

.comaprision_table .comparision_info_value{
    flex: 1;
    background-color: var(--white);
    padding: 1rem;
    border: 1px solid var(--logo_blue);
    text-align: center;
    color: var(--logo_blue);
    font-weight: 600;
}

.comaprision_table .comparision_info_value:last-child{
    border-left: none;
}

#compare_modal .content{
    padding: 1rem;
}

.cmp_loader{
    background-color: #fff;
}
.cmp_load_title{
    width: 100%;
    height: 3.875rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background: #ececec;
}
.loading-main-text {
    height: 10px;
    width: 65%;
    margin-bottom: 10px;
    background: #ececec;
    background-image: -webkit-linear-gradient(left, #ececec 0px, #ddd 40px, #ececec 80px);
    background-image: -o-linear-gradient(left, #ececec 0px, #ddd 40px, #ececec 80px);
    background-image: linear-gradient(90deg, #ececec 0px, #ddd 40px, #ececec 80px);
    background-size: 250px;
    border-radius: 10px;
    -webkit-animation: shine-loading-container-items 2s infinite ease-out;
            animation: shine-loading-container-items 2s infinite ease-out;
}
.loading-sub-text {
    height: 10px;
    width: 50%;
    background: #ececec;
    background-image: -webkit-linear-gradient(left, #ececec 0px, #ddd 40px, #ececec 80px);
    background-image: -o-linear-gradient(left, #ececec 0px, #ddd 40px, #ececec 80px);
    background-image: linear-gradient(90deg, #ececec 0px, #ddd 40px, #ececec 80px);
    background-size: 250px;
    border-radius: 10px;
    -webkit-animation: shine-loading-container-items 2s infinite ease-out;
            animation: shine-loading-container-items 2s infinite ease-out;
}
.cmp_load_change{
    width: 100%;
    height: 2.375rem;
    display: flex;
    justify-content: center;
    align-items: center;
}
.cmp_load_img{
    width: 100%;
    height: 15.625rem;
    background: #ececec;
    background-image: -webkit-linear-gradient(left, #ececec 0px, #ddd 40px, #ececec 80px);
    background-image: -o-linear-gradient(left, #ececec 0px, #ddd 40px, #ececec 80px);
    background-image: linear-gradient(90deg, #ececec 0px, #ddd 40px, #ececec 80px);
    background-size: 250px;
    border-radius: 10px;
    -webkit-animation: shine-loading-container-items 2s infinite ease-out;
            animation: shine-loading-container-items 2s infinite ease-out;
}
@-webkit-keyframes shine-loading-image {
    0% {
      background-position: -32px;
    }
    40%, 100% {
      background-position: 208px;
    }
  }
  
  
  @keyframes shine-loading-image {
    0% {
      background-position: -32px;
    }
    40%, 100% {
      background-position: 208px;
    }
  }
  
  @-webkit-keyframes shine-loading-container-items {
    0% {
      background-position: -100px;
    }
    40%, 100% {
      background-position: 140px;
    }
  }
  
  @keyframes shine-loading-container-items {
    0% {
      background-position: -100px;
    }
    40%, 100% {
      background-position: 140px;
    }
  }

@media only screen and (max-width: 767px) {
    .comaprision_table .comparision_info{
        flex-wrap: wrap;
    }

    .comaprision_table .comparision_info_label{
        flex: 1 0 100%;
    }

    .cmp_thumbnail_title h3{
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    .cmp_thumbnail_title p{
        font-size: 0.65rem;
    }

    /* .cmp_thumbnails_container{
        gap: 0.5rem;
    } */

    .comaprision_table .comparision_info_value{
        font-size: 80%;
        padding: 1rem 0.5rem;
    }
}

/* compare modal */
.compare_change_content{
    margin-top: 1rem;
}

.compare_change_item{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--light_gray)
}

.compare_change_item .compare_change_img{
    width: 100%;
    height: 70px;
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    cursor: pointer;
}

.compare_change_item .compare_change_img .tick_bg{
    width: 50px;
    height: 30px;
    background: var(--logo_blue);
    position: absolute;
    top: -0.5rem;
    right: -1.2rem;
    transform: rotate(45deg);
    display: none;
}

.compare_change_img input[type="radio"]:checked + img + .tick_bg {
    display: inline-block;
}

.compare_change_img input[type="radio"]:checked ~ img {
    border: 1px solid var(--logo_blue);
}

.compare_change_item .compare_change_img .tick_bg i{
    position: absolute;
    top: 1rem;
    left: 1.3rem;
    color: #fff;
    font-size: 0.7rem;
    transform: rotate(-45deg);
}

.compare_change_item .compare_change_img img{
    width: 100%;
    height: 100%;
    border-radius: 6px;
}

.compare_change_img .change_list_ids{
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.compare_change_item.shadow{
    opacity: 0.5;
    pointer-events: none;
}

.compare_change_title{
    position: relative;
}

.compare_change_title .change_name{
    font-size: 0.85rem;
    line-height: 1.2;
    font-weight: 400;
    height: 35px;
    overflow: hidden;
    cursor: pointer;
}

.compare_change_btn{
    margin-top: 1rem;
    width: 100%;
    background-color: var(--logo_blue);
    color: var(--white);
    border-color: var(--logo_blue);
    padding: 1.2rem;
}

.compare_change_title .listing_favorites{
    position: relative;
    background: none;
    top: 0;
    left: 0;
    padding: 0;
}

.compare_change_title .listing_favorites:before{
    background-color: var(--dark_gray);
}

.compare_change_title .listing_favorites input{
    width: 24px;
    height: 24px;
}

.compare_change_title .compare_change_actions{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.5rem;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}

.compare_change_title .compare_change_actions i{
    font-size: 1.2rem;
    color: var(--dark_gray);
    cursor: pointer;
    display: inline-block;
}

.compare_change_title .compare_change_actions i.no_show {
    display: none;
}

:is(#min_net_worth,#min_net_worth2) a.ui-state-default:first-of-type{
    pointer-events: none;
    cursor: not-allowed;
    display: none;
}

.compare_page .go_back_search{
    display: none;
}
.compare_page.cmp_scroll .cmp_thumbnail_img{
    height: 0;
}
.compare_page.cmp_scroll .cmp_header_section{
    position: fixed;
    max-width: 64rem;
    width: 100%;
    background: #fff;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}
.compare_page.cmp_scroll .cmp_header{
    padding: 0.25rem;
}
.compare_page.cmp_scroll .dashed{
    margin-bottom: 20rem;
}

@media only screen and (max-width: 992px) {
    .cmp_thumbnail_img, .cmp_thumbnail_img img{
        height: auto;
    }
    
    .cmp_thumbnail_img img{
        width: 100%;
    }

    .compare_page.cmp_scroll .cmp_header_section{
        left: 0;
    }
    .compare_page.cmp_scroll .dashed{
        margin-bottom: 16rem;
    }
}

@media only screen and (max-width: 767px) {
    .cmp_thumbnails_container{
        gap: 0;
        position: fixed;
        inset: 4.5rem 0 auto 0;
        z-index: 2;
        left: 0;
        top: 4rem;
        width: 100%;
        background: #fff;
    }

    .cmp_thumbnail{
        padding: 0.25rem;
    }

    .comaprision_table{
        margin-top: 15.5rem;
    }

    .cmp_thumbnail_button button{
        font-size: 0.75rem;
        height: 1.75rem;
    }
}

@media only screen and (max-width: 385px) {
    .comaprision_table {
        margin-top: 15rem;
    }
}
/* net_worth_calculator */
#calculator_form{
    display: flex;
    justify-content: center;
}
.calculator_main{
    display: flex;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    gap:15px;
    padding: 20px 30px 30px 30px;
    background-color: white;
    border:1px solid var(--gray);
}
.asset_liab{
    display: flex;
    flex-direction: column;
    gap:20px
}

.asset_liab .label_inline{
    position: relative;
}

#calculator_asset,#calculator_liabilities{
    display: flex;
    width: 100%;
    flex-direction: column;
    gap:8px;
}
.calc_heading{
    padding-bottom: 0 !important;
    font-weight: 600;
    font-size: 18px;
}
.calc_heading span{
    margin-left: 44px !important;
}
.calculator_main .box label{
    font-weight: 500;
}     
.calc_result{
    position: relative;
    top: 22px;
} 
.calc_result p span{
    color: var(--button_blue);
    font-size: 1rem;
    font-weight: 500;
    margin-left: 20px;
}
.calc_result button{
    background-color: var(--button_blue);
    color: white;
    font-size:13.5px;
    width:100%;
    padding: 0 1rem;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 20px;
}
    
.net_worth_totals{
    position: sticky;
    padding: 1rem 2rem;
    bottom: 0;
    margin: 0 -1rem 0;
    background: var(--white);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    border-top: 1px dashed var(--gray);
}

.net_worth_totals p{
    padding-bottom: 0 !important;
    color: var(--logo_blue);
    font-weight: 600;
}

.net_worth_totals p span{
    color: var(--button_blue);
}

.net_worth_totals button{
    background-color: var(--button_blue);
    color: white;
    font-size: 13.5px;
    padding: 0 1rem;
    font-weight: 600;
}

.calc_intro{
    text-align: justify;
    text-align-last: left;
}

.compare_select + svg{
    padding: 8px;
}

.compare_select:checked + svg{
    fill: #fff;
}

@media only screen and (max-width:768px) {
    #calculator_modal.modal.show{
        z-index: 6;
    }

    .net_worth_totals{
        padding: 1rem;
    }
}

@media only screen and (max-width:767px) {
    .net_worth_totals{
        display: block;
        padding: 0.5rem;
    }

    .net_worth_totals p{
        padding-bottom: 0.5rem !important;
        font-size: 0.875rem;
    }

    .net_worth_totals button{
        width: 100%;
    }
}