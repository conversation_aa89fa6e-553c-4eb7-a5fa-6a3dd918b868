# STRONG HTACCESS PROTECTION
<Files ~ "^.*\.([Hh][Tt][Aa])">
 order allow,deny
 deny from all
</Files>
<IfModule mod_dir.c>
    DirectoryIndex index.php
</IfModule>
<IfModule pagespeed_module>
    ModPagespeed off
</IfModule>
<IfModule mod_rewrite.c>
# BASE RULES
Options +FollowSymlinks
RewriteEngine On
RewriteBase /

# ERROR PAGE
ErrorDocument 404 /404.php

# DISABLE DIRECTORY VIEWS
Options -Indexes

# REDIRECT
rewriteRule ^home$ / [R=301,NC,L,QSA]

# PAGES
RewriteRule ^(favorites|financing|franchise|notifications|questions|recent|requests|resources|saved|working)?$  /index.php?page=$1 [L,QSA]
RewriteRule ^(forgot_password|login|signup)?$  /login.php?section=$1&login_screens=yes [NC,L,QSA]
RewriteRule ^unsubscribe$  unsubscribe.php [NC,L,QSA]
RewriteCond %{REQUEST_FILENAME} !-s
RewriteRule ^resources/([^/]+)?$ /index.php?page=resources&article=$1 [L,QSA] 
RewriteCond %{REQUEST_FILENAME} !-s
RewriteRule ^franchise/([^/]+)?$ /index.php?page=franchise&profile=$1 [L,QSA] 
RewriteCond %{REQUEST_FILENAME} !-s
rewriteRule ^survey/([^/]+)/?$ /survey/quiz.php?url=$1 [NC,L,QSA]

# JSONS
RewriteRule ^(article|article_list|check_email_exists|concepts|cookie|forgot_password|login|notification|notification_list|profile|quizzes|info|signup|submission|survey|update_password|welcome_message|settings_form|change_password|update_user|zip|update_email_setting|delete_notification|feedback_form|feedback_save|quiz_session).json(.*)$  /json/$1.php [L,QSA]
RewriteRule ^all_favorites.json(.*)$  /json/activities.php?master_type=296,315 [L,QSA]
RewriteRule ^article_favorites.json(.*)$  /json/activities.php?master_type=315 [L,QSA]
RewriteRule ^favorites.json(.*)$  /json/activities.php?master_type=296 [L,QSA]
RewriteRule ^recent_search.json(.*)$  /json/activities.php?master_type=297 [L,QSA]
RewriteRule ^recently_viewed.json(.*)$  /json/activities.php?master_type=298 [L,QSA]
RewriteRule ^requested.json(.*)$  /json/activities.php?master_type=316 [L,QSA]
</IfModule>
