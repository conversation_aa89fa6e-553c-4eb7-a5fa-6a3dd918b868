<?php

    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
    $page = (isset($_GET['page']) && $_GET['page']) ? $_GET['page'] : 'home';
    if(!isset($_SESSION['myac_login_contact_id'])){
        header('Location: /dashboard/login');
        die;
    }

    // External Cookies
   login_external();
//    debug($_SESSION);
?>
<!doctype html>
<html lang="en">
    <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/head.php'); ?>
    <body data-page="<?php echo $page; ?>">
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/body.php'); ?>
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/header.php'); ?>
        <main id="main" class="main_<?php echo $_GET['page'] ?>"><?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/pages/'.$page.'.php'); ?></main>
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/aside.php'); ?>
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/modals.php'); ?>
        <?php if($page == 'franchise'){ include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/franchise_popups.php'); } ?>
        <script>
            var page = '<?php echo $page ?>';
            // var login_id = '<?php echo md5($_SESSION['myac_login_contact_id']); ?>';
            // Removing hashing on user_id for testing
            var login_id = '<?php echo $_SESSION['myac_login_contact_id']; ?>';
        </script>
        <?php echo franchise_filters($_POST); ?>
        <script type="text/javascript" src="/dashboard/js/multiselect-dropdown.js?random=<?php echo $random;?>"></script>
        <script type="text/javascript" src="/dashboard/js/global.js?random=<?php echo $random;?>"></script>
        <script>
            load_articles();
            <?php if(isset($_SESSION['welcome'])){
                echo 'localStorage.clear();';
            } ?>
            function welcomeModal() {
                fetch('/dashboard/unset.json', {
                    method: 'GET', 
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    credentials: 'same-origin'  // Include cookies for session management (if needed)
                })
                .then(response => response.text())
                .then(data => {
                    // Handle response from PHP (e.g., log it, show a message, etc.)
                    console.log(data);
                    // Close the modal window
                    modal('welcome_popup','close');
                    // GA4 - Account created
                    _mktz.push(['_Goal', 'account-created', 'registration now']);
                    ga4_global_events('account_created','registrationMethod','registration now');
                    ga4_global_events('user_login','user_status','logged_in');
                })
                .catch(error => console.error('Error:', error));
            }
            // GA4 - Logged In User
            <?php if(in_array($page,['home','franchise']) && !isset($_SESSION['welcome'])){ ?>
                ga4_global_events('user_login','user_status','logged_in');
            <?php } ?>

            // GA4 - Franchise Search
            <?php if($page == 'franchise'){ echo $gtm_data; } ?>
        </script>
        <script>
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({ event: 'beta_ga4' });
        </script>
    </body>
</html>
