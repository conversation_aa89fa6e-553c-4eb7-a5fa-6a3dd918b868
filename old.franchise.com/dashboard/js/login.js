$(function () {
    $('input[name="show_signin_password"]').change(function () {
        var temp_checked = $('input[name="show_signin_password"]:checked').length;
        if (temp_checked >= 1) {
            $('input[name="password"]').prop('type', 'text');
        } else {
            $('input[name="password"]').prop('type', 'password');
        }
    });

    // Bing default
    bing_defaults();
});

function checkLoginUser(email, callback) {
    var url = absolute_path + '/check_email_exists.json?email=' + email + '&type=login';
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data.status);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                callback(data);
            } else {
                callback(data);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function checkEmailExists(email, id, callback) {
    if (email) {
        if (id == 'signin') {
            var url = absolute_path + '/check_email_exists.json?email=' + encodeURIComponent(email) + '&type=login';
        } else {
            var url = absolute_path + '/check_email_exists.json?email=' + encodeURIComponent(email);
        }
        console.log(url);
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').classList.remove('show');
                if (data.status == 'error') {
                    // console.log(data.message);
                    document.getElementById('is_error').value = '';
                    document.querySelector('#' + id + ' input[name=email]').classList.remove('error-outline');
                    document.querySelector('#' + id + ' input[name=email]').nextElementSibling.classList.add('hide');
                }
                callback(data);
            })
            .catch(error => {
                document.getElementById('loading').classList.remove('show');
                console.error('Error:', error);
            });
    }
}

function login(email, password, callback) {
    var url = absolute_path + '/login.json?email=' + email + '&password=' + password;
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            if (data.status == 'success') {
                var timeNow = new Date().toLocaleString();
                setCookie('logged_in', timeNow, 365);
                callback(data.status);
            } else {
                document.getElementById('loading').classList.remove('show');
                callback(data.status);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function welcome_message() {
    var url = absolute_path + '/welcome_message.json';
    // document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.text())
        .then(data => {
            // console.log(data);
        })
        .catch(error => {
            // document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function signIn(step = null) {
    removeErrors('signin', ['email', 'password']);
    // document.querySelector('#signin input[name=email]').classList.remove('error-outline');
    // document.querySelector('#signin input[name=email]').nextElementSibling.classList.add('hide');
    // document.querySelector('#signin input[name=password]').classList.remove('error-outline');
    // document.querySelector('#signin input[name=password]').nextElementSibling.classList.add('hide');
    var email = document.querySelector('#signin input[name=email]').value;
    var password = document.querySelector('#signin input[name=password]').value;
    var is_error = document.querySelector('#signin input[name=is_error]').value;
    var openMessageCenter = document.querySelector('#signin input[name=open_message]').value;
    var login_page = document.querySelector('#signin input[name=login_page]').value;
    $query_url = '/franchise';
    if (openMessageCenter) {
        $query_url = '/?openMessageCenter=yes';
    } else if (login_page) {
        $query_url = login_page;
    }
    if (!email) {
        formFieldsValidation('#signin input[name=email]', 'Email is required.');
    } else if (!password) {
        formFieldsValidation('#signin input[name=password]', 'Password is required');
    } else {
        checkEmailExists(email, 'signin', function (data) {
            if (data.status == 'success') {
                login(email, password, function (status) {
                    if (status == 'success') {
                        document.getElementById('signin').action = absolute_path + $query_url;
                        document.getElementById('signin').submit();
                    } else {
                        // console.log(data.data[0].login_type); return;
                        if (data.data[0].login_type) {
                            var content = `<p>You've already logged in via ` + data.data[0].login_type + `. You can login via ` + data.data[0].login_type + ` again or you can <a href="/dashboard/forgot_password?forgot=password" class="">create a new password</a>.</p>
                            <p class="text-center"><a href="" class="button">Okay</a></p>`;
                            document.querySelector('#alert .content').innerHTML = content;
                            document.getElementById('alert').classList.add('show');
                        }
                        formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                    }
                });
            } else {
                formFieldsValidation('#signin input[name=email]', 'Couldn\'t find your account');
            }
        })

    }
}

function closeSignupCompleteModal() {
    modal('alert', 'close');
    window.location.href = absolute_path + '/login';
    return false;
}


function signUpUser(callback) {
    var url = absolute_path + '/signup.json';
    document.getElementById('loading').classList.add('show');
    var email = document.querySelector('#signup input[name=email]').value;
    var password = document.querySelector('#signup input[name=password]').value;
    var first_name = document.querySelector('#signup input[name=first_name]').value;
    var last_name = document.querySelector('#signup input[name=last_name]').value;
    var state = document.querySelector('#signup select[name=state]').value;
    var dataObj = {
        first_name: first_name,
        last_name: last_name,
        state: state,
        email: email,
        password: atob(password)
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataObj)
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                callback(data);
            } else {
                callback(data);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.log('Error:', error);
        });
}

function signUp(e, step = null) {
    e.preventDefault();
    if (step == 1) {
        var is_error = document.querySelector('#signup input[name=is_error]').value;
        removeErrors('signup', ['email', 'create_password', 'confirm_password']);
        var email = document.querySelector('#signup input[name=email]').value;
        var password = document.querySelector('#signup input[name=create_password]').value;
        var confirm_password = document.querySelector('#signup input[name=confirm_password]').value;
        var login_page = document.querySelector('#signup input[name=login_page]').value;
        var passwordValidate = validatePassword(password);
        if (!email) {
            formFieldsValidation('#signup input[name=email]', 'Email is required.');
        } else if (!password) {
            formFieldsValidation('#signup input[name=create_password]', 'Password is required');
        } else if (!confirm_password) {
            formFieldsValidation('#signup input[name=confirm_password]', 'Confirm Password is required');
        } else if (!passwordValidate) {
            formFieldsValidation('#signup input[name=create_password]', 'Password must be At least one uppercase letter, include at least one of the following characters <strong>!@$</strong> and At least 7 characters long');
        } else if (password != confirm_password) {
            formFieldsValidation('#signup input[name=confirm_password]', 'Password & Confirm Password not match');
        } else {
            // checkEmailExists(document.querySelector('#signup input[name=email]'),'signup');
            checkEmailExists(email, 'signup', function (data) {
                if (data.status == 'success') {
                    setCookie('beta_email', email);
                    setCookie('beta_password', btoa(password));
                    document.getElementById('signup').action = absolute_path + '/signup?signup=2';
                    document.getElementById('signup').submit();
                } else {
                    if(data.data == "blacklisted"){                        
                        document.getElementById('signup').action = '/';
                        document.getElementById('signup').submit();
                    }else{                        
                        formFieldsValidation('#signup input[name=email]',data.message);
                    }
                }
            });
        }
    } else if (step == 2) {
        removeErrors('signup2', ['first_name', 'last_name']);
        var first_name = document.querySelector('#signup input[name=first_name]').value;
        var last_name = document.querySelector('#signup input[name=last_name]').value;
        var state = document.querySelector('#signup select[name=state]').value;
        var login_page = document.querySelector('#signup input[name=login_page]').value;
        var url = '/dashboard/franchise';
        if (login_page) {
            url = login_page;
        }
        if (!first_name) {
            formFieldsValidation('#signup input[name=first_name]', 'First Name is required.');
        } else if (!last_name) {
            formFieldsValidation('#signup input[name=last_name]', 'Last Name is required.');
        } else if (!first_name && !last_name) {
            formFieldsValidation('#signup input[name=last_name]', 'First Name & Last Name is required.');
        } else if (!state) {
            formFieldsValidation('#signup select[name=state]', 'State is required.');
        } else {
            signUpUser(function (data) {
                // console.log(data);
                if (data.status == 'success') {
                    if (typeof dataLayer !== 'undefined') {
                        window.dataLayer.push({
                            'event': 'signup_button'
                        });
                    }
                    if (typeof fbq === "function") {
                        fbq('track', 'CompleteRegistration');
                    }
                    bing_event('signup');
                    // delete_cookie('beta_email');
                    delete_cookie('beta_password');
                    var content = `<h2>Thank You</h2>`;
                    content += `<p>Your account as been created! Click "Okay" to view your My Account dashboard.</p>
                    <p class="text-center"><a href="`+ url + `" class="button">Okay</a></p>`;
                    // content += `<p>Your account as been created, please check you mail and click on confirm link to login.</p>
                    // <p class="text-center"><button onclick="closeSignupCompleteModal();">Okay</button></p>`;
                    document.querySelector('#alert .content').innerHTML = content;
                    document.getElementById('alert').classList.add('show');
                    welcome_message();
                    return;
                } else {
                    document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                    document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                }
            });
        }
    }
}

window.addEventListener('DOMContentLoaded', function () {
    if (document.getElementById('signup')) {
        // if (document.querySelector('#signup input[name=first_name]')) {
        //     var first_name = getCookie('first_name');
        //     var last_name = getCookie('last_name');
        //     if (first_name) {
        //         document.querySelector('#signup input[name=first_name]').value = first_name;
        //     }
        //     if (last_name) {
        //         document.querySelector('#signup input[name=last_name]').value = last_name;
        //     }
        // }
        // if (document.querySelector('#signup select[name=state]')) {
        //     var state = getCookie('state');
        //     if (state) {
        //         document.querySelector('#signup select[name=state]').value = state;
        //     }
        // }
        if (document.querySelector('#signup input[name=email]')) {
            var email = getCookie('beta_email');
            if (email) {
                document.querySelector('#signup input[name=email]').value = email;
            }
        }
    }
    if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
    }
});

function forgetPassword() {
    var email = document.querySelector('#forgot_password input[name=email]').value;
    if (!email) {
        formFieldsValidation('#forgot_password input[name=email]', 'Email is required.');
        return;
    }
    document.getElementById('loading').classList.add('show');
    var url = absolute_path + '/forgot_password.json?email=' + email;
    console.log(url);
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                var content = `<h2>Password Reset Mail Sent</h2>
            <p>Password reset mail has been sent to your mail address, please check and click on the reset link to reset your password..</p>
            <p class="text-center"><button onclick="closeSignupCompleteModal();">Okay</button></p>`;
                document.querySelector('#alert .content').innerHTML = content;
                document.getElementById('alert').classList.add('show');
            } else {
                formFieldsValidation('#forgot_password input[name=email]', data.message);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function updatePassword() {
    removeErrors('update_password', ['update_password', 'update_password2']);

    var email = document.querySelector('#update_password input[name=email]').value;
    var token = document.querySelector('#update_password input[name=token]').value;
    var password = document.querySelector('#update_password input[name=update_password]').value;
    var confirm_password = document.querySelector('#update_password input[name=update_password2]').value;
    var passwordValidate = validatePassword(password);
    if (!password) {
        formFieldsValidation('#update_password input[name=update_password]', 'Password is required');
    } else if (!confirm_password) {
        formFieldsValidation('#update_password input[name=update_password2]', 'Confirm Password is required');
    } else if (!passwordValidate) {
        formFieldsValidation('#update_password input[name=update_password]', 'Password must be At least one uppercase letter, include at least one of the following characters <strong>!@$</strong> and At least 7 characters long');
    } else if (password != confirm_password) {
        formFieldsValidation('#update_password input[name=update_password2]', 'Password & Confirm Password not match');
    } else {
        document.getElementById('loading').classList.add('show');
        var url = absolute_path + '/update_password.json';
        var dataObj = {
            email: email,
            password: password,
            token: token
        }
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataObj)
        })
            .then(response => response.json())
            .then(data => {
                // console.log(data);
                document.getElementById('loading').classList.remove('show');
                if (data.status == 'success') {
                    var content = `<h2>Password Reset Successful</h2>
                <p>Your password has been updated successfully, please click on below Login button to go to login page.</p>
                <p class="text-center"><button onclick="closeSignupCompleteModal();">Login</button></p>`;
                    document.querySelector('#alert .content').innerHTML = content;
                    document.getElementById('alert').classList.add('show');
                } else {
                    formFieldsValidation('#update_password input[name=update_password2]', data.message);
                }
            })
            .catch(error => {
                document.getElementById('loading').classList.remove('show');
                console.error('Error:', error);
            });
    }
}

document.addEventListener('DOMContentLoaded', (event) => {
    const form = document.querySelector('.login_forms');
    form.addEventListener('keypress', function (event) {
        // Check if the pressed key is Enter
        if (event.key === 'Enter') {
            event.preventDefault(); // Prevent the form submission
        }
    });
});

//Bing Defaults
function bing_defaults() {
    window.uetq = window.uetq || [];
    if (typeof uetq !== 'undefined') {
        window.uetq.push('set', {
            'pid': {
                'em': getCookie('email') ? getCookie('email') : '',
                'ph': getCookie('phone') ? getCookie('phone') : ''
            }
        });
    }
}

//Bing Events (single item)
function bing_event(event, price = null) {
    if (typeof uetq !== 'undefined') {
        if (event == 'purchase') {
            window.uetq.push('event', '', { 'revenue_value': price, 'currency': 'USD' });
        } else {
            window.uetq.push('event', event, {});

        }
    }
}
function checkEmailExistsForSocialLogin(email, id, callback) {
    if (email) {
        var url = absolute_path + '/check_email_exists.json?email=' + encodeURIComponent(email) + '&type=login';
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').classList.remove('show');
                if (data.status == 'error') {
                    // console.log(data.message);
                    document.getElementById('is_error').value = '';
                    document.querySelector('#' + id + ' input[name=email]').classList.remove('error-outline');
                    document.querySelector('#' + id + ' input[name=email]').nextElementSibling.classList.add('hide');
                }
                callback(data);
            })
            .catch(error => {
                document.getElementById('loading').classList.remove('show');
                console.error('Error:', error);
            });
    } else {
        // console.log(email,'email');
        formFieldsValidation('#' + id + ' input[name=email]', 'Invalid email address');
    }

}
// Callback function that will be triggered when the user successfully signs in
function onSignIn(response) {
    // Ensure response.credential is available
    // console.log(response);
    var formId = document.getElementById('g_id_signin').getAttribute('data-id');
    if (response.credential) {
        const idToken = response.credential; // The ID token
        // console.log("ID Token:", idToken);
        var googleUrl = absolute_path + '/google_login.json?idToken=' + idToken;
        // Send the ID token to your server for validation
        // console.log(googleUrl,'url');
        fetch(googleUrl, {
            method: 'GET',
            // body: JSON.stringify({ 'idToken': idToken }),
            // data : {'idToken':idToken},
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                // Check if the response contains an error or success message
                if (data.error) {
                    // console.error('Error during authentication:', data.error);
                    alert('Authentication failed: ' + data.error);
                } else {
                    // console.log('User authenticated:', data.user);
                    // Do something with the authenticated user's information
                    let email = data.user.email,
                        first_name = data.user.given_name,
                        last_name = data.user.family_name,
                        password = '!Test1234',
                        login_type = 'google';
                    $query_url = '/franchise';
                    checkEmailExistsForSocialLogin(email, formId, function (data) {
                        // alert(data.status);
                        if (data.status == 'success') {
                            socialLogin(email, password, login_type, function (status) {
                                if (status == 'success') {
                                    document.getElementById(formId).action = absolute_path + $query_url;
                                    document.getElementById(formId).submit();
                                } else {
                                    formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                                }
                            });
                        } else {
                            signUpUserSocialLogin(email, first_name, last_name, login_type, function (data) {
                                // console.log(data);
                                var url = '/dashboard/franchise';
                                if (data.status == 'success') {
                                    if (typeof dataLayer !== 'undefined') {
                                        window.dataLayer.push({
                                            'event': 'signup_button'
                                        });
                                    }
                                    if (typeof fbq === "function") {
                                        fbq('track', 'CompleteRegistration');
                                    }
                                    bing_event('signup');
                                    // delete_cookie('beta_email');
                                    delete_cookie('beta_password');
                                    var content = `<h2>Thank You</h2>`;
                                    content += `<p>Your account as been created! Click "Okay" to view your My Account dashboard.</p>
                                <p class="text-center"><a href="`+ url + `" class="button">Okay</a></p>`;
                                    // content += `<p>Your account as been created, please check you mail and click on confirm link to login.</p>
                                    // <p class="text-center"><button onclick="closeSignupCompleteModal();">Okay</button></p>`;
                                    document.querySelector('#alert .content').innerHTML = content;
                                    document.getElementById('alert').classList.add('show');
                                    welcome_message();
                                } else {
                                    document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                                    document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                                }
                            });
                            // formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                        }
                    })
                }
                console.log("Authentication successful:", data);
                // Handle server response (e.g., store user data or session)
            })
            .catch(error => {
                console.error('Network or other error:', error);
                alert('An error occurred: ' + error.message);
                console.error("Error during authentication:", error);
            });
    } else {
        console.error("ID Token not available in response.");
    }
}
function signUpUserSocialLogin(email, first_name, last_name, login_type = null, callback) {
    var url = absolute_path + '/signup.json';
    if (login_type == 'google') {
        url += '?login_type=google';
    }
    if (login_type == 'apple') {
        url += '?login_type=apple';
    }
    if (login_type == 'linkedin') {
        url += '?login_type=linkedin';
    }
    document.getElementById('loading').classList.add('show');
    var dataObj = {
        first_name: first_name,
        last_name: last_name,
        email: email,
        password: '!Test1234',
        login_type: login_type
    }
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataObj)
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                callback(data);
            } else {
                callback(data);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.log('Error:', error);
        });
}
function socialLogin(email, password, login_type = null, callback) {
    // console.log(callback);
    var url = absolute_path + '/login.json?email=' + email + '&password=' + password;
    if (login_type == 'google') {
        url += '&login_type=google';
    }
    if (login_type == 'apple') {
        url += '&login_type=apple';
    }
    if (login_type == 'linkedin') {
        url += '&login_type=linkedin';
    }
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            if (data.status == 'success') {
                var timeNow = new Date().toLocaleString();
                setCookie('logged_in', timeNow, 365);
                callback(data.status);
            } else {
                document.getElementById('loading').classList.remove('show');
                callback(data.status);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}
// Initialize Apple ID Auth only on button click
document.addEventListener("DOMContentLoaded", function () {
    if (typeof AppleID !== "undefined") {
        AppleID.auth.init({
            clientId: 'com.franchise.beta',  // Your app's client ID (Services ID)
            scope: 'name email',             // Specify the scope for the data you need
            redirectURI: absolute_path + '/callbackApple',  // The redirect URI after successful login
            state: 'state',
            usePopup: true                   // Use a popup for login flow
        });

        // Attach event listener to the button to trigger the Apple login popup on click
        if (document.getElementById('appleid-signin')) {
            document.getElementById('appleid-signin').addEventListener('click', function () {
                const data = AppleID.auth.signIn();  // This will open the popup when the button is clicked
            });
        }

        // Listen for authorization success.
        document.addEventListener('AppleIDSignInOnSuccess', (response) => {
            // Handle successful response.
            var formId = document.getElementById('appleid-signin').getAttribute('data-id');
            const authData = response.detail.authorization;
            const userData = response.detail.user;
            // console.log(authData);
            // console.log(userData,'data2');
            if (response && authData && userData) {
                // If response, authData, and userData exist
                let email = userData.email,
                    first_name = userData.name.firstName,
                    last_name = userData.name.lastName,
                    password = 'apple@123',
                    login_type = 'apple';
                $query_url = '/franchise';
                checkEmailExistsForSocialLogin(email, formId, function (data) {
                    // alert(data.status);
                    if (data.status == 'success') {
                        socialLogin(email, password, login_type, function (status) {
                            if (status == 'success') {
                                document.getElementById(formId).action = absolute_path + $query_url;
                                document.getElementById(formId).submit();
                            } else {
                                formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                            }
                        });
                    } else {
                        // signUpUserSocialLogin(email, first_name, last_name, login_type, function (data) {
                        //     // console.log(data);
                        //     var url = dashboard + '/franchise';
                        //     if (data.status == 'success') {
                        //         if (typeof dataLayer !== 'undefined') {
                        //             window.dataLayer.push({
                        //                 'event': 'signup_button'
                        //             });
                        //         }
                        //         if (typeof fbq === "function") {
                        //             fbq('track', 'CompleteRegistration');
                        //         }
                        //         bing_event('signup');
                        //         // delete_cookie('beta_email');
                        //         delete_cookie('beta_password');
                        //         var content = `<h2>Thank You</h2>`;
                        //         content += `<p>Your account as been created! Click "Okay" to view your My Account dashboard.</p>
                        //                 <p class="text-center"><a href="`+ url + `" class="button">Okay</a></p>`;
                        //         // content += `<p>Your account as been created, please check you mail and click on confirm link to login.</p>
                        //         // <p class="text-center"><button onclick="closeSignupCompleteModal();">Okay</button></p>`;
                        //         document.querySelector('#alert .content').innerHTML = content;
                        //         document.getElementById('alert').classList.add('show');
                        //         welcome_message();
                        //     } else {
                        //         document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                        //         document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                        //     }
                        // });
                        formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                    }
                });
                // Proceed with further logic, such as saving the data or handling it
            } else if (response && authData) {
                // If response and authData exist
                const result = onSignInApple(authData);
                //  console.log(result);
                // Proceed with further logic, such as saving the data or handling it
            } else {
                console.log("No user data available.");
            }
        });

        // Listen for authorization failures.
        document.addEventListener('AppleIDSignInOnFailure', (event) => {
            // Handle error.
            console.log(event);
        });
    }
});

// $(document).ready(function() {
//     // Use MutationObserver to watch for iframe insertion into #g_id_signin
//     const observer = new MutationObserver(function(mutationsList, observer) {
//     // Check if iframe has been added inside the div
//     const iframe = $('#g_id_signin iframe');
//     if (iframe.length > 0) {
//         // Add a class to the parent div that contains the iframe
//         $('#g_id_signin div').addClass('iframe-container');

//         // Optionally add class to the iframe itself
//         iframe.addClass('g_id_iframe');
//         // Stop observing once iframe is added
//         observer.disconnect();
//     }
//     });
//     // Start observing the #g_id_signin div for changes to its child elements
//     observer.observe(document.getElementById('g_id_signin'), { childList: true });
// });
function onSignInApple(authData) {
    // Ensure response.credential is available
    var formId = document.getElementById('appleid-signin').getAttribute('data-id');
    if (authData.id_token) {
        const idToken = authData.id_token; // The ID token
        const authorizationCode = authData.code; // The authorizationCode token
        // console.log("ID Token:", idToken);
        var appleUrl = absolute_path + '/apple_login.json?idToken=' + idToken + '&authorizationCode=' + authorizationCode;
        // Send the ID token to your server for validation
        fetch(appleUrl, {
            method: 'GET',
            // body: JSON.stringify({ 'idToken': idToken,'authorizationCode':authorizationCode }),
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                // console.log(data); return;
                // Check if the response contains an error or success message
                if (data.error) {
                    console.error('Error during authentication:', data.error);
                    alert('Authentication failed: ' + data.error);
                } else {
                    console.log('User authenticated:', data.user);
                    // Do something with the authenticated user's information
                    let email = data.user.email,
                        first_name = data.user.firstName,
                        last_name = data.user.lastName,
                        password = '!Test1234',
                        login_type = 'apple';
                    $query_url = '/franchise';
                    checkEmailExistsForSocialLogin(email, formId, function (data) {
                        // alert(data.status);
                        if (data.status == 'success') {
                            socialLogin(email, password, login_type, function (status) {
                                if (status == 'success') {
                                    document.getElementById(formId).action = absolute_path + $query_url;
                                    document.getElementById(formId).submit();
                                } else {
                                    formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                                }
                            });
                        } else {
                            // signUpUserSocialLogin(email, first_name, last_name, login_type, function (data) {
                            //     // console.log(data);
                            //     var url = dashboard + '/franchise';
                            //     if (data.status == 'success') {
                            //         if (typeof dataLayer !== 'undefined') {
                            //             window.dataLayer.push({
                            //                 'event': 'signup_button'
                            //             });
                            //         }
                            //         if (typeof fbq === "function") {
                            //             fbq('track', 'CompleteRegistration');
                            //         }
                            //         bing_event('signup');
                            //         // delete_cookie('beta_email');
                            //         delete_cookie('beta_password');
                            //         var content = `<h2>Thank You</h2>`;
                            //         content += `<p>Your account as been created! Click "Okay" to view your My Account dashboard.</p>
                            //         <p class="text-center"><a href="`+ url + `" class="button">Okay</a></p>`;
                            //         // content += `<p>Your account as been created, please check you mail and click on confirm link to login.</p>
                            //         // <p class="text-center"><button onclick="closeSignupCompleteModal();">Okay</button></p>`;
                            //         document.querySelector('#alert .content').innerHTML = content;
                            //         document.getElementById('alert').classList.add('show');
                            //         welcome_message();
                            //     } else {
                            //         document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                            //         document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                            //     }
                            // });
                            formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                        }
                    })
                }
                console.log("Authentication successful:", data);
                // Handle server response (e.g., store user data or session)
            })
            .catch(error => {
                console.error('Network or other error:', error);
                alert('An error occurred: ' + error.message);
                console.error("Error during authentication:", error);
            });
    } else {
        console.error("ID Token not available in response.");
    }
}
//linkedin login
function openLinkedInPopup() {
    const clientId = '86md91w0p0gxqz'; // LinkedIn Client ID
    const redirectUri = absolute_path + '/callbackLinkedin'; // Your redirect URI
    const scope = 'openid profile email'; // Define the required scopes
    const state = 'randomString'; // Optional state for security
    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&state=${state}`;

    // Open LinkedIn login in a pop-up
    const width = 600;
    const height = 600;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;
    // Open the LinkedIn authorization popup
    const popup = window.open(authUrl, 'LinkedInLogin', `width=${width},height=${height},top=${top},left=${left}`);
    // Interval to check if the popup is closed or if the user has been redirected
    const interval = setInterval(function () {
        try {
            // Check if popup is closed
            if (popup.closed) {
                clearInterval(interval);
                return; // Exit if popup is closed
            }

            // Check if popup has redirected to the callback URL
            if (popup.location.href.indexOf(redirectUri) === 0) {
                clearInterval(interval);
                const urlParams = new URLSearchParams(popup.location.search);
                const code = urlParams.get('code');
                const error = urlParams.get('error');

                // If there's an error, show an alert
                if (error) {
                    alert('Error during LinkedIn login: ' + error);
                    popup.close();
                    return;
                }

                if (code) {
                    // Send the authorization code to the server to get the access token
                    // getLinkedInData(code,state);
                    var interval = setInterval(function () {
                        if (popup.closed) {
                            clearInterval(interval);
                            // getLinkedInData(); // Fetch LinkedIn data after popup closes
                            getLinkedInData(redirectUri, code, state);
                        }
                    }, 10);
                }

                // Close the popup after processing the response
                popup.close();
            }
        } catch (e) {
            // Catch any cross-origin errors here (e.g., accessing popup location)
        }
    }, 1000); // Check every 100ms
}

function getLinkedInData(redirectUri, code, state) {
    console.log(redirectUri, 'redirectUri');
    // Wait for the popup to be closed and get the authorization code

    var getUrl = redirectUri + '?code=' + encodeURIComponent(code) + '&state=' + encodeURIComponent('randomString1111');
    var formId = document.getElementById('g_id_signin').getAttribute('data-id');
    // Send an AJAX request to fetch user data from the server
    fetch(getUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    }).then(response => response.json())
        .then(data => {
            if (data.error) {
                // console.error('Error during authentication:', data.error);
                alert('Authentication failed: ' + data.error);
            } else {
                // console.log('User authenticated:', data.user);
                // Do something with the authenticated user's information
                let email = data.user.email,
                    first_name = data.user.firstName,
                    last_name = data.user.lastName,
                    password = '!Test1234',
                    login_type = 'linkedin',

                    $query_url = '/franchise';
                checkEmailExistsForSocialLogin(email, formId, function (data) {
                    // alert(data.status);
                    if (data.status == 'success') {
                        socialLogin(email, password, login_type, function (status) {
                            if (status == 'success') {
                                document.getElementById(formId).action = absolute_path + $query_url;
                                document.getElementById(formId).submit();
                            } else {
                                formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                            }
                        });
                    } else {
                        signUpUserSocialLogin(email, first_name, last_name, login_type, function (data) {
                            // console.log(data);
                            var url = '/dashboard/franchise';
                            if (data.status == 'success') {
                                if (typeof dataLayer !== 'undefined') {
                                    window.dataLayer.push({
                                        'event': 'signup_button'
                                    });
                                }
                                if (typeof fbq === "function") {
                                    fbq('track', 'CompleteRegistration');
                                }
                                bing_event('signup');
                                // delete_cookie('beta_email');
                                delete_cookie('beta_password');
                                var content = `<h2>Thank You</h2>`;
                                content += `<p>Your account as been created! Click "Okay" to view your My Account dashboard.</p>
                                    <p class="text-center"><a href="`+ url + `" class="button">Okay</a></p>`;
                                // content += `<p>Your account as been created, please check you mail and click on confirm link to login.</p>
                                // <p class="text-center"><button onclick="closeSignupCompleteModal();">Okay</button></p>`;
                                document.querySelector('#alert .content').innerHTML = content;
                                document.getElementById('alert').classList.add('show');
                                welcome_message();
                            } else {
                                document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                                document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                            }
                        });
                        // formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                    }
                })
            }
            console.log("Authentication successful:", data);
            // Handle server response (e.g., store user data or session)
        })
        .catch(error => {
            console.error('Network or other error:', error);
            alert('An error occurred: ' + error.message);
            console.error("Error during authentication:", error);
        });
}
// Event listener for the LinkedIn login button
// document.getElementById('linkedin-login-button').addEventListener('click', openLinkedInPopup);
