// Lazy Load Background Images
function lazyLoadBg(){
    var lazyBackgrounds = [].slice.call(document.querySelectorAll('.lazy-background'));
  
    if ("IntersectionObserver" in window) {
      let lazyBackgroundObserver = new IntersectionObserver(function(entries, observer) {
        entries.forEach(function(entry) {
          if (entry.isIntersecting) {
            let temp = entry.target.dataset.style;
            entry.target.setAttribute('style', temp);
            entry.target.classList.remove('.lazy-background');
            lazyBackgroundObserver.unobserve(entry.target);
          }
        });
      });
  
      lazyBackgrounds.forEach(function(lazyBackground) {
        lazyBackgroundObserver.observe(lazyBackground);
      });
    }
}

// On Document Load
document.addEventListener('DOMContentLoaded', function() {
    lazyLoadBg();
});

var absolute_path = 'https://www.franchise.com',
    title_append = ' | My Account',
    current_location = window.location.href;
const live_urls = ["myaccount","www"];
const new_live_urls = ["beta"];
if(live_urls.includes(location.hostname.split('.')[0]) == false){
    absolute_path = 'https://'+location.hostname.split('.')[0]+'.franchise.com';
}
if(new_live_urls.includes(location.hostname.split('.')[0]) == true){
    absolute_path = 'https://beta.franchise.com';
}
if (window.location.href.indexOf('beta') == -1) {
    absolute_path += '/dashboard';
}
global_cookie = cookie_var();

function loadActivityCookie(){
    var timeNow = new Date().toLocaleString();
    setCookie('last_activity',timeNow,7);
}

window.addEventListener("load",function() {
    if (page != 'franchise') {
        setTimeout(function(){
            window.scrollTo(0, 1);
          }, 0);
    }
});

function callSliders(){
    // Investment Slider
    if ($('#investment_range').length) {
        $( "#investment_range" ).slider({
            range: true,
            min: 0,
            max: 500000,
            step: 10000,
            values: [ min, max ],
            slide: function( event, ui ) {
                $( "#investment_amount" ).val( "$" + ui.values[ 0 ].toLocaleString() + " - $" + ui.values[ 1 ].toLocaleString() );
            },
            stop: function( event, ui ) {
                check_count('#franchise_filter');
                $( "#min" ).val(ui.values[ 0 ]);
                $( "#max" ).val(ui.values[ 1 ]);
            }
        });
        $( "#min" ).val($( "#investment_range" ).slider( "values", 0 ));
        $( "#max" ).val($( "#investment_range" ).slider( "values", 1 ));
        $( "#investment_amount" ).val( "$" + $( "#investment_range" ).slider( "values", 0 ).toLocaleString() + " - $" + $( "#investment_range" ).slider( "values", 1 ).toLocaleString() );
    };

    // Net Worth Slider
    if ($('#min_net_worth').length && typeof max_net_worth != 'undefined') {
        $( "#min_net_worth" ).slider({
            range: true,
            min: 0,
            max: max_net_worth,
            step: 1000,
            values: [ net_worth_min, net_worth_max ],
            slide: function( event, ui ) {
                // Prevent the left handle (index 0) from moving
                const leftValue = $("#min_net_worth").slider("values", 0); // Fixed left handle value
                if (ui.handle === $("#min_net_worth .ui-slider-handle:first")[0]) {
                    ui.values[0] = leftValue; // Lock the left handle value
                    return false; // Prevent interaction
                }
                $( "#net_worth_max" ).val(ui.values[ 1 ]);
                $( "#net_worth_amount" ).val( "$" + ui.values[ 0 ].toLocaleString() + " - $" + ui.values[ 1 ].toLocaleString() );
            },
            stop: function( event, ui ) {
                check_count('#franchise_filter');
                $( "#net_worth_min" ).val(ui.values[ 0 ]);
                $( "#net_worth_max" ).val(ui.values[ 1 ]);
            }
        });
        $( "#net_worth_min" ).val($( "#min_net_worth" ).slider( "values", 0 ));
        $( "#net_worth_max" ).val($( "#min_net_worth" ).slider( "values", 1 ));
        $( "#net_worth_amount" ).val( "$" + $( "#min_net_worth" ).slider( "values", 0 ).toLocaleString() + " - $" + $( "#min_net_worth" ).slider( "values", 1 ).toLocaleString() );
    };

    // Franchise Units Slider
    if ($('#franchise_units').length && typeof max_units != 'undefined') {
        $( "#franchise_units" ).slider({
            range: true,
            min: 0,
            max: max_units,
            step: 100,
            values: [ units_min, units_max ],
            slide: function( event, ui ) {
                $( "#units_max" ).val(ui.values[ 1 ]);
                $( "#franchise_units_amount" ).val( ui.values[ 0 ].toLocaleString() + " - " + ui.values[ 1 ].toLocaleString() );
            },
            stop: function( event, ui ) {
                check_count('#franchise_filter');
                $( "#units_min" ).val(ui.values[ 0 ]);
                $( "#units_max" ).val(ui.values[ 1 ]);
            }
        });
        $( "#units_min" ).val($( "#franchise_units" ).slider( "values", 0 ));
        $( "#units_max" ).val($( "#franchise_units" ).slider( "values", 1 ));
        $( "#franchise_units_amount" ).val($( "#franchise_units" ).slider( "values", 0 ).toLocaleString() + " - " + $( "#franchise_units" ).slider( "values", 1 ).toLocaleString() );
    };

    // Investment Slider
    if ($('#investment_range2').length) {
        $( "#investment_range2" ).slider({
            range: true,
            min: 0,
            max: 500000,
            step: 10000,
            values: [ min, max ],
            slide: function( event, ui ) {
                $( "#min2" ).val(ui.values[ 0 ]);
                $( "#max2" ).val(ui.values[ 1 ]);
                $( "#investment_amount2" ).val( "$" + ui.values[ 0 ].toLocaleString() + " - $" + ui.values[ 1 ].toLocaleString() );
            },
            stop: function( event, ui ) {
                check_count('#franchise_filter2');
                $( "#min2" ).val(ui.values[ 0 ]);
                $( "#max2" ).val(ui.values[ 1 ]);
            }
        });
        $( "#min2" ).val($( "#investment_range2" ).slider( "values", 0 ));
        $( "#max2" ).val($( "#investment_range2" ).slider( "values", 1 ));
        $( "#investment_amount2" ).val( "$" + $( "#investment_range2" ).slider( "values", 0 ).toLocaleString() + " - $" + $( "#investment_range2" ).slider( "values", 1 ).toLocaleString() );
    };

    if ($('#min_net_worth2').length && typeof max_net_worth != 'undefined') {
        $( "#min_net_worth2" ).slider({
            range: true,
            min: 0,
            max: max_net_worth,
            step: 1000,
            values: [ net_worth_min, net_worth_max ],
            slide: function( event, ui ) {
                const leftValue = $("#min_net_worth2").slider("values", 0); // Fixed left handle value
                if (ui.handle === $("#min_net_worth2 .ui-slider-handle:first")[0]) {
                    ui.values[0] = leftValue; // Lock the left handle value
                    return false; // Prevent interaction
                }
                $( "#net_worth_max2" ).val(ui.values[ 1 ]);
                $( "#net_worth_amount2" ).val( "$" + ui.values[ 0 ].toLocaleString() + " - $" + ui.values[ 1 ].toLocaleString() );
            },
            stop: function( event, ui ) {
                check_count('#franchise_filter');
                $( "#net_worth_min2" ).val(ui.values[ 0 ]);
                $( "#net_worth_max2" ).val(ui.values[ 1 ]);
            }
        });
        $( "#net_worth_min2" ).val($( "#min_net_worth2" ).slider( "values", 0 ));
        $( "#net_worth_max2" ).val($( "#min_net_worth2" ).slider( "values", 1 ));
        $( "#net_worth_amount2" ).val( "$" + $( "#min_net_worth2" ).slider( "values", 0 ).toLocaleString() + " - $" + $( "#min_net_worth2" ).slider( "values", 1 ).toLocaleString() );
    };

    // Franchise Units Slider
    if ($('#franchise_units2').length && typeof max_units != 'undefined') {
        $( "#franchise_units2" ).slider({
            range: true,
            min: 0,
            max: max_units,
            step: 100,
            values: [ units_min, units_max ],
            slide: function( event, ui ) {
                $( "#units_min2" ).val(ui.values[ 0 ]);
                $( "#units_max2" ).val(ui.values[ 1 ]);
                $( "#franchise_units_amount2" ).val( ui.values[ 0 ].toLocaleString() + " - " + ui.values[ 1 ].toLocaleString() );
            },
            stop: function( event, ui ) {
                check_count('#franchise_filter2');
                $( "#units_min2" ).val(ui.values[ 0 ]);
                $( "#units_max2" ).val(ui.values[ 1 ]);
            }
        });
        $( "#units_min2" ).val($( "#franchise_units2" ).slider( "values", 0 ));
        $( "#units_max2" ).val($( "#franchise_units2" ).slider( "values", 1 ));
        $( "#franchise_units_amount2" ).val($( "#franchise_units2" ).slider( "values", 0 ).toLocaleString() + " - " + $( "#franchise_units2" ).slider( "values", 1 ).toLocaleString() );
    };
}

$(function () {

    if (!['login','signup'].includes(page)) {
        // Load Favorites
        load_favorites();
        // Load Notification count
        loadNotificationCount(); 
    }
    
    //Bing Defaults
    bing_defaults();
    // Load Survey
    if (['home','franchise'].includes(page)) {
        load_survey('account-sign-up');
    }
    if (page == 'franchise') {
        setTimeout(()=>{
            loadCompareActivity();
        },100)
    }
    // Filter Search Results on Load
    sortTab('select');

    loadActivityCookie();

    if (page == 'compare-results') {
        get_compare_summary();
    }
    
    // Dashboard
    $(".dash_item h3").click(function () {
        if ($(this).hasClass('show')) {
            $(this).removeClass('show');
        } else {
            $('.dash_item h3').removeClass('show');
            $(this).addClass('show');
        }
    });

    // Contact Info
    $(document).on('click', '.contact_info strong', function () {
        if ($(this).hasClass('show')) {
            $(this).removeClass('show');
        } else {
            $('.contact_info strong').removeClass('show');
            $(this).addClass('show');
        }
    });

    function fetchArticlesList(master_types_id) {
        var url = absolute_path+'/article_list.json?master_types_id='+master_types_id;
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            document.getElementById("articles_content").innerHTML = data;
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
    }

    // Tabs
    $(".tabs:not(#notification_tabs) li").click(function () {
        if ($(this).hasClass('active')) {
            return;
        } else {
            var active_tab = $(this).attr('data-tab');
            $('.tabs li,.tabbed').removeClass('active');
            $(this).addClass('active');
            $('#'+active_tab+'.tabbed').addClass('active');
            if(['the_basics','financial','research'].includes(active_tab)){
                var master_types_id = $(this).attr('data-id');
                if (active_tab != 'unread' || active_tab != 'read') {
                    fetchArticlesList(master_types_id);
                }
                $('html').scrollTop(resource_distance);
            }
            if(['favorites_franchises','favorites_articles'].includes(active_tab)){
                var data_count = $('#'+active_tab+' .item').length,
                    plural = 's',
                    h1_em = 'Your Favorite Franchises';
                if(active_tab == 'favorites_articles'){
                    h1_em = 'Your Saved Articles';
                }
                if(data_count == 1){
                    plural = '';
                }
                $('.h1_counter').attr('data-count',data_count);
                var text = data_count.toLocaleString()+' result'+plural;
                $('.h1_counter span').text(text);
                $('.h1_counter em').text(h1_em);
            }
        }
    });

    // calling sliders function
    callSliders();

    // Check Resources Scroll to Top
    if ($('#resource_tabs').length) {
        var scrollTop     = $(window).scrollTop(),
            elementOffset = $('#resource_tabs').offset().top,
            resource_distance      = (elementOffset - scrollTop);
    }

    // Franchise Counts
    if ($('.h1_counter').length) {
        var data_count = $('.other_listings .item,.listings .item,.articles .item').length,
            plural = 's';
        if(data_count == 1){
            plural = '';
        }
        $('.h1_counter').attr('data-count',data_count);
        var text = data_count.toLocaleString()+' result'+plural;
        $('.h1_counter span').text(text);
    };

    $.fn.isInViewport = function() {
        var elementTop = $(this).offset().top;
        var elementBottom = elementTop + $(this).outerHeight();
        return elementBottom > 0;
    };
    
    $('#profile_modal .guts').on('resize scroll', function() {
        $('.profile_learn_more').each(function() {
            if ($(this).isInViewport()) {
                $('#profile_modal').removeClass('scrolled');
            } else {
                $('#profile_modal').addClass('scrolled');
            }
        });
    });

    $(document).on('click', '.profile_scrolltop span', function () {
        $('#profile_modal .guts').animate ({scrollTop:0}, 500);
    });

    $('input[name="show_signup_password"]').change(function() {
        var temp_checked = $('input[name="show_signup_password"]:checked').length; 
        if (temp_checked >= 1) {
            $('input[name="create_password"]').prop('type', 'text');
            $('input[name="confirm_password"]').prop('type', 'text');
        } else {
            $('input[name="create_password"]').prop('type', 'password');
            $('input[name="confirm_password"]').prop('type', 'password');
        }
    });

    $('input[name="show_update_password"]').change(function() {
        var temp_checked = $('input[name="show_update_password"]:checked').length;
        if (temp_checked >= 1) {
            $('input[name="update_password"]').prop('type', 'text');
            $('input[name="update_password2"]').prop('type', 'text');
        } else {
            $('input[name="update_password"]').prop('type', 'password');
            $('input[name="update_password2"]').prop('type', 'password');
        }
    });

    $(document).on('change', 'select#sort_by', function () {
        sortTab('select');
    });

    window.addEventListener("popstate", function (e) {
        console.log(current_location+' '+window.location.href);
        if(current_location == window.location.href){
            history.go(-1);
        }else{
            window.location.reload();
        }
    });
});

//Get Cookie
function getCookie(name) {
    var v = document.cookie.match('(^|;) ?' + name + '=([^;]*)(;|$)');
    return v ? v[2] : null;
}

//Set Cookie
function setCookie(key,value,days=1){
    var date = new Date();
    date.setTime(date.getTime()+(days*24*60*60*1000));
    var expires = "; expires="+date.toGMTString();
    document.cookie = key+"="+value+expires+"; path=/";
}

function delete_cookie(name) {
    document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

// Button Add/Remove Favorites
function favorites(fbo_id,article = false){
    var favorites = [],
        url = absolute_path+'/favorites.json?v='+fbo_id+'&t=';
    if (localStorage.favorites) {
        favorites = JSON.parse(localStorage.getItem('favorites'));
    }
    if(article){
        url = absolute_path+'/article_favorites.json?v='+fbo_id+'&t=';
        if (localStorage.article_favorites) {
            favorites = JSON.parse(localStorage.getItem('article_favorites'));
        }
    }
    if (favorites) {
        if(!favorites.includes(fbo_id)){
            url += 'add';
        }else{
            url += 'remove';
            if ($('#modal_fav_'+fbo_id+' i')) {
                $('#modal_fav_'+fbo_id+' i').removeClass('checked');
            }
        }
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            load_favorites();
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }else{
        console.error('Data not found or `favorites` field is missing in localStorage.');
    }
}

// Check Favorites on Load
function load_favorites(){
    var count = 0,
        favorites = [];
    var url = absolute_path+'/all_favorites.json';
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        favorites = data;
        if (favorites) {
            $('input.favorites').prop('checked', false);
            $('.modal_fav i').removeClass('checked');
            $('.modal_fav span').html('Add to Favorites');
            window.localStorage.setItem('favorites', JSON.stringify(favorites[296]));
            window.localStorage.setItem('article_favorites', JSON.stringify(favorites[315]));
            favorites[296].forEach(id => {
                $('input[data-fboid="'+id+'"]').prop('checked', true);
                $('#modal_fav_'+id+' i').addClass('checked');
                $('#modal_fav_'+id+' span').html('Remove from Favorites');
            });
            favorites[315].forEach(id => {
                $('input[data-articleid="'+id+'"]').prop('checked', true);
            });
            count = favorites.count;
            $('.favorites_nav').attr('data-count',count);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Alert Modal
function alert_modal(h3 = 'Warning', message = null, button = 'Okay') {
    var content = '<h3>' + h3 + '</h3>';
    content += '<p>' + message + '</p>';
    content += '<div class="text-center"><button class="button" onclick="modal(\'alert\',\'close\');"><span>' + button + '</span></button></div>';
    $('#alert').find('.content').html(content);
    modal('alert');
}

// Modals and Popups
function modal(id,action = null){
    menu_modal('close');
    if(action == 'close'){
        if (id == 'survey_modal') {
            closeSurveyModal();
        }
        if (id == 'filter_popup') {
            callSliders();
        }
        $('#'+id).removeClass('show');
        history.replaceState(null, null, ' ');
    }else{
        if(id == 'notifications_modal'){
            appendIdUrl('#'+page+'-Messaging+Center');
            openNotificationsModal(0);
            modal_backbutton('#notifications_modal');
        }else if(id == 'terms_modal'){
            appendIdUrl('#'+page+'-terms-conditions');
            modal_backbutton('#terms_modal');
        }else if(id == 'privacy_modal'){
            appendIdUrl('#'+page+'-privacy');
            modal_backbutton('#privacy_modal');
        }else if(id == 'feedback_popup'){
            appendIdUrl('#'+page+'-feedback');
            modal_backbutton('#feedback_popup');
        }
        $('#'+id).addClass('show');
        loadActivityCookie();
    }
    pushHistory();
}

// Quiz ONLY
function closeModal(id){
    window.localStorage.setItem('survey', '1');
    $('.modal').removeClass('show'); 
}

// Menu
function menu_modal(action = null){
    if(action == 'close'){
        $('.overlay,#menu_modal').removeClass('show');
    }else{
        $('.overlay,#menu_modal').addClass('show');
    }
}

function modalNotification(id,notification_id){
    var url = absolute_path+'/notification.json?id='+id+'&notification_id='+notification_id;
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
         console.log(data); 
        if(data.includes('fetchArticleModal('))
        {
            data = data.replaceAll('https://::', '');
        }
        document.getElementById('loading').classList.remove('show');
        $("#notification_"+notification_id).removeClass('bold');
        $("#notification_popup .content").html(data);
        $('#notification_popup').addClass('show');
        loadActivityCookie();
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function closeNotificationPopup() {
    $('#notification_popup').removeClass('show');
    loadNotificationCount();
    // openNotificationsModal();
}

function openNotificationsModal(is_read = 0,tab_click=false) {
    var url = absolute_path+'/notification_list.json?is_read='+is_read;
    // console.log(url);
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        document.getElementById('loading').classList.remove('show');
        $("#notifications").html(data);
        if (!tab_click) {
            $('#notifications_modal').addClass('show');
            $('#notification_tabs li').removeClass('active');
            $('#notification_tabs li:first-child').addClass('active');
        }
        loadActivityCookie();
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function loadNotificationCount() {
    setTimeout(function () {
        var url = absolute_path+'/notification_list.json?count=yes';
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            $('.notifications_nav').attr('data-count',data);
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }, 100);
}

$(document).on('click', '#notification_tabs li', function(){
    if ($(this).hasClass('active')) {
        return;
    } else {
        var active_tab = $(this).attr('data-tab');
        $('#notification_tabs li').removeClass('active');
        $(this).addClass('active');
    }
    var active_tab = $(this).attr('data-tab');
    // var is_read = $(this).attr('data-id');
    $('#notification_tabs li').removeClass('active');
    $(this).addClass('active');
    if (active_tab == 'unread') {
        openNotificationsModal(0,true);
    }else{
        openNotificationsModal(1,true);
    }
});

function addViewedArticle(id,type='add'){
    var url = absolute_path+'/recently_viewed_article.json?v='+id+'&t='+type;
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function fetchArticleModal(e,id,article='') {
    var url = absolute_path+'/article.json?id='+id;
    if(article != ''){
        url += '&url='+article;
    }
    document.getElementById('loading').classList.add('show');
    console.log(url);
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        document.getElementById('loading').classList.remove('show');
        document.getElementById("article_modal").innerHTML = data.article;
        ga4_datalayer('view_article',id);
        pushHistory();
        history.pushState(null, data.title, data.url);
        document.title = data.title+title_append;
        modal_backbutton('#article_modal');
        $('#article_modal').addClass('show');
        $('#notification_popup').removeClass('show');
        load_favorites();
        loadActivityCookie();
        addViewedArticle(id);
        if (typeof dataLayer !== 'undefined') {
            dataLayer.push({'event':'modal_url','modalURL':data.url,'logged_in':'1'});
        }
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function sortTab(target){
    var divs = [],
        listings = document.querySelectorAll("#franchises .item");
        document.querySelectorAll("*[data-src]");
    if(target == 'select'){
        var data_type = $('select#sort_by').val();
        $('input[type="radio"][name="sort"][value="'+data_type+'"]').prop("checked", true);
    }else{
        var data_type = $('input[type="radio"][name="sort"]:checked').val();
        $('select#sort_by').val(data_type);
    }
    $('#filtered,#filtered2').val(data_type);
    // console.log(data_type);

    for (var i = 0; i < listings.length; ++i) {
        divs.push(listings[i]);
    }
    if(data_type == 'default'){
        divs.sort(function(a, b) {
            return b.getAttribute("data-order").localeCompare(a.getAttribute("data-order"));
        });
    }else if(data_type == 'franchise_az'){
        divs.sort(function(a, b) {
            return a.getAttribute("data-name").localeCompare(b.getAttribute("data-name"));
        });
    }else if(data_type == 'franchise_za'){
        divs.sort(function(a, b) {
            return b.getAttribute("data-name").localeCompare(a.getAttribute("data-name"));
        });
    }else if(data_type == 'industry_az'){
        divs.sort(function(a, b) {
            return a.getAttribute("data-category").localeCompare(b.getAttribute("data-category"));
        });
    }else if(data_type == 'industry_za'){
        divs.sort(function(a, b) {
            return b.getAttribute("data-category").localeCompare(a.getAttribute("data-category"));
        });
    }else if(data_type == 'mcr_low'){
        divs.sort(function(a, b) {
            return a.getAttribute("data-invest").localeCompare(b.getAttribute("data-invest"));
        });
    }else if(data_type == 'mcr_high'){
        divs.sort(function(a, b) {
            return b.getAttribute("data-invest").localeCompare(a.getAttribute("data-invest"));
        });
    }else if(data_type == 'mcr_old'){
        divs.sort(function(a, b) {
            return a.getAttribute("data-year").localeCompare(b.getAttribute("data-year"));
        });
    }else if(data_type == 'mcr_new'){
        divs.sort(function(a, b) {
            return b.getAttribute("data-year").localeCompare(a.getAttribute("data-year"));
        });
    }else if(data_type == 'units_low'){
        divs.sort(function(a, b) {
            return a.getAttribute("data-units").localeCompare(b.getAttribute("data-units"));
        });
    }else if(data_type == 'units_high'){
        divs.sort(function(a, b) {
            return b.getAttribute("data-units").localeCompare(a.getAttribute("data-units"));
        });
    }
    var div = document.createElement('div');
    divs.forEach(function(el) {
        div.appendChild(el);
    });
    var inside = div.innerHTML;
    $("#results").html(inside);
    lazyLoadBg();
    $('html').scrollTop(0);
}

// Check Favorites on Load
function check_count(form){
    setTimeout(function () {
        var query = '?website_id=60',
            cat_id = [],
            array = ['min','max','units_min','units_max','state_code','cat_id','franchising_since','home_based','military_discount','net_worth_min','net_worth_max'];
        $(form).find('input[name = "cat_id[]"]').each(function () {
            var val = $(this).val().trim();
            cat_id.push(val);
        });
        if(cat_id.length > 0){
            query += '&cat_id='+cat_id.toString();
        }
        $(form).find('select,input').each(function () {
            var name = $(this).attr('name'),
                val = $(this).val().trim();
            if($(this).attr('type') == 'checkbox'){
                if($(this).is(':checked')){
                    // Do Nothing;
                }else{
                    val = '';
                }
            }
            if (array.includes(name) && ((val) && val != '')) {
                query += '&' + name + '=' + val;
            }
        });
        var count = 0;
        var url = absolute_path+'/concepts.json'+query;
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            count = data.total;
            $('.franchise_count').text(count);
            $('button[type="submit"]').attr('data-count',count);
            if(count == 0){
                $('.franchise_count').closest('button').attr('disabled', true);
            }else{
                $('.franchise_count').closest('button').attr('disabled', false);
            }
            // console.log(count);
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }, 100);
}

// Load Profile Modal
function view_summary(e,concept_id,profile = ''){
    $('#profile_modal .content').html('');
    document.getElementById('loading').classList.add('show');
    var url = absolute_path+'/profile.json?id='+concept_id,
        temp = profile_favorites = profile_header = profile_columns = profile_columns1 = profile_columns2 = profile_columns3 = column1 = column2 = column3 = profile_learn_more = profile_long_description = '',
        column1_array = {new_investment: 'Minimum Cash Required', new_min_net_worth: 'Net Worth', new_franchise_fee: 'Franchise Fee', royalty_fee : 'Royalty Fee',total_investment: 'Total Investment'},
        column2_array = {home_based: 'Home Based',is_mobile_franchise: 'Mobile Franchise', military_discount: 'Military Discount', financing_available : 'Financing',is_training_available: 'Training'},
        column3_array = {address: 'Headquarters',new_franchise_units: 'Total Units',year_founded: 'Year Founded',franchising_since: 'Franchising Since'};
    if(profile != ''){
        url += '&url='+profile;
    }
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        console.log(data);
        $('#profile_modal').attr('data-id',concept_id);
        document.getElementById('loading').classList.remove('show');
        $.getJSON(absolute_path+'/requested.json', function(requested) {
            if(requested == null){
                requested = [];
            }
            var profile_title = data.name.replace(/[^\x20-\x7E]/g, '').replace('&#2013265929;', 'é').replace(/\\/g, '').replace(/\'/g, '').replace('&amp;', '&').replace(';', '');
            for ([key, value] of Object.entries(column1_array)) {
                if(data[key] != null){
                    column1 += `<tr>
                                    <td class="title">`+value+`</td>
                                    <td class="value">`+data[key]+`</td>
                                </tr>`;
                }
            }
            for ([key, value] of Object.entries(column2_array)) {
                if(data[key] == 1){
                    var temp_value = 'Available';
                    if (['home_based','is_mobile_franchise'].includes(key)){
                        temp_value = 'Yes';
                    }
                    column2 += `<tr>
                                    <td class="title">`+value+`</td>
                                    <td class="value">`+temp_value+`</td>
                                </tr>`;
                }
            }
            for ([key, value] of Object.entries(column3_array)) {
                if(data[key] != null){
                    column3 += `<tr>
                                    <td class="title">`+value+`</td>
                                    <td class="value">`+data[key]+`</td>
                                </tr>`;
                }
            }
            if(column1 != '' && profile_columns1 == ''){
                profile_columns1 = `<div class="profile_column">
                                            <h2>Requirements</h2>
                                            <table>
                                                <tr>
                                                    <td class="subtitle" colspan="2">Financial requirements for this franchise:</td>
                                                </tr>`+column1+`</table>
                                        </div>`;
            }
            if(column2 != '' && profile_columns2 == ''){
                profile_columns2 = `<div class="profile_column">
                                            <h2>Options</h2>
                                            <table>
                                                <tr>
                                                    <td class="subtitle" colspan="2">Options available to franchisees:</td>
                                                </tr>`+column2+`</table>
                                        </div>`;
            }
            if(column3 != '' && profile_columns3 == ''){
                profile_columns3 = `<div class="profile_column">
                                            <h2>Franchisor Details</h2>
                                            <table>
                                                <tr>
                                                    <td class="subtitle" colspan="2">Facts about this franchise:</td>
                                                </tr>`+column3+`</table>
                                        </div>`;
            }
            profile_header = `<div id="profile_header">
                                <div class="profile_header_item">
                                    <h1 class="profile_title">`+profile_title+`</h1>
                                    <h2 class="profile_category">`+data.category_name+`</h2>
                                    <p class="profile_details">`+data.intro_description+`</p>
                                </div>
                                <div class="profile_header_item add_border">
                                    <div class="profile_banner">
                                        <!--<span class="slider left">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M6.028 0v6.425l5.549 5.575-5.549 5.575v6.425l11.944-12z"/></svg>
                                        </span>-->
                                        <img src="`+data.image_url+`"/>
                                        <!--<span class="slider right">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M6.028 0v6.425l5.549 5.575-5.549 5.575v6.425l11.944-12z"/></svg>
                                        </span>-->
                                    </div>
                                    <!--<div class="profile_video"><a href="javascript:;">Watch Our Video &raquo;</a></div>-->
                                </div>
                            </div>`;
            profile_columns = `<div id="profile_columns">`+profile_columns1+profile_columns2+profile_columns3+`</div>`;
            profile_learn_more = `<div class="profile_learn_more">
                                    <h2>Availability</h2>
                                    <p><strong>`+profile_title+`</strong> is currently accepting inquiries from the following states:</p>
                                    <p>`+data.locations+`</p>
                                </div>`;
            profile_long_description = `<div class="profile_long_description">`+data.long_description+`</div>`;
            profile_favorites = `<input type="checkbox" class="favorites" data-fboid="`+data.fbo_id+`" onclick="favorites(`+data.fbo_id+`);">`;
            temp = profile_header+profile_columns+profile_learn_more+profile_long_description;
            $('#profile_modal .content').html(temp);
            $('#profile_modal .listing_favorites').html(profile_favorites);
            var profile_footer_button = '<span class="button disabled">Information Already Requested</span>';
            if(!requested.includes(data.fbo_id)){
                profile_footer_button = `<a href="javascript:;" class="button" onclick="request_modal(`+data.fbo_id+`,`+data.investment+`,'`+profile_title+`');">Request Free Information</a>`;
            }
            $('.profile_footer_button').html(profile_footer_button);
            modal_backbutton('#profile_modal');
            modal('profile_modal');
            if (typeof dataLayer !== 'undefined') {
                dataLayer.push({'event':'modal_url','modalURL':'/dashboard/franchise'+data.brochure_url,'logged_in':'1'});
            }
            pushHistory();
            history.pushState(null, profile_title, absolute_path+'/franchise'+data.brochure_url);
            document.title = profile_title+title_append;
            load_favorites();
            loadActivityCookie();
            recently_viewed(data.fbo_id);
            $('#profile_modal .guts').scrollTop(0);
            ga4_datalayer('beta_view_item',data.fbo_id);
        });
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Add to Recently Viewed
function recently_viewed(fbo_id){
    var url = absolute_path+'/recently_viewed.json?v='+fbo_id+'&t=add';
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Modal Text
function modal_backbutton(target){
    var page = $('body').attr('data-page');
    if(page == 'recent'){
        page = 'recently viewed';
    }else if(page == 'saved'){
        page = 'saved articles';
    }
    var text = 'Back to '+page;
    $(target).find('.close_modal').attr('data-before',text);
}

// Load Survey
function load_survey(quiz){
    if (!localStorage.survey) {
        var url = absolute_path+'/survey.json';
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if(data['count'] == 0){
                if (data['quiz_close'] != 'yes') {
                    modal('survey_modal');
                    ga4_global_events('quiz_started'); 
                }
            }else{
                window.localStorage.setItem('survey', '1');
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
}

function appendIdUrl(url=''){
    var tagUrl = window.location.href.split('#')[0];
    if (url) {
        var tagUrl = tagUrl+url;
    }
    history.pushState({ path: tagUrl }, '', tagUrl);
}

$(document).on('click', '.modal', function(event) {
    var parentId = $(this).closest('.modal').attr('id');
    if (parentId != 'survey_modal' && !$(event.target).closest('.guts').length) {
        // console.log('Parent ID:', parentId);
        $('#'+parentId).removeClass('show');
    }else{
        if (parentId == 'survey_modal' && !$(event.target).closest('.guts').length) {
            closeSurveyModal();
            $('#'+parentId).removeClass('show');
        }
    }
    pushHistory();
});

// adding session 'myac_quiz_close' if closing the survey modal
function closeSurveyModal() {
    window.localStorage.setItem('survey', '1');
}

document.addEventListener("DOMContentLoaded", function() {
    if (typeof page !== 'undefined' && page == 'home') {
        var url = absolute_path+'/article_list.json?article_type=302';
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading').classList.remove('show');
            document.getElementById("home_articles").innerHTML = data;
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });

        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('openMessageCenter')) {
            var tagUrl = window.location.href.split('?')[0];
            window.history.pushState({ path: tagUrl }, '', tagUrl);
            modal('notifications_modal');        
        }else if (urlParams.has('openMenu')) {
            menu_modal();    
        }else if (urlParams.has('openSubmit')) {
            console.log(urlParams.getAll('i')); // ["api"]
        }
    }
})

function changePasswordToggle() {
    $('input[name="show_change_password"]').change(function() {
        var temp_checked = $('input[name="show_change_password"]:checked').length;
        if (temp_checked >= 1) {
            $('input[name="current_password"]').prop('type', 'text');
            $('input[name="new_password"]').prop('type', 'text');
            $('input[name="confirm_password"]').prop('type', 'text');
        } else {
            $('input[name="current_password"]').prop('type', 'password');
            $('input[name="new_password"]').prop('type', 'password');
            $('input[name="confirm_password"]').prop('type', 'password');
        }
    });
}

function settings_modal(){
    var url = absolute_path+'/settings_form.json';
    $('.overlay,#menu_modal').removeClass('show');
    $('#settings_modal').addClass('show');
    appendIdUrl('#'+page+'-settings');
    modal_backbutton('#settings_modal');
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        document.getElementById('loading').classList.remove('show');
        document.querySelector("#settings_modal .content").innerHTML = data;
        changePasswordToggle();
        loadActivityCookie();
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function toggleSettingsNav(e){
    var btns = document.querySelectorAll(".settings_nav_item");
    for (var i = 0; i < btns.length; i++) {
        btns[i].classList.remove('active');
    }
    e.classList.add('active');
    var navItem = e.getAttribute('data-item');
    if(navItem == 'personal_info'){
        document.getElementById('settings_pi').classList.remove('hide');
        document.getElementById('settings_pw').classList.add('hide');
        document.getElementById('settings_nt').classList.add('hide');
    }else if(navItem == 'change_password'){
        document.getElementById('settings_pw').classList.remove('hide');
        document.getElementById('settings_pi').classList.add('hide');
        document.getElementById('settings_nt').classList.add('hide');
    }else{
        document.getElementById('settings_nt').classList.remove('hide');
        document.getElementById('settings_pi').classList.add('hide');
        document.getElementById('settings_pw').classList.add('hide');
    }
}

function zipcode_check(zip_code, callback){
    var url = absolute_path+'/zip.json?z='+zip_code;
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        document.getElementById('loading').classList.remove('show');
        callback(data);
        
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function validatePassword(password) {
    // Regular expression to check the conditions:
    // - At least one uppercase letter
    // - include at least one of the following characters !@$
    // - At least 7 characters long
    // const regex = /^(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{7,}$/;
    const regex = /^(?=.*[A-Z])(?=.*[!@$]).{7,}$/;
    return regex.test(password);
}

function formFieldsValidation(idF,text) {
    document.querySelector(idF).classList.add('error-outline');
    document.querySelector(idF).nextElementSibling.classList.remove('hide');
    document.querySelector(idF).nextElementSibling.innerHTML = text;
}

function removeErrors(id, errors){
    for (let i = 0; i < errors.length; i++) {
        const element = errors[i];
        document.querySelector('#'+id+' input[name='+element+']').classList.remove('error-outline');
        document.querySelector('#'+id+' input[name='+element+']').nextElementSibling.classList.add('hide');
    }
}

function updateUserPost(url, dataObj) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: dataObj
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        document.getElementById('loading').classList.remove('show');
        if (data.status == 'success') {
            var content = `<p>Your data has been updated.</p>
                <p class="text-center"><a href="javascript:" onclick="modal('alert','close');" class="button">Okay</a></p>`;
                document.querySelector('#alert .content').innerHTML = content;
                document.getElementById('alert').classList.add('show');
                if($('#request_popup').hasClass('show')){
                    var info = data.data;
                    var fbo_id = $('#request_popup').attr('data-fbo_id'),
                        investment = $('#request_popup').attr('data-investment'),
                        name = $('#request_popup').attr('data-name');
                        console.log(name);
                    global_cookie['z'] = info.zip_code;
                    global_cookie['p'] = info.phone;
                    // console.log(global_cookie);
                    request_modal(fbo_id,investment,"'"+name+"'");
                }
        }else{
            formFieldsValidation('#settings_modal select[name=state]','MyAccount user failed to save.');
        }
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.log('Error:', error);
    });
}

function updateUser() {
    var url = absolute_path+'/update_user.json';
    removeErrors('settings_modal',['first_name','last_name','phone','zip_code']);
    var first_name = document.querySelector('#settings_modal input[name=first_name]').value;
    var last_name = document.querySelector('#settings_modal input[name=last_name]').value;
    var phone = document.querySelector('#settings_modal input[name=phone]').value;
    var zip_code = document.querySelector('#settings_modal input[name=zip_code]').value;
    phone = phone ? phone.replace(/[^0-9]/g,'') : phone;
    var state_code = document.querySelector('#settings_modal select[name=state]').value;
    if (!first_name) {
        formFieldsValidation('#settings_modal input[name=first_name]','First Name is required');
    }else if (!last_name) {
        formFieldsValidation('#settings_modal input[name=last_name]','Last Name is required');
    }else{
        document.getElementById('loading').classList.add('show');
        var dataObj = {
            first_name : first_name,
            last_name : last_name,
            phone : phone,
            state_code : state_code,
            zip_code : zip_code
        }
        dataObj = JSON.stringify(dataObj);
        if (zip_code) {
            zipcode_check(zip_code, function (data) {
                if (!data) {
                    document.getElementById('loading').classList.remove('show');
                    formFieldsValidation('#settings_modal input[name=zip_code]','ZIP Code is not valid.');
                }else{
                    updateUserPost(url, dataObj);
                }
            })
        }else{
            updateUserPost(url, dataObj)
        }
    }
}

function changePassword() {
    var url = absolute_path+'/change_password.json';
    removeErrors('settings_modal',['current_password','new_password','confirm_password']);

    var current_password = document.querySelector('#settings_modal input[name=current_password]').value;
    var new_password = document.querySelector('#settings_modal input[name=new_password]').value;
    var confirm_password = document.querySelector('#settings_modal input[name=confirm_password]').value;
    var passwordValidate = validatePassword(new_password);
    if (!current_password) {
        formFieldsValidation('#settings_modal input[name=current_password]','Current Password is required');
    }else if (!new_password) {
        formFieldsValidation('#settings_modal input[name=new_password]','New Password is required');
    }else if (!confirm_password) {
        formFieldsValidation('#settings_modal input[name=confirm_password]','Confirm Password is required');
    }else if(!passwordValidate){
        formFieldsValidation('#settings_modal input[name=new_password]','Password must be At least one uppercase letter, include at least one of the following characters <strong>!@$</strong> and At least 7 characters long');
    }else if(new_password != confirm_password){
        formFieldsValidation('#settings_modal input[name=confirm_password]','Password & Confirm Password not match');
    }else{
        var dataObj = {
            current_password : current_password,
            new_password : new_password
        }
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataObj)
        })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                document.querySelector('#settings_modal input[name=current_password]').value = '';
                document.querySelector('#settings_modal input[name=new_password]').value = '';
                document.querySelector('#settings_modal input[name=confirm_password]').value = '';
                var content = `<p>Your password has been changed.</p>
                    <p class="text-center"><a href="javascript:" onclick="modal('alert','close');" class="button">Okay</a></p>`;
                    document.querySelector('#alert .content').innerHTML = content;
                    document.getElementById('alert').classList.add('show');
            }else{
                formFieldsValidation('#settings_modal input[name=current_password]','Current Password seems to be wrong.');
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.log('Error:', error);
        });
    }
}

// Request Modal
function request_modal(fbo_id,investment,name){
    var url = absolute_path+'/info.json';
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        var content = `<h3>Request Information</h3>`,
            paragraph = 'You are requesting additional information about <strong>'+name+'</strong>.',
            text = phone = zip_code = contact_info = contact_list = '',
            contact_loop = {f: 'First Name', l: 'Last Name', e: 'Email', p : 'Phone',z: 'ZIP Code'}
        if(data.p == null || data.p == '' || data.z == null || data.z == ''){
            if(data.p == null || data.p == ''){
                text += ' the best phone number to reach you'
                phone = `<div class="row label_inline">
                            <label>Phone</label>
                            <div class="row_item">
                                <input type="tel" id="request_phone" name="request_phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" oninput="this.value=formatPhoneNumber(this.value);" required>
                                <div class="error"></div>
                            </div>
                        </div>`;
            }
            if(data.z == null || data.z == ''){
                if(text != ''){
                    text += ' and';
                }
                text += ' your ZIP code'
                zip_code = `<div class="row label_inline">
                            <label>Your ZIP Code</label>
                            <div class="row_item">
                                <input type="text" id="zip_code" name="zip_code" maxlength="5" pattern="[0-9]{5}" oninput="this.value=formatNumber(this.value);" required>
                                <div class="error"></div>
                            </div>
                        </div>`;
            }
            paragraph += ' You will need to provide '+text+'.';
        }
        for ([key, value] of Object.entries(contact_loop)) {
            if(global_cookie[key] != null && global_cookie[key] != '' && global_cookie[key] != 0){
                var temp_value = global_cookie[key];
                if(key == 'p'){
                    temp_value = formatPhoneNumber("'"+temp_value+"'");
                }
                contact_list += `<tr>
                                <td class="title">`+value+`</td>
                                <td class="value">`+temp_value+`</td>
                            </tr>`;
            }
        }
        contact_info = `<div class="contact_info">
                            <strong>Your Contact Information</strong>
                            <div id="contact_list">
                                <table>`+contact_list+`</table>
                                <p><a href="javascript:;" onclick="settings_modal();">Update Your Information</a></p>
                            </div>
                        </div>`;
        content += `<p>`+paragraph+`</p>`+contact_info+phone+zip_code;
        content += `<div class="checkbox_button">
                        <label>
                            <input type="checkbox" value="1" name="request_info" checked>
                            <span>By pressing "Request", you agree that Franchise.com and businesses you selected may call/text/email you, including for marketing purposes related to your inquiry. This contact may be made using automated or pre-recorded/artificial voice technology. Data and message rates may apply. You don't need to consent as a condition of any purchase. You may opt-out of SMS at any time by replying <strong>STOP</strong> to the phone number we texted you from. You also agree to our <a href="javascript:;" onclick="modal('privacy_modal');">Privacy Policy &amp; Terms of Use</a>.</span>
                        </label>
                    </div>`;
        content += `<div class="text-center">
                        <button class="button cancel" type="button" onclick="modal('request_popup','close');">Cancel</button>
                        <button class="button solid" type="button" onclick="request_info(`+fbo_id+`,`+investment+`);">Request</button>
                    </div>`;
        document.getElementById('loading').classList.remove('show');
        $('#request_popup').find('.content').html(content);
        $('#request_popup').attr('data-fbo_id',fbo_id);
        $('#request_popup').attr('data-investment',investment);
        $('#request_popup').attr('data-name',name);
        modal('request_popup');
        ga4_datalayer('beta_view_cart',fbo_id);
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function sendRequestInfo(url,fbo_id,investment) {
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        if (typeof dataLayer !== 'undefined') {
            var transaction_id = Date.now();
            if (data != null) {
                transaction_id = data.session_id;
            }
            dataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object.
            dataLayer.push({
                event: "beta_purchase",
                ecommerce: {
                    transaction_id: transaction_id,
                    value: investment,
                    currency: "USD",
                    items: [{item_id: fbo_id,affiliation: 60,price: investment+'.00',quantity: 1}]
                }
            });
        }
        if(typeof fbq === "function"){
            fbq('track', 'Purchase', {currency: "USD", value: investment+'.00'});
        }
        modal('request_popup','close');
        modal('profile_modal','close');
        document.getElementById('loading').classList.remove('show');
        // alert_modal(h3 = 'Information Requested!','Make sure to check your email and text messages for incoming additional information.')
        if(getCookie('post_survey') == 'done'){
            alert_modal(h3 = 'Information Requested!','Make sure to check your email and text messages for incoming additional information.');
        }else{
            modal('quiz_modal');
            setCookie('post_survey','done',365);
        }
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

// Request Information
function request_info(fbo_id,investment){
    var url = absolute_path+'/submission.json?f='+fbo_id+'&i='+investment,
        valid = true,
        is_checked = $('input[name="request_info"]:checked').length;
    if(is_checked == 0){
        valid = false;
    }
    if($('#request_phone').length){
        $('#request_phone').removeClass('error-outline');
        $('#request_phone + .error').text('');
        var request_phone = $('#request_phone').val(),
            phone_pattern = /^\d{10}$/;
            request_phone = request_phone.replace(/\D/g,'');
        if(request_phone[0] == '1'){
            request_phone = request_phone.substr(1);
        }
        if(request_phone.match(phone_pattern)){
            url += '&p='+request_phone;
        }else{
            $('#request_phone').addClass('error-outline');
            $('#request_phone + .error').text('Valid Phone Number is required');
            valid = false;
        }
    }
    if($('#zip_code').length){
        $('#zip_code').removeClass('error-outline');
        $('#zip_code + .error').text('');
        var zip_code = $('#zip_code').val(),
            zip_code_pattern = /^\d{5}$/;
            zip_code = zip_code.replace(/\D/g,'');
        if(zip_code.match(zip_code_pattern)){
            url += '&z='+zip_code;
            document.getElementById('loading').classList.add('show');
            zipcode_check(zip_code, function (data) {
                document.getElementById('loading').classList.remove('show');
                if (!data) {
                    $('#zip_code').addClass('error-outline');
                    $('#zip_code + .error').text('Valid ZIP code is required');
                }
            });
        }else{
            $('#zip_code').addClass('error-outline');
            $('#zip_code + .error').text('Valid ZIP code is required');
            valid = false;
        }
    }
    if(valid){
        sendRequestInfo(url,fbo_id,investment);
    }
}

function formatPhoneNumber(phoneNumberString) {
    var input = phoneNumberString.replace(/\D/g,'');
    var size = input.length;
    if (size>0) {input="("+input}
    if (size>3) {input=input.slice(0,4)+") "+input.slice(4,11)}
    if (size>6) {input=input.slice(0,9)+"-" +input.slice(9)}
    return input;
}

function formatNumber(numberString) {
    return numberString.replace(/\D/g,'');
}

function updateEmailSetting(){
    var email_unsubscribe = document.getElementById('email_unsubscribe').checked == true ? 0 : 1;
    // console.log(email_unsubscribe);
    var url = absolute_path+'/update_email_setting.json?email_unsubscribe='+email_unsubscribe;
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        document.getElementById('loading').classList.remove('show');
        var content = `<p>Your Notification Setting has been updated.</p>
                    <p class="text-center"><a href="javascript:" onclick="modal('alert','close');" class="button">Okay</a></p>`;
                    document.querySelector('#alert .content').innerHTML = content;
                    document.getElementById('alert').classList.add('show');
        
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function deleteNotificationAlert(id) {
    var content = `<p>Are you sure you want to delete this message?</p>
                    <p class="text-center">
                    <a href="javascript:" onclick="modal('alert','close');" class="button">Cancel</a>
                    <a href="javascript:" onclick="deleteNotification(`+id+`);" class="button solid">Confirm</a>
                    </p>`;
    document.querySelector('#alert .content').innerHTML = content;
    document.getElementById('alert').classList.add('show');
}

function refreshAfterDeleteNotification() {
    var content = `<p>Message has been deleted successfully.</p>
                    <p class="text-center">
                    <a href="javascript:" onclick="modal('alert','close');" class="button">Close</a>
                    </p>`;
        document.querySelector('#alert .content').innerHTML = content;
        document.getElementById('alert').classList.add('show');
}

function deleteNotification(id) {
    var url = absolute_path+'/delete_notification.json?id='+id;
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        modal('alert','close');
        modal('notification_popup','close');
        document.getElementById('loading').classList.remove('show');
        refreshAfterDeleteNotification()
        openNotificationsModal(0,true);
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
    });
}

function feedbackSubmit() {
    var url = absolute_path+'/feedback_save.json';
    document.querySelector('#feedback_popup textarea[name=comments]').classList.remove('error-outline');
    document.querySelector('#feedback_popup textarea[name=comments]').nextElementSibling.classList.add('hide');
    var first_name = document.querySelector('input[name=first_name]').value;
    var last_name = document.querySelector('input[name=last_name]').value;
    var email = document.querySelector('input[name=email]').value;
    var phone = (document.querySelector('input[name=phone]').value).replace(/[^0-9]/g,'');
    var comments = document.querySelector('textarea[name=comments]').value;
    var dataObj = {
        first_name : first_name,
        last_name : last_name,
        email : email,
        phone : phone,
        comments : comments
    }
    if (!comments) {
        formFieldsValidation('#feedback_popup textarea[name=comments]','Comments is required');
    }else{
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataObj)
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                var content = `<h2 class="feedback_modal_h2">Thank You!</h2><p class="text-center">Your feedback has been saved.</p><br>
                    <p class="text-center"><a href="javascript:" onclick="modal('feedback_popup','close');" class="button">Okay</a></p>`;
                    document.querySelector('#feedback_popup .content').innerHTML = content;
            }else{
                formFieldsValidation('#feedback_popup textarea[name=comments]',data.message);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
    }
}

function correctPwdPattern(e) {
    let passwordInput = e.value;
    let sanitizedValue = passwordInput.replace(/[^a-zA-Z0-9!@$]/g, '');
    return sanitizedValue;
}

function showTooltip(evt, text) {
    let tooltip = document.getElementById("tooltip");
    tooltip.innerHTML = text;
    tooltip.style.display = "block";
    tooltip.style.left = evt.pageX + 10 + 'px';
    tooltip.style.top = evt.pageY + 10 + 'px';
}

function showNWTooltip(evt, text, id) {
    let tooltip = document.getElementById(id);
    tooltip.innerHTML = text;
    tooltip.style.display = "block";
    tooltip.style.left = 10 + 'px';
    tooltip.style.top = 10 + 'px';
}
  
function hideTooltip(id=null) {
    var tid = id ? id : 'tooltip';
    var tooltip = document.getElementById(tid);
    tooltip.style.display = "none";
}

// Cookie Variables
function cookie_var(){
    let cookie_json = new XMLHttpRequest();
    console.log(absolute_path+'/info.json');
    cookie_json.open('get',absolute_path+'/info.json',false);
    cookie_json.send(null);
    var temp = JSON.parse(cookie_json.responseText);
    if (typeof quiz_global_cookie !== 'undefined') {
        if(quiz_global_cookie['quiz_udid'] ==  null){
            quiz_global_cookie['quiz_udid'] = temp.i;
        }
        if(quiz_global_cookie['beta_email'] ==  null){
            quiz_global_cookie['beta_email'] = temp.e;
        }
    }
    return temp;
}

//GA4 DataLayer Push
function ga4_datalayer(event,fbo_id = null){
    if (typeof dataLayer !== 'undefined') {
        window.dataLayer = window.dataLayer || [];
        var push = '"event": "'+event+'", "login_contract_id": "'+global_cookie.i+'"';
        if(fbo_id != null){
            push += ',"fbo_id" : '+fbo_id;
        }
        var final = JSON.parse('{'+push+'}');
        window.dataLayer.push(final);
    }
}

function ga4_global_events(event,parameter = '',value = ''){
    if (typeof dataLayer !== 'undefined') {
        var dataLayer_array = {'event': event,'user_id': login_id};
        if(parameter != '' && value != ''){
            dataLayer_array[parameter] = value;
        }
        window.dataLayer = window.dataLayer || [];
        dataLayer.push(dataLayer_array);
    }
}

function load_articles(){
    if (typeof article_url !== 'undefined') {
        fetchArticleModal(this,1,article_url);
    }else if (typeof profile_url !== 'undefined') {
        view_summary(this,123456,profile_url);
    }
}

function pushHistory(){
    var folders = document.URL.split('/');
    if(folders['4'] == 'resources' || folders['4'] == 'franchise'){
        var page = back_url = $('body').attr('data-page'),
            new_title = page.charAt(0).toUpperCase() + page.slice(1);
        if(page == 'home'){
            back_url = '';
        }
        history.pushState(null, new_title, absolute_path+'/'+back_url);
        // console.log('asdasdad'+absolute_path+'/'+back_url);
        document.title = new_title+title_append;
    }
}

async function addCompareSelect(id,type='add'){
    var url = absolute_path+'/comparision.json?v='+id+'&t='+type;
    await fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function setCompareItems(results=null) {
    var image_thumbnails = document.querySelectorAll('.compare_thumbnail');
    var cnt = results ? results.length : 0;
    for (let i = 0; i < image_thumbnails.length; i++) {
        const item = image_thumbnails[i];
        item.classList.remove('has_image');
        item.removeAttribute('data-id');
        item.removeAttribute('data-image');
        item.style.backgroundImage = "url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDUwIDUwIiBmaWxsPSIjN0Y5QUM3Ij4KPHBhdGggZD0iTSAyNSAyIEMgMTIuMzA5NTM0IDIgMiAxMi4zMDk1MzQgMiAyNSBDIDIgMzcuNjkwNDY2IDEyLjMwOTUzNCA0OCAyNSA0OCBDIDM3LjY5MDQ2NiA0OCA0OCAzNy42OTA0NjYgNDggMjUgQyA0OCAxMi4zMDk1MzQgMzcuNjkwNDY2IDIgMjUgMiB6IE0gMjUgNCBDIDM2LjYwOTUzNCA0IDQ2IDEzLjM5MDQ2NiA0NiAyNSBDIDQ2IDM2LjYwOTUzNCAzNi42MDk1MzQgNDYgMjUgNDYgQyAxMy4zOTA0NjYgNDYgNCAzNi42MDk1MzQgNCAyNSBDIDQgMTMuMzkwNDY2IDEzLjM5MDQ2NiA0IDI1IDQgeiBNIDM0Ljk4ODI4MSAxNC45ODgyODEgQSAxLjAwMDEgMS4wMDAxIDAgMCAwIDM0LjE3MTg3NSAxNS40Mzk0NTMgTCAyMy45NzA3MDMgMzAuNDc2NTYyIEwgMTYuNjc5Njg4IDIzLjcxMDkzOCBBIDEuMDAwMSAxLjAwMDEgMCAxIDAgMTUuMzIwMzEyIDI1LjE3NzczNCBMIDI0LjMxNjQwNiAzMy41MjUzOTEgTCAzNS44MjgxMjUgMTYuNTYwNTQ3IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAzNC45ODgyODEgMTQuOTg4MjgxIHoiPjwvcGF0aD4KPC9zdmc+)";
        item.innerHTML = '';
    }

    if (results) {
        for (let i = 0; i < results.length; i++) {
            const c_item = results[i];
            let image_url = c_item['image_url'];
            let id = c_item['id'];
            const item = image_thumbnails[i];
            item.classList.add('has_image');
            item.setAttribute('data-id',id);
            item.setAttribute('data-image',image_url);
            item.style.backgroundImage = "url("+image_url+")";
            item.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" onclick="removeCompareItem('+id+');" aria-hidden="true" viewBox="0 0 48 48"><path fill="#f44336" d="M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z"></path><path fill="#fff" d="M29.656,15.516l2.828,2.828l-14.14,14.14l-2.828-2.828L29.656,15.516z"></path><path fill="#fff" d="M32.484,29.656l-2.828,2.828l-14.14-14.14l2.828-2.828L32.484,29.656z"></path></svg>';
        }
    }
    
    var image_thumbnails_active = document.querySelectorAll('.compare_thumbnail.has_image');
    if (image_thumbnails_active.length > 1) {
        document.getElementById('btn_compare').classList.add('active');
    }else{
        document.getElementById('btn_compare').classList.remove('active');
    }

}

function setCompareTumbnails(e) {
    var id = e.getAttribute('data-id');
    var url = e.getAttribute('data-image');
    var image_thumbnails = document.querySelectorAll('.compare_thumbnail');
    for (let i = 0; i < image_thumbnails.length; i++) {
        const item = image_thumbnails[i];
        if (!item.classList.contains('has_image')) {
            item.classList.add('has_image');
            item.setAttribute('data-id',id);
            item.setAttribute('data-image',url);
            item.style.backgroundImage = "url("+url+")";
            item.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" onclick="removeCompareItem('+id+');" aria-hidden="true" viewBox="0 0 48 48"><path fill="#f44336" d="M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z"></path><path fill="#fff" d="M29.656,15.516l2.828,2.828l-14.14,14.14l-2.828-2.828L29.656,15.516z"></path><path fill="#fff" d="M32.484,29.656l-2.828,2.828l-14.14-14.14l2.828-2.828L32.484,29.656z"></path></svg>';
            break;
        }
    }
    var image_thumbnails_active = document.querySelectorAll('.compare_thumbnail.has_image');
    if (image_thumbnails_active.length > 1) {
        document.getElementById('btn_compare').classList.add('active');
    }else{
        document.getElementById('btn_compare').classList.remove('active');
    }
}

function rearrangeThumnails(arr) {
    var imageThumbnails = document.querySelectorAll('.compare_thumbnail');
    for (let i = 0; i < imageThumbnails.length; i++) {
        let item = imageThumbnails[i];
        item.classList.remove('has_image');
        item.style.backgroundImage = "url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDUwIDUwIiBmaWxsPSIjN0Y5QUM3Ij4KPHBhdGggZD0iTSAyNSAyIEMgMTIuMzA5NTM0IDIgMiAxMi4zMDk1MzQgMiAyNSBDIDIgMzcuNjkwNDY2IDEyLjMwOTUzNCA0OCAyNSA0OCBDIDM3LjY5MDQ2NiA0OCA0OCAzNy42OTA0NjYgNDggMjUgQyA0OCAxMi4zMDk1MzQgMzcuNjkwNDY2IDIgMjUgMiB6IE0gMjUgNCBDIDM2LjYwOTUzNCA0IDQ2IDEzLjM5MDQ2NiA0NiAyNSBDIDQ2IDM2LjYwOTUzNCAzNi42MDk1MzQgNDYgMjUgNDYgQyAxMy4zOTA0NjYgNDYgNCAzNi42MDk1MzQgNCAyNSBDIDQgMTMuMzkwNDY2IDEzLjM5MDQ2NiA0IDI1IDQgeiBNIDM0Ljk4ODI4MSAxNC45ODgyODEgQSAxLjAwMDEgMS4wMDAxIDAgMCAwIDM0LjE3MTg3NSAxNS40Mzk0NTMgTCAyMy45NzA3MDMgMzAuNDc2NTYyIEwgMTYuNjc5Njg4IDIzLjcxMDkzOCBBIDEuMDAwMSAxLjAwMDEgMCAxIDAgMTUuMzIwMzEyIDI1LjE3NzczNCBMIDI0LjMxNjQwNiAzMy41MjUzOTEgTCAzNS44MjgxMjUgMTYuNTYwNTQ3IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAzNC45ODgyODEgMTQuOTg4MjgxIHoiPjwvcGF0aD4KPC9zdmc+)";
        item.innerHTML = '';
    }
    for (let i = 0; i < arr.length; i++) {
        let t_id = arr[i].getAttribute('data-id');
        let t_img = arr[i].getAttribute('data-image');
        let thumbnail = imageThumbnails[i];

        thumbnail.classList.add('has_image');
        thumbnail.setAttribute('data-id', t_id);
        thumbnail.setAttribute('data-image', t_img);
        thumbnail.style.backgroundImage = `url(${t_img})`;
        thumbnail.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" onclick="removeCompareItem(${t_id});" aria-hidden="true" viewBox="0 0 48 48">
                <path fill="#f44336" d="M44,24c0,11.045-8.955,20-20,20S4,35.045,4,24S12.955,4,24,4S44,12.955,44,24z"></path>
                <path fill="#fff" d="M29.656,15.516l2.828,2.828l-14.14,14.14l-2.828-2.828L29.656,15.516z"></path>
                <path fill="#fff" d="M32.484,29.656l-2.828,2.828l-14.14-14.14l2.828-2.828L32.484,29.656z"></path>
            </svg>`;
    }
}

function removeCompareItem(id) {
    if (document.getElementById('compare_select_'+id)) {
        document.getElementById('compare_select_'+id).checked = false;
    }
    var image_thumbnails = document.querySelectorAll('.compare_thumbnail');
    let remainingThumbnails = [];
    for (let i = 0; i < image_thumbnails.length; i++) {
        const item = image_thumbnails[i];
        if (item.getAttribute('data-id') == id) {
            item.classList.remove('has_image');
            item.removeAttribute('data-id');
            item.removeAttribute('data-image');
            item.style.backgroundImage = "url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDUwIDUwIiBmaWxsPSIjN0Y5QUM3Ij4KPHBhdGggZD0iTSAyNSAyIEMgMTIuMzA5NTM0IDIgMiAxMi4zMDk1MzQgMiAyNSBDIDIgMzcuNjkwNDY2IDEyLjMwOTUzNCA0OCAyNSA0OCBDIDM3LjY5MDQ2NiA0OCA0OCAzNy42OTA0NjYgNDggMjUgQyA0OCAxMi4zMDk1MzQgMzcuNjkwNDY2IDIgMjUgMiB6IE0gMjUgNCBDIDM2LjYwOTUzNCA0IDQ2IDEzLjM5MDQ2NiA0NiAyNSBDIDQ2IDM2LjYwOTUzNCAzNi42MDk1MzQgNDYgMjUgNDYgQyAxMy4zOTA0NjYgNDYgNCAzNi42MDk1MzQgNCAyNSBDIDQgMTMuMzkwNDY2IDEzLjM5MDQ2NiA0IDI1IDQgeiBNIDM0Ljk4ODI4MSAxNC45ODgyODEgQSAxLjAwMDEgMS4wMDAxIDAgMCAwIDM0LjE3MTg3NSAxNS40Mzk0NTMgTCAyMy45NzA3MDMgMzAuNDc2NTYyIEwgMTYuNjc5Njg4IDIzLjcxMDkzOCBBIDEuMDAwMSAxLjAwMDEgMCAxIDAgMTUuMzIwMzEyIDI1LjE3NzczNCBMIDI0LjMxNjQwNiAzMy41MjUzOTEgTCAzNS44MjgxMjUgMTYuNTYwNTQ3IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAzNC45ODgyODEgMTQuOTg4MjgxIHoiPjwvcGF0aD4KPC9zdmc+)";
            item.innerHTML = '';
        }else if (item.classList.contains('has_image')) {
            remainingThumbnails.push(item);
        }
    }
    var image_thumbnails_active = document.querySelectorAll('.compare_thumbnail.has_image');
    if (image_thumbnails_active.length > 1) {
        document.getElementById('btn_compare').classList.add('active');
    }else{
        document.getElementById('btn_compare').classList.remove('active');
    }
    rearrangeThumnails(remainingThumbnails);
    addCompareSelect(id,'remove');
}

async function toggleMoreModal(e,id) {
    var image_thumbnails = document.querySelectorAll('.compare_thumbnail.has_image');
    if (e.getAttribute('data-type') == 'general') {
        document.getElementById('more_modal_'+id).classList.toggle('hide');
    }else if(e.getAttribute('data-type') == 'more_modal'){
        document.getElementById('more_modal_'+id).classList.toggle('hide');
        document.getElementById('compare_check').checked = true;
        if (image_thumbnails.length >= 5) {
            toggle_compare();
            alert_modal('Your have reached maximum limit','Maximum selection for comparision has been reached, you can remove any of those selected franchises to add another.');
            return;
        }
        toggle_compare(false);
        document.getElementById('compare_select_'+id).checked = true;
        setCompareTumbnails(e);
        addCompareSelect(id);
    }else{
        if (document.getElementById('compare_select_'+id).checked) {
            if (image_thumbnails.length >= 5) {
                document.getElementById('compare_select_'+id).checked = false;
                alert_modal('Your have reached maximum limit','Maximum selection for comparision has been reached, you can remove any of those selected franchises to add another.');
                return;
            }
            setCompareTumbnails(e);
            addCompareSelect(id);
        }else{
            removeCompareItem(id);
        }
    }
}

async function toggle_compare(is_tgl=true){
    var allIcons = document.querySelectorAll('.results_tile_icon');
    var compare_check = document.getElementById('compare_check');
    
    if (compare_check.checked) {
        document.getElementById('compare_div').classList.remove('hide');
        allIcons.forEach(item => {
            let id = item.getAttribute('data-id');
            item.innerHTML = `<input id="compare_select_`+id+`" class="compare_select" type="checkbox" name="compare_select">
            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 50 50">
            <path d="M 41.9375 8.625 C 41.273438 8.648438 40.664063 9 40.3125 9.5625 L 21.5 38.34375 L 9.3125 27.8125 C 8.789063 27.269531 8.003906 27.066406 7.28125 27.292969 C 6.5625 27.515625 6.027344 28.125 5.902344 28.867188 C 5.777344 29.613281 6.078125 30.363281 6.6875 30.8125 L 20.625 42.875 C 21.0625 43.246094 21.640625 43.410156 22.207031 43.328125 C 22.777344 43.242188 23.28125 42.917969 23.59375 42.4375 L 43.6875 11.75 C 44.117188 11.121094 44.152344 10.308594 43.78125 9.644531 C 43.410156 8.984375 42.695313 8.589844 41.9375 8.625 Z"></path>
            </svg>`;
            item.setAttribute('data-type','compare');
        });
        if (is_tgl) {
            await loadCompareActivity(false);
        }
    }else{
        document.getElementById('compare_div').classList.add('hide');
        allIcons.forEach(item => {
            item.innerHTML = `<svg width="800px" height="20px" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000" class="bi bi-three-dots-vertical">
                                <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                            </svg>`;
            item.setAttribute('data-type','general');
        });
    }
}

function go_compare() {
    window.location.href = absolute_path+'/compare-results';
}

async function loadCompareActivity(tgl = true) {
    // document.getElementById('loading').classList.add('show');
    const url = absolute_path + '/compare_activity.json';

    try {
        const response = await fetch(url, { method: 'GET' });
        const data = await response.json();

        // document.getElementById('loading').classList.remove('show');

        if (data && data.length > 0) {
            let is_toggle = false;
            const ids = [];

            if (tgl) {
                for (let j = 0; j < data.length; j++) {
                    const id = data[j];
                    if (typeof document.getElementById('compare_select_' + id) !== 'undefined') {
                        document.getElementById('compare_check').checked = true;
                        toggle_compare(false);
                        is_toggle = true;
                        break;
                    }
                }
            } else {
                is_toggle = true;
            }

            data.forEach(item => {
                const id = item;
                ids.push(id);
                if (document.getElementById('compare_select_' + id)) {
                    document.getElementById('compare_select_' + id).checked = true;
                }
            });

            if (is_toggle) {
                const results = await fetch_compare_main_data(ids);
                setCompareItems(results);
            }
        }
    } catch (error) {
        console.log(error);
    }
}

function moreFavorite(id,concepts_id) {
    document.getElementById('more_modal_'+concepts_id).classList.toggle('hide');
    favorites(id);
}

function cleanCompareData(name,value) {
    if (value == null) {
        return '-';
    }else if(name == 'Home Based' || name == 'Mobile Franchise'){
        if (value == 0) {
            return 'No';
        }else{
            return 'Yes';
        }
    }else if(name == 'Training Available' || name == 'Military Discount' || name == 'Financing Available'){
        if (value == 0) {
            return 'Not Available';
        }else{
            return 'Available';
        }
    }else{
        return value;
    }
}

// fetch compare concepts from myac activities table
function fetchCompareModalData() {
    let ids = document.getElementById('compare_fran_list').value;
    let compare = document.getElementById('cmp_concept_1').getAttribute('data-id');
    let compare_with = document.getElementById('cmp_concept_2').getAttribute('data-id');

    var change_id= document.querySelectorAll('.thumbnail_change_inputs:checked');
    var selected = change_id[0].getAttribute('data-id');
    var side = change_id[0].getAttribute('data-side');

    if (selected == compare) {
        compare = compare;
    }else{
        var tmp = compare_with;
        compare_with = compare;
        compare = tmp;
    }
    document.getElementById('loading').classList.add('show');
    var url = absolute_path+'/compare_list.json?concept_ids='+ids+'&compare='+compare+'&compare_with='+compare_with+'&side='+side;
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loading').classList.remove('show');
        document.getElementById('compare_change_content').innerHTML = data;
        load_favorites();
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// trigger compare modal
function compare_modal(e,action=null){
    if(action == 'close'){
        $('body').css('overflow', 'auto');
        $('.overlay_compare,#compare_modal').removeClass('show');
        var change_id= document.querySelectorAll('.thumbnail_change_inputs');
        change_id.forEach(item => {
            item.checked = false;
        });
    }else{
        document.body.style.overflow = 'hidden';
        $('.overlay_compare,#compare_modal').addClass('show');
        $('#compare_modal').css('overflow-y', 'auto');
        fetchCompareModalData();
    }
}

// Remove item from compare modal
function removeFromCompareModal(id) {
    let ids = document.getElementById('compare_fran_list').value;
    let regex = new RegExp(`(^|,)${id}(,|$)`, 'g');
    let updatedIds = ids.replace(regex, '$1').replace(/,,/g, ',').replace(/^,|,$/g, '');

    // update the ids
    document.getElementById('compare_fran_list').value = updatedIds;
    let tUrl = window.location.href.split("?")[0];
    tUrl = tUrl+'?franchises='+updatedIds;
    // history.pushState({ path: tUrl }, '', tUrl);
    // remove the id from my account activities table
    addCompareSelect(id,'remove');
    
    fetchCompareModalData();
}

// Set Comparision data
function get_summary(cmp_arr){
    let temp = '';
    for (let i = 0; i < cmp_arr.length; i++) {
        const element = cmp_arr[i];
        temp += `<div class="comparision_info">
                <div class="comparision_info_label">`+element[0]+`</div>
                <div class="comparision_info_value">`+cleanCompareData(element[0],element[1])+`</div>
                <div class="comparision_info_value">`+cleanCompareData(element[0],element[2])+`</div>
            </div>`;
    }
    document.getElementById('comparision_info_div').innerHTML = temp;
}

async function fetch_compare_main_data(img_ids=null) {
    if (img_ids) {
        var ids = img_ids;
        var filter = '&get_image=true';
    }else{
        var ids = document.querySelectorAll('.cmp_ids');
        var filter = '';
    }
    var endpoints = [];
    document.getElementById('loading').classList.add('show');
    ids.forEach(item => {
        if (img_ids) {
            var vl = item;
        }else{
            var vl = item.value;
        }
        endpoints.push(absolute_path+'/profile.json?id='+vl+filter);
    });

    try {
        const results = await Promise.all(
          endpoints.map(endpoint => fetch(endpoint).then(res => res.json()))
        );
        document.getElementById('loading').classList.remove('show');
        return results; // Return the results array
    } catch (error) {
        console.error('Error fetching data:', error);
        document.getElementById('loading').classList.remove('show');
        return []; // Return an empty array or handle the error as needed
    }
}

// Function to check if all values are null, except for the excluded key
function checkIfAllValuesAreNullOrEmptyExceptKey(obj, excludeKey) {
    return Object.keys(obj).every(key => {
        // Check if the key is the one to exclude or if its value is null or an empty string
        return key === excludeKey || obj[key] === null || obj[key] === "";
    });
}

// Get comparision data
async function get_compare_summary() {
    let ids = document.querySelectorAll('.cmp_ids');
    // document.getElementById('loading').classList.add('show');
    let labels = {address: 'HQ Location',year_founded: 'Year Founded',franchising_since: 'Franchising Since', new_franchise_units: 'Number of Units',
        home_based: 'Home Based', is_mobile_franchise: 'Mobile Franchise', is_training_available: 'Training Available', new_investment: 'Minimum Cash Required',
        new_franchise_fee: 'Franchise Fee', total_investment: 'Total Investment', military_discount: 'Military Discount', financing_available: 'Financing Available'
    }

    let cmp_arr = [];
    for (const [index, [key, value]] of Object.entries(Object.entries(labels))) {
        cmp_arr[index] = [value];
    }

    const results = await fetch_compare_main_data();

    if (results && results.length > 0) {
        const excludeKey = 'image_url';

        const result1 = checkIfAllValuesAreNullOrEmptyExceptKey(results[0], excludeKey);
        const result2 = checkIfAllValuesAreNullOrEmptyExceptKey(results[1], excludeKey);

        
        for (let i = 0; i < results.length; i++) {
            var concept_id = ids[i].value;
            var data = results[i];
            if (i == 0) {
                cmp_id = 'cmp_concept_1';
                side = 'left';
            }else if (i==1){
                cmp_id = 'cmp_concept_2';
                side = 'right';
            }
            
            let temp = `<div id="`+cmp_id+`" data-id="`+concept_id+`" class="cmp_thumbnail_item">
                <div class="cmp_thumbnail_title">
                    <h3>`+data['name']+`</h3>
                    <p>`+data['category_name']+`</p>
                </div>
                <div class="cmp_thumbnail_change">
                    <input class="thumbnail_change_inputs" type="checkbox" data-side="`+side+`" data-id="`+concept_id+`" onchange="compare_modal(this,'open')">
                    <p class="change_icon">Change</p>
                </div>
                <div class="cmp_thumbnail_img">
                    <img src="`+data['image_url']+`" alt="">
                    <div class="listing_favorites">
                        <input type="checkbox" class="favorites" data-fboid="`+data['fbo_id']+`" onclick="favorites(`+data['fbo_id']+`);">
                    </div>
                </div>
            </div>
            <div class="cmp_thumbnail_button"><button data-page="compare" onclick="view_summary(this,`+concept_id+`);">View More<span class="hide-sm">&nbsp;Details</span></button></div>`;
            document.getElementById('cmp_thumbnail_'+(i+1)).innerHTML = temp;
    
            
            if (result1 == false || result2 == false) {
                for (const [index, [key, value]] of Object.entries(Object.entries(labels))) {
                    cmp_arr[index][i+1] = data[key];
                }
                get_summary(cmp_arr);
            }else{
                document.getElementById('comparision_info_div').innerHTML = 'Not enough data to compare, change to other concepts to compare';
            }
        }
        load_favorites();
    }
}

function select_new_compare_name(id,id2,side) {
    var change_list_ids = document.querySelectorAll('.change_list_ids');
    change_list_ids.forEach(item => {
        if (item.getAttribute('data-id') == id) {
            item.checked = 'checked';
        }
    });
    select_new_compare(id,id2,side);
}

function select_new_compare(id,id2,side) {
    var compareItems = document.querySelectorAll('.compare_change_item');
    compareItems.forEach(item => {
        if (!item.classList.contains('shadow')) {
            var tid = item.getAttribute('data-id');
            document.getElementById('delete_'+tid).classList.remove('no_show');
        }
    });
    document.getElementById('delete_'+id).classList.add('no_show');

    if (side == 'left') {
        document.getElementById('cmp_id_1').value = id;
        document.getElementById('cmp_id_2').value = id2;
    }else{
        document.getElementById('cmp_id_1').value = id2;
        document.getElementById('cmp_id_2').value = id;
    }

    var change_id= document.querySelectorAll('.thumbnail_change_inputs');
    change_id.forEach(item => {
        item.checked = false;
    });
    document.querySelector('.compare_page').classList.remove('cmp_scroll');
    window.scrollTo(0, 0);
    get_compare_summary();
    
}

// Compare modal button click
function compare_change_selected() {
    compare_modal(this,'close');
}

//Bing Defaults
function bing_defaults(){
    window.uetq=window.uetq||[];
    if (typeof uetq !== 'undefined') {
        window.uetq.push('set', { 'pid': {
            'em': getCookie('email') ? getCookie('email') : '',
            'ph': getCookie('phone') ? getCookie('phone') : ''
        }});
    }
}

//Bing Events (single item)
function bing_event(event,price = null){
    if (typeof uetq !== 'undefined') {
        if(event == 'purchase'){
            window.uetq.push('event','', {'revenue_value': price, 'currency': 'USD'});
        }else{
            window.uetq.push('event', event, {});

        }
    }
}

if (typeof page != 'undefined' && page == 'compare-results') {    
    var scrollBefore = 0;
    window.addEventListener('scroll',function(e){
        const scrolled = window.scrollY;
        if(scrollBefore > scrolled){
            //ScrollUP
            scrollBefore = scrolled;
            document.querySelector('.compare_page').classList.remove('cmp_scroll');
        }else{
            scrollBefore = scrolled;
            document.querySelector('.compare_page').classList.add('cmp_scroll');
        }
    })
}

// Net Worth Calculator
function calculator_modal(){
    $('.overlay,#menu_modal').removeClass('show');
    $('#calculator_modal').addClass('show');
    // appendIdUrl('#'+page+'#net_worth_calculator');
    modal_backbutton('#calculator_modal');
}

//Net Worth Calculator Sending data  
$('#see_franchise_button').on("click",function(e){
    e.preventDefault();
    const data={};
    //Getting values from input fields and storing in data object above line
    $('.box input').each(function(){
        const key=$(this).data("key");
        const value=parseFloat($(this).val().replace('$','').replace(',',''));
        data[key]=value;
    })
    if (data['net_worth'] == 0) {
        alert('Net Worth cannot be "0"');
        return;
    }
    fetch(absolute_path+"/net_worth_save.json", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => 
        document.getElementById('calculator_form').submit()
    )
    .catch(error => 
        console.error("Error:", error)
    );
});