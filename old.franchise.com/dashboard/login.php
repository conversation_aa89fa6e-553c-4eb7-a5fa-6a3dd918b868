<?php

    include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global.php');
    $page = @$_GET['section'];

    $openMessageCenter = '';
    if (isset($_GET['openMessageCenter'])) {
        $openMessageCenter = 'yes';
        login_external();
    }

    if (isset($_SESSION['myac_login_contact_id'])) {
        if (in_array($page,['login','signup'])) {
            if ($openMessageCenter) {
                header('Location: /dashboard/?openMessageCenter=true');
            }else{
                header('Location: /dashboard/');
            }
        }
    }else if(!isset($_SESSION['myac_login_contact_id'])){
        if (in_array($page,['signup'])) {
            if(isset($_GET['signup'])){
                if($_GET['signup'] == 2 && !isset($_POST['email'])){
                    header('Location: '.$dashboard.'/signup?signup=1');
                    echo 'asdasd';
                    exit;
                }
            }
        }
    }

    // Return Modal Page Name
    function modal_name(){
        global $page;
        if($page == 'login'){
            return 'Sign In';
        }else if($page == 'signup'){
            return 'Sign Up';
        }else if($page == 'forgot_password'){
            return 'Forgot Password';
        }else{
            return 'Page';
        }
    }

    // Create Shells for Different Pages
    function create_shell(){
        global $page, $signin, $signup, $forgot, $update, $openMessageCenter;
        $signin = 1;
        $signup = $forgot = $update = null;
        if(isset($_GET['signin'])){
            if(is_numeric($_GET['signin'])){
                $signin = $_GET['signin'];
            }
        }else if(isset($_GET['signup'])){
            if(is_numeric($_GET['signup'])){
                $signup = $_GET['signup'];
                $signin = null;
            }
        }else if(isset($_GET['forgot'])){
            if($_GET['forgot'] == 'password'){
                $forgot = 'password';
                $signin = null;
            }
        }else if(isset($_GET['reset'])){
            if($_GET['reset'] == 'password'){
                $update = 'password';
                $signin = null;
            }
        }
        if($page == 'signup'){
            echo '<main id="signup_main">
                    <div class="promo">
                        <div class="logo text-center">
                            <img src="/images/beta_logo.svg" /> 
                        </div>
                        <h2>Empowering Your<br>Franchise Journey</h2>
                    </div>
                    <div id="login">';
                        include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/login_pages/'.$page.'_form.php');
                        include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/login_pages/login_footer.php');
            echo    '
            </div>
                </main>';
        }else if($page == 'login1234'){ //remove '1234' to work
            echo '<main id="login_main">
                    <div id="login">';
                        include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/login_pages/'.$page.'_form.php');
            echo    '</div>
                    <div class="promo">
                        <h1>My Account Login</h1>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut ultrices vestibulum felis eu fermentum. Praesent pharetra massa ut enim rhoncus, suscipit volutpat turpis efficitur. Donec feugiat aliquam purus sit amet sollicitudin. Nunc semper auctor diam eu faucibus. Aliquam porta fringilla eros, at aliquet sem consectetur nec. Maecenas efficitur consectetur dolor quis iaculis. Fusce placerat, felis et ultricies viverra, felis tellus pulvinar sapien, viverra accumsan nisl neque et mi. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                    </div>
                </main>';
        }else{
            echo '<main id="other_main">
                    <div id="login">
            ';
            include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/login_pages/'.$page.'_form.php');
            include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/login_pages/login_footer.php');
            echo '</div></main>';
        }
    }
?>
<!doctype html>
<html>
    <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/head.php'); ?>
    <body data-page="<?php echo modal_name(); ?>">
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/body.php'); ?>
        <?php create_shell(); ?>
        <div id="alert">
            <div class="alert">
                <div class="content"></div>
            </div>
        </div>
        <div id="loading">
            <div> 
                <svg fill="#fff" class="fa-pulse" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512">
                    <path d="M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"></path>
                </svg>
            </div>
        </div>
        <div id="feedback_popup" class="popup">
            <div class="guts">
                <div class="content">
                    <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/json/feedback_form.php'); ?>
                </div>
            </div>
        </div>
        <div id="privacy_modal" class="modal">
            <div class="guts">
                <div class="modal_banner">
                    <div class="close_modal" onclick="modal('privacy_modal','close');">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
                    </div>
                    <div></div>
                </div>
                <div class="content terms"><?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/privacy_policy.php'); ?></div>
            </div>
        </div>
        <div id="terms_modal" class="modal">
            <div class="guts">
                <div class="modal_banner">
                    <div class="close_modal" onclick="modal('terms_modal','close');">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
                    </div>
                    <div></div>
                </div>
                <div class="content terms"><?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/terms_conditions.php'); ?></div>
            </div>
        </div>
        <script>
            var page = "<?php echo $page; ?>";
        </script>
        <script src="/dashboard/js/global.js?random=<?php echo $random;?>"></script>
        <script src="/dashboard/js/login.js?random=<?php echo $random;?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({ event: 'beta_ga4' });
        </script>
    </body>
</html>