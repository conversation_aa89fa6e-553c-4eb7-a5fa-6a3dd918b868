<?php
    $title = ucwords($page).' | My Account';
?>
<head>
    <meta charset="utf-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="minimal-ui, width=device-width, initial-scale=1, minimum-scale=1.0, maximum-scale=1.0">

    <title><?php echo @$title; ?></title>
    <meta name="description" content="{description}">

    <link rel="apple-touch-icon" sizes="180x180" href="https://franchise-ventures-general.s3.amazonaws.com/cdn_myac/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://franchise-ventures-general.s3.amazonaws.com/cdn_myac/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://franchise-ventures-general.s3.amazonaws.com/cdn_myac/icons/favicon-16x16.png">
    <link rel="manifest" href="/dashboard/site.webmanifest">
    <link rel="mask-icon" href="https://franchise-ventures-general.s3.amazonaws.com/cdn_myac/icons/safari-pinned-tab.svg" color="#5bbad5">
    <link rel="shortcut icon" href="https://franchise-ventures-general.s3.amazonaws.com/cdn_myac/icons/favicon.ico">
    <meta name="msapplication-TileColor" content="#314972">
    <meta name="msapplication-config" content="/dashboard/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/dashboard/css/root.css?random=<?php echo $random;?>" type="text/css" >
    <link rel="stylesheet" href="/dashboard/css/svg.css?random=<?php echo $random;?>" type="text/css" >
    <link rel="stylesheet" href="/dashboard/css/mobile.css?random=<?php echo $random;?>" type="text/css" >
    <link rel="stylesheet" href="/dashboard/css/tablet.css?random=<?php echo $random;?>" type="text/css" media="screen and (min-width: 48rem)">
    <link rel="stylesheet" href="/dashboard/css/desktop.css?random=<?php echo $random;?>" type="text/css" media="screen and (min-width: 64rem)">
    <link rel="stylesheet" href="/dashboard/css/multiselect-dropdown.css?random=<?php echo $random;?>" type="text/css" >
    <?php if(isset($_GET['login_screens'])){ ?>
        <link rel="stylesheet" href="/dashboard/css/login.css?random=<?php echo $random;?>" type="text/css" >
    <?php } ?>
    <?php if(isset($_GET['showsynd']) && ($_GET['showsynd'] == 'yes')){ ?>
        <link rel="stylesheet" href="/dashboard/css/demo.css?random=<?php echo $random;?>" type="text/css" >
    <?php } ?>
    <script type="text/javascript" src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>	
    <script type="text/javascript" src="//code.jquery.com/ui/1.9.2/jquery-ui.js"></script>
    <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    <?php if($not_dev){ ?>
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-T3J9HJS');</script>
        <!-- End Google Tag Manager -->
    <?php } ?>
    <?php echo load_quiz('26',false); ?>
    <?php echo load_profile_article(); ?>
</head>