<header id="header">
    <div class="header_left">
        <div class="logo">
            <a href="/dashboard/">
                <img src="/dashboard/images/beta_logo.svg" />
            </a>
        </div>
        <div class="desktop_nav">
            <?php echo aside_ul(false); ?> 
        </div>
    </div>
    <nav id="header_nav">
        <div class="notifications_nav" data-count="0" onclick="modal('notifications_modal');">
        <!-- If we need to make notifications as a separate page replace with the below line -->
        <!-- <div class="notifications_nav" data-count="0" onclick="window.location='/notifications';"> -->
            <i class="bell"></i>
        </div>
        <div class="favorites_nav" data-count="0" onclick="window.location='/dashboard/favorites';">
            <i class="heart"></i>
        </div>
        <div class="menu" onclick="menu_modal();">
            <svg clip-rule="evenodd" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="m11 16.745c0-.414.336-.75.75-.75h9.5c.414 0 .75.336.75.75s-.336.75-.75.75h-9.5c-.414 0-.75-.336-.75-.75zm-9-5c0-.414.336-.75.75-.75h18.5c.414 0 .75.336.75.75s-.336.75-.75.75h-18.5c-.414 0-.75-.336-.75-.75zm4-5c0-.414.336-.75.75-.75h14.5c.414 0 .75.336.75.75s-.336.75-.75.75h-14.5c-.414 0-.75-.336-.75-.75z" fill-rule="nonzero"/></svg>
        </div>
    </nav>
</header>