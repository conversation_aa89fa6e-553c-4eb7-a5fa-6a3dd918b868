<?php
    if($_SESSION['myac_first_name'] == 'first_name'){
        $_SESSION['myac_first_name'] = '';
    }
    if($_SESSION['myac_last_name'] == 'last_name'){
        $_SESSION['myac_last_name'] = '';
    }
    // Create Icons
    $menu_variables = [
        'avatar' => strtoupper(substr($_SESSION['myac_first_name'], 0, 1)).strtoupper(substr($_SESSION['myac_last_name'], 0, 1)),
        'name' => $_SESSION['myac_first_name'].' '.$_SESSION['myac_last_name']
    ];
    $notification_tabs = [
        [
            'id' => '0',
            'before' => null,
            'mobile' => 'Unread',
            'after' => null
        ],
        [
            'id' => '1',
            'before' => null,
            'mobile' => 'Read',
            'after' => null
        ]
    ];
?>
<div id="alert">
    <div class="alert">
        <div class="content"></div>
    </div>
</div>
<div id="terms_modal" class="modal">
    <div class="guts">
        <div class="modal_banner">
            <div class="close_modal" onclick="modal('terms_modal','close');">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
            </div>
            <div></div>
        </div>
        <div class="content terms"><?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/terms_conditions.php'); ?></div>
    </div>
</div>
<div id="notifications_modal" class="modal">
    <div class="guts">
        <div class="modal_banner">
            <div class="close_modal" onclick="modal('notifications_modal','close');">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
            </div>
            <div></div>
        </div>
        <div class="content">
            <h3>Messaging Center</h3>
            <!-- <div class="notification_list_block"><?php echo tabs($notification_tabs,'notification_tabs') ?></div> -->
            <div id="notifications"></div>
        </div>
    </div>
</div>
<div class="overlay" onclick="menu_modal('close');"></div>
<div id="menu_modal">
    <div class="guts">
        <div class="content">
            <header class="menu_header">
                <div class="avatar"><?php echo $menu_variables['avatar']; ?></div>
                <div class="name"><?php echo $menu_variables['name']; ?></div>
                <div class="email"><?php echo @$_SESSION['myac_email']; ?></div>
            </header>
            <ul>
                <li><a href="/dashboard/saved"><svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M5 0v24l7-6 7 6v-24h-14zm1 1h12v20.827l-6-5.144-6 5.144v-20.827z"/></svg>Saved Articles</a></li>
                <li><a href="/dashboard/requests"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M17 3v-2c0-.552.447-1 1-1s1 .448 1 1v2c0 .552-.447 1-1 1s-1-.448-1-1zm-12 1c.553 0 1-.448 1-1v-2c0-.552-.447-1-1-1-.553 0-1 .448-1 1v2c0 .552.447 1 1 1zm13 13v-3h-1v4h3v-1h-2zm-5 .5c0 2.481 2.019 4.5 4.5 4.5s4.5-2.019 4.5-4.5-2.019-4.5-4.5-4.5-4.5 2.019-4.5 4.5zm11 0c0 3.59-2.91 6.5-6.5 6.5s-6.5-2.91-6.5-6.5 2.91-6.5 6.5-6.5 6.5 2.91 6.5 6.5zm-14.237 3.5h-7.763v-13h19v1.763c.727.33 1.399.757 2 1.268v-9.031h-3v1c0 1.316-1.278 2.339-2.658 1.894-.831-.268-1.342-1.111-1.342-1.984v-.91h-9v1c0 1.316-1.278 2.339-2.658 1.894-.831-.268-1.342-1.111-1.342-1.984v-.91h-3v21h11.031c-.511-.601-.938-1.273-1.268-2z"/></svg>Request History</a></li>
                <li><a href="/dashboard/recent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M24 12c0 6.627-5.373 12-12 12s-12-5.373-12-12h2c0 5.514 4.486 10 10 10s10-4.486 10-10-4.486-10-10-10c-2.777 0-5.287 1.141-7.099 2.977l2.061 2.061-6.962 1.354 1.305-7.013 2.179 2.18c2.172-2.196 5.182-3.559 8.516-3.559 6.627 0 12 5.373 12 12zm-13-6v8h7v-2h-5v-6h-2z"/></svg>Recently Viewed</a></li>
                <li><a href="javascript:;" onclick="settings_modal();"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M24 14.187v-4.374c-2.148-.766-2.726-.802-3.027-1.529-.303-.729.083-1.169 1.059-3.223l-3.093-3.093c-2.026.963-2.488 1.364-3.224 1.059-.727-.302-.768-.889-1.527-3.027h-4.375c-.764 2.144-.8 2.725-1.529 3.027-.752.313-1.203-.1-3.223-1.059l-3.093 3.093c.977 2.055 1.362 2.493 1.059 3.224-.302.727-.881.764-3.027 1.528v4.375c2.139.76 2.725.8 3.027 1.528.304.734-.081 1.167-1.059 3.223l3.093 3.093c1.999-.95 2.47-1.373 3.223-1.059.728.302.764.88 1.529 3.027h4.374c.758-2.131.799-2.723 1.537-3.031.745-.308 1.186.099 3.215 1.062l3.093-3.093c-.975-2.05-1.362-2.492-1.059-3.223.3-.726.88-.763 3.027-1.528zm-4.875.764c-.577 1.394-.068 2.458.488 3.578l-1.084 1.084c-1.093-.543-2.161-1.076-3.573-.49-1.396.581-1.79 1.693-2.188 2.877h-1.534c-.398-1.185-.791-2.297-2.183-2.875-1.419-.588-2.507-.045-3.579.488l-1.083-1.084c.557-1.118 1.066-2.18.487-3.58-.579-1.391-1.691-1.784-2.876-2.182v-1.533c1.185-.398 2.297-.791 2.875-2.184.578-1.394.068-2.459-.488-3.579l1.084-1.084c1.082.538 2.162 1.077 3.58.488 1.392-.577 1.785-1.69 2.183-2.875h1.534c.398 1.185.792 2.297 2.184 2.875 1.419.588 2.506.045 3.579-.488l1.084 1.084c-.556 1.121-1.065 2.187-.488 3.58.577 1.391 1.689 1.784 2.875 2.183v1.534c-1.188.398-2.302.791-2.877 2.183zm-7.125-5.951c1.654 0 3 1.346 3 3s-1.346 3-3 3-3-1.346-3-3 1.346-3 3-3zm0-2c-2.762 0-5 2.238-5 5s2.238 5 5 5 5-2.238 5-5-2.238-5-5-5z"/></svg>Settings</a></li>
                <li><a href="javascript:;" onclick="modal('feedback_popup');"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 3c5.514 0 10 3.592 10 8.007 0 4.917-5.145 7.961-9.91 7.961-1.937 0-3.383-.397-4.394-.644-1 .613-1.595 1.037-4.272 1.82.535-1.373.723-2.748.602-4.265-.838-1-2.025-2.4-2.025-4.872-.001-4.415 4.485-8.007 9.999-8.007zm0-2c-6.338 0-12 4.226-12 10.007 0 2.05.738 4.063 2.047 5.625.055 1.83-1.023 4.456-1.993 6.368 2.602-.47 6.301-1.508 7.978-2.536 1.418.345 2.775.503 4.059.503 7.084 0 11.91-4.837 11.91-9.961-.001-5.811-5.702-10.006-12.001-10.006zm1.024 13.975c0 .566-.458 1.025-1.024 1.025-.565 0-1.024-.459-1.024-1.025 0-.565.459-1.024 1.024-1.024.566 0 1.024.459 1.024 1.024zm1.141-8.192c-.498-.505-1.241-.783-2.09-.783-1.786 0-2.941 1.271-2.941 3.237h1.647c0-1.217.68-1.649 1.261-1.649.519 0 1.07.345 1.117 1.004.052.694-.319 1.046-.788 1.493-1.157 1.1-1.179 1.633-1.173 2.842h1.643c-.01-.544.025-.986.766-1.785.555-.598 1.245-1.342 1.259-2.477.008-.758-.233-1.409-.701-1.882z"/></svg>Help &amp; Feedback</a></li>
                <li><a href="javascript:;" onclick="modal('privacy_modal');"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M18 10v-4c0-3.313-2.687-6-6-6s-6 2.687-6 6v4h-3v14h18v-14h-3zm-10-4c0-2.206 1.795-4 4-4s4 1.794 4 4v4h-8v-4zm11 16h-14v-10h14v10zm-7.737-2l-2.843-2.756 1.173-1.173 1.67 1.583 3.563-3.654 1.174 1.173-4.737 4.827z"/></svg>Privacy Policy</a></li>
                <li><a href="javascript:;" onclick="calculator_modal();"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">!Font Awesome Free 6.7.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2024 Fonticons, Inc.<path d="M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-384c0-35.3-28.7-64-64-64L64 0zM96 64l192 0c17.7 0 32 14.3 32 32l0 32c0 17.7-14.3 32-32 32L96 160c-17.7 0-32-14.3-32-32l0-32c0-17.7 14.3-32 32-32zm32 160a32 32 0 1 1 -64 0 32 32 0 1 1 64 0zM96 352a32 32 0 1 1 0-64 32 32 0 1 1 0 64zM64 416c0-17.7 14.3-32 32-32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-96 0c-17.7 0-32-14.3-32-32zM192 256a32 32 0 1 1 0-64 32 32 0 1 1 0 64zm32 64a32 32 0 1 1 -64 0 32 32 0 1 1 64 0zm64-64a32 32 0 1 1 0-64 32 32 0 1 1 0 64zm32 64a32 32 0 1 1 -64 0 32 32 0 1 1 64 0zM288 448a32 32 0 1 1 0-64 32 32 0 1 1 0 64z"/></svg>Net Worth Calculator</a></li>
                <?php /*<li><a href="javascript:;" onclick="modal('terms_modal');"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M7 22v-16h14v7.543c0 4.107-6 2.457-6 2.457s1.518 6-2.638 6h-5.362zm16-7.614v-10.386h-18v20h8.189c3.163 0 9.811-7.223 9.811-9.614zm-10 1.614h-4v-1h4v1zm6-4h-10v1h10v-1zm0-3h-10v1h10v-1zm1-7h-17v19h-2v-21h19v2z"/></svg>Terms &amp; Conditions</a></li>*/ ?>
            </ul>
            <a href="/dashboard/logout.php" class="button solid">Logout</a>
        </div>
    </div>
</div>
<div id="profile_modal" class="modal">
    <div class="guts">
        <div class="modal_banner">
            <div class="close_modal" onclick="modal('profile_modal','close');">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/></svg>
            </div>
            <div id="listing_favorites">
                <span>&nbsp;Favorites</span>
                <div class="listing_favorites"></div>
            </div>
        </div>
        <div class="profile_scrolltop">
            <span>back to top</span>
        </div>
        <div class="content"></div>
        <div class="profile_footer_button"></div>
    </div>
</div>
<?php if(!isset($_SESSION['welcome_survey']) || (isset($_SESSION['welcome_survey']) && $_SESSION['welcome_survey'] == 0)){?>
<div id="survey_modal" class="modal">
    <div class="guts">
        <div class="close_btn" onclick="modal('survey_modal','close');">&times;</div>
        <div class="content"><?php echo load_quiz('26'); ?></div>
    </div>
</div>
<?php } ?>
<div id="quiz_modal" class="modal">
    <div class="guts">
        <div class="close_btn" onclick="modal('quiz_modal','close');">&times;</div>
        <div class="content"><?php echo load_quiz('27'); ?></div>
    </div>
</div>
<div id="article_modal" class="modal"></div>
<div id="loading">
    <div> 
        <svg fill="#fff" class="fa-pulse" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512">
            <path d="M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"></path>
        </svg>
    </div>
</div>
<div id="notification_popup" class="popup">
    <div class="guts">
        <div class="content"></div>
    </div>
</div>
<div id="feedback_popup" class="popup">
    <div class="guts">
        <div class="content">
            <?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/json/feedback_form.php'); ?>
        </div>
    </div>
</div>
<div id="welcome_popup" class="popup <?php echo isset($_SESSION['welcome']) ? 'show' : ''; ?>">
    <div class="guts">
        <div class="content">
            <div class="message">
                <p class="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="#1fb141" x="0px" y="0px" width="60" height="60" viewBox="0 0 30 30">
                        <path d="M15,3C8.373,3,3,8.373,3,15c0,6.627,5.373,12,12,12s12-5.373,12-12C27,8.373,21.627,3,15,3z M21.707,12.707l-7.56,7.56 c-0.188,0.188-0.442,0.293-0.707,0.293s-0.52-0.105-0.707-0.293l-3.453-3.453c-0.391-0.391-0.391-1.023,0-1.414s1.023-0.391,1.414,0 l2.746,2.746l6.853-6.853c0.391-0.391,1.023-0.391,1.414,0S22.098,12.316,21.707,12.707z"></path>
                    </svg>
                </p>
                <h2>Welcome, <?php echo ($_SESSION['myac_first_name'].' '.$_SESSION['myac_last_name']); ?></h2>
                <p class="text-center">Your account has been successfully verified.</p>
            </div>
            <div class="row">
                <div class="row_item buttons">
                    <button class="button cancel" onclick="welcomeModal();">Get Started</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="request_popup" class="popup">
    <div class="alert">
        <div class="content"></div>
    </div>
</div>
<div id="settings_modal" class="modal">
    <div class="guts">
        <div class="modal_banner">
            <div class="close_modal" onclick="modal('settings_modal','close');">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/></svg>
            </div>
        </div>
        <div class="content"></div>
    </div>
</div>
<div id="privacy_modal" class="modal">
    <div class="guts">
        <div class="modal_banner">
            <div class="close_modal" onclick="modal('privacy_modal','close');">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"></path></svg>
            </div>
            <div></div>
        </div>
        <div class="content terms"><?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/privacy_policy.php'); ?></div>
    </div>
</div>

<div id="calculator_modal" class="modal">
    <div class="guts">
        <div class="modal_banner">
            <div class="close_modal" onclick="modal('calculator_modal','close');">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/></svg>
            </div>
        </div>
        <div class="content terms"><?php include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/pages/net_worth_calculator.php'); ?></div>
    </div>
</div>

<div id="expired_popup" class="popup">
    <div class="guts">
        <div class="content">
            <h3>Your Session Has Expired</h3>
            <p>Please log back into your account by clicking the link below.</p>
            <p>&nbsp;</p>
            <p class="text-center"><a href="/dashboard/logout.php" class="button solid">Click Here</a></p>
        </div>
    </div>
</div>
<div class="overlay_compare" onclick="compare_modal(this,'close');"></div>
<div id="compare_modal">
    <div class="guts">
        <div class="content">
            <p>Select a Franchise</p>
            <div id="compare_change_content" class="compare_change_content">Loading...</div>
        </div>
    </div>
</div>