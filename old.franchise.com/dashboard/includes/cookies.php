<?php
// Initial Cookie Variables and UDID
$host = $_SERVER['HTTP_HOST'];
preg_match("/[^\.\/]+\.[^\.\/]+$/", $host, $matches);
$isSecure = false;
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') {
    $isSecure = true;
}else if(!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https' || !empty($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] == 'on'){
    $isSecure = true;
}
if (!isset($_COOKIE['udid'])) {
    $udid = uniqid(rand(), true);
    setcookie('udid',$udid, time() + (10 * 365 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 10 years	
} else {
    $udid = htmlspecialchars($_COOKIE['udid'], ENT_QUOTES);
}

//Checking to see if visitor already has UTM variables set
$campaign_check = ['utm_medium','utm_source','utm_campaign','utm_type','gclid','mlclkid','msclkid','fbclid','wbraid'];
$current_get = array_keys($_GET);
$intersect = array_intersect($campaign_check, $current_get);

if(!empty($intersect)){
    //declaring the UTM cookie arrays
    $fv_campaign = array();
    $gclid_array = ['gclid','mlclkid','msclkid','fbclid','wbraid'];

    //Clearing existing cookie
    if (isset($_COOKIE['fv_campaign'])) {
        unset($_COOKIE['fv_campaign']); 
        setcookie('fv_campaign', '', time() - 3600, '/'); 
    }
    foreach ($campaign_check as $key => $value) {
        if (isset($_GET[$value])) {
            $$value = htmlspecialchars($_GET[$value]);
            $fv_campaign[$value] = $_GET[$value];
        }
    }
    foreach ($gclid_array as $key => $value) {
        if (isset($_GET[$value])) {
            $gclid_mlclkid = htmlspecialchars($_GET[$value]);
            $fv_campaign['gclid_mlclkid'] = $_GET[$value];
        }
    }
    setcookie('fv_campaign',json_encode($fv_campaign), time() + (1 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 1 day
}else{
    if(isset($_COOKIE['fv_campaign'])){
        $temp_fv_campaign = json_decode($_COOKIE['fv_campaign'],true);
        foreach ($temp_fv_campaign as $key => $value) {
            $$key = htmlspecialchars($value, ENT_QUOTES);
        }        
    }
}

//Get Visitor's IP Address
if(isset($_SERVER['HTTP_CF_CONNECTING_IP'])){
    $ipAddress = $_SERVER['HTTP_CF_CONNECTING_IP'];
}else if(isset($_SERVER['HTTP_X_FORWARDED_FOR'])){
    $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
}else{
    $ipAddress = $_SERVER['REMOTE_ADDR'];
}
$temp_ipAddress_array = explode(',',$ipAddress);
$ipAddress = $temp_ipAddress_array[0];
if(substr_count($ipAddress, ':') == 1){
    $temp_ipAddress_array = explode(':',$ipAddress);
    $ipAddress = $temp_ipAddress_array[0];
}
if (!isset($_COOKIE['ip_address']) || $_COOKIE['ip_address'] != $ipAddress) {
    setcookie('ip_address',$ipAddress, time() + (10 * 365 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 10 years	
} 
setcookie('quiz_udid',$_SESSION['myac_login_contact_id'] ?? '', time() + (10 * 365 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 10 years	

// SESSION variables
$cookie_array = ['first_name','last_name','email','phone'];
foreach ($cookie_array as $key => $value) {
    $temp_session_name = 'myac_'.$value;
    if (!isset($_COOKIE[$value])) {
        if(isset($_SESSION[$temp_session_name])){
            $$value = htmlspecialchars($_SESSION[$temp_session_name]);
            setcookie($value,$_SESSION[$temp_session_name], time() + (1 * 365 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 1 years	
        }
    }else{
        if(isset($_SESSION[$temp_session_name])){
            setcookie($value,$_SESSION[$temp_session_name], time() + (1 * 365 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 1 years
            $$value = $_SESSION[$temp_session_name];   	            
        }else{
            $$value = $_COOKIE[$value];   
        }       
    }
}
if(isset($_GET['state_name'])){
    setcookie('state_name',$_GET['state_name'], time() + (1 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 30 days	        
}

//print_r($_SESSION);
if (isset($_GET['healthcheck']) || (isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/bot|crawl|curl|go-http-client|google|lighthouse|mediapartners|owler|site24x7|siteuptime|slurp|spider|trident/i', strtolower($_SERVER['HTTP_USER_AGENT']))) || isset($_GET['blog_include'])) {
    $geo = ['city'=>'corona','state'=>'ca','country'=>'us','country3'=>'usa','continent'=>6];
    setcookie('geo',json_encode($geo), time() + (1 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 1 day
}else if (isset($geo_ignore)) {
    //DO NOTHING
}else if (isset($_COOKIE['geo'])) {
    $geo = json_decode($_COOKIE['geo'],true);
}else{
    $geo = [];
    $netacuity = 'https://location.cloud.netacuity.com/webservice/query?u=66f61e33-5ced-48aa-bb1d-b159316cc9e7&ip=' . $ipAddress . '&dbs=all&trans_id=ou812v&json=true';
    if(file_get_contents($netacuity)){
        $geoLocation = file_get_contents($netacuity);
        $geoLocation = json_decode($geoLocation, true); 
        track_netacuity(4,$netacuity,$geoLocation);
        $geo['city'] = $geoLocation['response']['pulse-city'];
        $geo['state'] = $geoLocation['response']['pulse-region'];
        $geo['country'] = $geoLocation['response']['pulse-two-letter-country'];
        $geo['country3'] = $geoLocation['response']['pulse-country'];
        $geo['continent'] = $geoLocation['response']['pulse-continent-code'];
        setcookie('geo',json_encode($geo), time() + (1 * 24 * 60 * 60), "/", $matches[0] , $isSecure, false); //Expires in 1 day
    }
}
