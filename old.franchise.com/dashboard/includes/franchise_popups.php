<div id="filter_popup" class="popup">
    <div class="guts">
        <div class="content">
            <form id="franchise_filter2" method="post" onchange="check_count('#franchise_filter2');">
                <input type="hidden" id="filtered2" name="filtered" value="default">
                <input type="hidden" id="min2" name="min">
                <input type="hidden" id="max2" name="max">
                <input type="hidden" id="units_min2" name="units_min">
                <input type="hidden" id="units_max2" name="units_max"> 
                <input type="hidden" id="net_worth_min2" name="net_worth_min">
                <input type="hidden" id="net_worth_max2" name="net_worth_max">
                <div class="row">
                    <label>Industry</label>
                    <div class="row_item">
                        <?php echo select($categories,'cat_id',@$_POST['cat_id'],'cat_id',true); ?>
                    </div>
                </div>
                <div class="row">
                    <label>State</label>
                    <div class="row_item">
                        <?php echo select($states,'state_code',@$_POST['state_code'],'state'); ?>
                    </div>
                </div>
                <div class="row">
                    <label>Minimum Cash Required<input type="text" name="investment" id="investment_amount2" readonly disabled="disabled"></label>
                    <div class="row_item">
                        <div id="investment_range2"></div>
                    </div>
                </div>
                <div class="row">
                    <label>Net Worth Required<input type="text" name="min_net_worth" id="net_worth_amount2" readonly disabled="disabled"></label>
                    <div class="row_item">
                        <div id="min_net_worth2"></div>
                    </div>
                </div>
                <div class="row_item buttons">
                    <button type="button" class="filter_nw_btn button" onclick="calculator_modal();">Calculate your Net Worth</button>
                </div>
                <div class="row">
                    <label>Franchising Since</label>
                    <div class="row_item">
                        <input type="text" id="franchising_since2" name="franchising_since" min="1880" max="2024" maxlength="4" pattern="[0-9]{4}"  placeholder="Ex: 1980" value="<?php echo @$_POST['franchising_since']; ?>" oninput="this.value=formatNumber(this.value);" />
                    </div>
                </div>
                <div class="row">
                    <div class="row_item">
                        <?php echo radio_checkbox('checkbox','military_discount','1','Veteran Discount',@$_POST['military_discount']); ?>
                        <?php echo radio_checkbox('checkbox','home_based','1','Home-based',@$_POST['home_based']); ?>
                    </div>
                </div>
                <div class="row">
                    <label>Number of Existing Units<input type="text" name="franchise_units" id="franchise_units_amount2" readonly disabled="disabled"></label>
                    <div class="row_item">
                        <div id="franchise_units2"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="row_item buttons">
                        <button class="button cancel" type="button" onclick="modal('filter_popup','close');">Cancel</button>
                        <button class="button solid" type="submit" data-count="<?php echo $count; ?>">See <span class="franchise_count"><?php echo $count; ?></span> Result</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="sort_popup" class="popup">
    <div class="guts">
        <div class="content">
            <h3>Sort By</h3>
            <div class="row">
                <label>Your Recommended Order</label>
                <div class="row_item">
                    <?php echo radio_checkbox('radio','sort','default','Not Sorted',true); ?>
                </div>
            </div>
            <div class="row">
                <label>Franchise Name</label>
                <div class="row_item">
                    <?php echo radio_checkbox('radio','sort','franchise_az','A-Z',false); ?>
                    <?php echo radio_checkbox('radio','sort','franchise_za','Z-A',false); ?>
                </div>
            </div>
            <div class="row">
                <label>Industry Name</label>
                <div class="row_item">
                    <?php echo radio_checkbox('radio','sort','industry_az','A-Z',false); ?>
                    <?php echo radio_checkbox('radio','sort','industry_za','Z-A',false); ?>
                </div>
            </div>
            <div class="row">
                <label>Minimum Cash Required</label>
                <div class="row_item">
                    <?php echo radio_checkbox('radio','sort','mcr_high','High to Low',false); ?>
                    <?php echo radio_checkbox('radio','sort','mcr_low','Low to High',false); ?>
                </div>
            </div>
            <div class="row">
                <label>Franchising Since Date</label>
                <div class="row_item">
                    <?php echo radio_checkbox('radio','sort','mcr_old','Oldest to Newest',false); ?>
                    <?php echo radio_checkbox('radio','sort','mcr_new','Newest to Oldest',false); ?>
                </div>
            </div>
            <div class="row">
                <label>Franchise Size</label>
                <div class="row_item">
                    <?php echo radio_checkbox('radio','sort','units_high','High to Low',false); ?>
                    <?php echo radio_checkbox('radio','sort','units_low','Low to High',false); ?>
                </div>
            </div>
            <div class="row">
                <div class="row_item buttons">
                    <button class="button" onclick="modal('sort_popup','close');">Cancel</button>
                    <button class="button solid" onclick="sortTab('popup');modal('sort_popup','close');">Apply</button>
                </div>
            </div>
        </div>
    </div>
</div>