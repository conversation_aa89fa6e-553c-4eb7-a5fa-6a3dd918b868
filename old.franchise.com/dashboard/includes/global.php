<?php
if(!isset($_SESSION)){
    session_start();
}

include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/cookies.php');
include_once($_SERVER['DOCUMENT_ROOT'].'/dashboard/includes/global_live_dev.php');

    $random_query = null;
    $requested = [];
    $tiny_src = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
    $random = mt_rand(100000, 999999);
    $default_filter = [
        'min' => 0,
        'max' => 500000,
        'net_worth_min' => 0,
        'net_worth_max' => 2500000,
        'units_min' => 0,
        'units_max' => 8500,
        'max_units' => 8500,
        'max_net_worth' => 2500000
    ];

    //DEBUG
    function debug($var){
        ob_start();
        echo '<pre>';
        print_r($var);
        echo '</pre>';
        ob_end_flush();
    }

    //JSON post CURL job function
    function httpPost($url,$params = [],$request = 'POST',$content_type = 'default')
    {
        // print_r($url);
        $array = [];
        if(is_array($params)){
            $array = $params;   
        }
        $postData = $content_type == 'default' ? json_encode($array) : $array;
        $header = $content_type == 'default' ? array('Content-Type: application/json') :array('Content-Type: multipart/form-data');
        $ch = curl_init();  
        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $request);
        if($request == 'POST'){
            curl_setopt($ch, CURLOPT_HEADER, false); 
            curl_setopt($ch, CURLOPT_POST, count($array));
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);   
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        
        $output=curl_exec($ch);
        //return $output;
        curl_close($ch);
        // return $output;
        return json_decode($output,true);   
    }

    //Get JSON from API
    function get_json($url,$api = true){
        global $api_url;
        $request_URL = ($api) ? $api_url.$url : $url;
        $ch = curl_init();  
    
        curl_setopt($ch,CURLOPT_URL,$request_URL);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch,CURLOPT_HEADER, false);  
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    
        $output=curl_exec($ch);
    
        curl_close($ch);
        $array = json_decode($output,true);
        return $output;
    }

    function setLoginCookies($cookieName,$cookieValue) {
        $cookieExpiration = time() + 86400; // 24 hours
        $cookieDomain = '.franchise.com'; // Note the leading dot for the subdomain
        setcookie($cookieName, $cookieValue, $cookieExpiration, '/', $cookieDomain, $isSecure, true);
    }

    function removeLoggedInCookie() {
        unset($_COOKIE['logged_in']);
        setcookie('logged_in', '', time() - 3600);
    }
    
    function log_out(){
        // session_unset();
        session_destroy();
        removeLoggedInCookie();
        header('Location: /dashboard/login'); 
    }
    
    // Radio/Checkboxes Inputs
    function radio_checkbox($type,$name,$value,$span,$checked = false,$id = null){
        $temp_id = (!is_null($id)) ? 'id="'.$id .'" ' : '';
        $temp_checked = ($checked) ? 'checked' : '';
        return '<div class="'.$type.'_button">
                    <label>
                        <input '.$temp_id.'type="'.$type.'" value="'.$value.'" name="'.$name.'" '.$temp_checked.'>
                        <span>'.$span.'</span>
                    </label>
                </div>';
    }

    // Selects
    function select($array,$name,$selected = null,$id = null, $multi = false){
        $multi_select = '';
        if($multi){
            $multi_select = 'class="multi_select" multiple data-multi-select data-search="false"';
            if($name =='state_code'){
                $multi_select .= ' data-select-all="false"';
            }
        }
        $temp_id = (!is_null($id)) ? 'id="'.$id .'" ' : '';
        $options = '';
        foreach($array as $key => $value){
            $temp_selected = '';
            if(is_array($selected)){
                if(in_array($value['value'],$selected)){
                    $temp_selected = ' selected';
                }
            }else{
                if($selected == $value['value']){
                    $temp_selected = ' selected';
                }
            }
            $temp_disabled = isset($value['disabled']) ? ' disabled' : '';
            $options .= '<option value="'.$value['value'].'"'.$temp_selected.$temp_disabled.'>'.$value['text'].'</option>';
        }
        return '<select '.$multi_select.' '.$temp_id.'name="'.$name.'">'.$options.'</select>';
    }

    // Return Tabs
    function tabs($array,$id = null){
        $active = 'active';
        $list = '';
        $tab_id = (is_null($id)) ? '' : 'id="'.$id.'" ';
        foreach($array as $key => $value){
            $temp_tab = str_replace(' ', '_', strtolower($value['mobile']));
            if($value['id'] ==  296){
                $temp_tab = 'favorites_franchises';
            }else if($value['id'] ==  315){
                $temp_tab = 'favorites_articles';
            }
            $list .= '<li data-id="'.$value['id'].'" data-tab="'.$temp_tab.'" class="'.$active.'"><span data-before="'.$value['before'].'" data-after="'.$value['after'].'">'.$value['mobile'].'</span></li>';
            $active = '';
        }
        return '<div '.$tab_id.'class="tabs"><ul>'.$list.'</ul></div>';
    }

    // Create JavaScript Variables for Filtering Franchises
    function franchise_filters(){
        global $default_filter;
        // Get Max Franchise Units
        $temp_max_units_json = get_json('/api/myaccount_franchise_units');
        $temp_max_units_array = json_decode($temp_max_units_json,true);
        $temp = isset($temp_max_units_array['data'][0]) ? $temp_max_units_array['data'][0] : '';
        if ($temp) {
            if(is_numeric($temp['franchise_units'])){
                $default_filter['max_units'] = $default_filter['units_max'] = $temp['franchise_units'];
            };
            if(is_numeric($temp['net_worth'])){
                $default_filter['max_net_worth'] = $default_filter['net_worth_max'] = $temp['net_worth'];
            };
            $return = [];
            foreach($default_filter as $key => $value){
                $temp_value = (isset($_POST[$key])) ? $_POST[$key] : $value;
                $return[] = $key.' = '.$temp_value;
            }
            return '<script>var '.implode(',',$return).';</script>';
        }else{
            return '';
        }
    }
    // function franchise_filters(){
    //     global $default_filter;
    //     // Get Max Franchise Units
    //     $temp_max_units_json = get_json('/api/myaccount_franchise_units');
    //     $temp_max_units_array = json_decode($temp_max_units_json,true);
    //     if(is_numeric($temp_max_units_array['data'])){
    //         $default_filter['max_units'] = $default_filter['units_max'] = $temp_max_units_array['data'];
    //     };
    //     $return = [];
    //     foreach($default_filter as $key => $value){
    //         $temp_value = (isset($_POST[$key])) ? $_POST[$key] : $value;
    //         $return[] = $key.' = '.$temp_value;
    //     }
    //     // return '<script>var '.implode(',',$return).';</script>';
    //     return '<script>console.log('.implode(',',$return).');</script>';
    // }

    // Create JavaScript Variables for Filtering Franchises
    function franchise_filters_net(){
        global $default_filter;
        // Get Max Net Worth
        $temp_max_units_json = get_json('/api/myaccount_net_worth');
        $temp_max_units_array = json_decode($temp_max_units_json,true);
        if(is_numeric($temp_max_units_array['data'])){
            $default_filter['max_net_worth'] = $default_filter['net_worth_max'] = $temp_max_units_array['data'];
        };
        $return = [];
        foreach($default_filter as $key => $value){
            $temp_value = (isset($_POST[$key])) ? $_POST[$key] : $value;
            $return[] = $key.' = '.$temp_value;
        }
        return '<script>var '.implode(',',$return).';</script>';
    }


    // Create Aside Menu
    function aside_ul($is_bottom = true){
        global $page;
        $aside = [
            'home' => [
                'text' => 'Home'
            ],
            'franchise' => [
                'text' => 'Franchises'
            ],
            'question' => [
                'text' => 'AI Chat'
            ],
            'resources' => [
                'text' => 'Resources'
            ]
        ];
        $list = '';
        foreach($aside as $key => $value){
            $active = ($key == $page) ? 'active' : '';
            $url = ($key == 'home') ? '' : $key;
            if($key == 'question'){
                $active = ('questions' == $page) ? 'active' : '';
                $url = 'questions';
            }
            $svg = ($is_bottom) ? '<i class="'.$key.'"></i>' : '';
            $list .= '<li id="'.$key.'" class="'.$active.'">
                        <a href="/dashboard/'.$url.'">
                            '.$svg.'
                            <span>'.$value['text'].'</span>
                        </a>
                    </li>';
        }
        return '<ul>'.$list.'</ul>';
    }

    // Return Formatted Investment
    function format_investment($number){
        $return = $number;
        if(strlen($number ?? '') >= 4 && strlen($number ?? '') <= 6){
            $temp_string3 = substr($number, -3);
            $temp_string2 = substr($number, -2);
            if($temp_string3 == '000'){
                $return = substr($number, 0, -3).'k';
            }else if($temp_string2 == '00'){
                $return = substr($number, 0, -3).'.'.$temp_string3[0].'K';
            }else{
                $return = number_format($number);
            }
        }
        if(strlen($number ?? '') >= 7){
            $temp_string6 = substr($number, -6);
            $temp_string5 = substr($number, -5);
            if($temp_string6 == '000000'){
                $return = substr($number, 0, -6).'m';
            }else if($temp_string5 == '00000'){
                $return = substr($number, 0, -6).'.'.$temp_string5[0].'M';
            }else{
                $return = number_format($number);
            }
        }
        return $return;
    }

    //FORMAT PHONE
    function format_phone($phone){
        $return = preg_replace("/[^0-9]/", "",$phone);
        if(strlen($phone ?? '') == 7){
            $return = preg_replace("/(\d{3})(\d{4})/", "$1-$2", $phone);
        }
        if(strlen($phone ?? '') == 10){
            $return = preg_replace("/(\d{3})(\d{3})(\d{4})/", "($1) $2-$3", $phone);
        }
        return $return;
    }

    // Results
    function franchise_results($array,$other = false){
        global $requested, $tiny_src, $mx_net_worth;
        $listings = '';
        // debug($array);
        $i = $sample = 1;
        $other = ($other) ? 'other_' : '';
        foreach($array as $key => $value){
            $top_temp = '';
            $data_invest = preg_replace('/[^0-9]/','',$value['investment']);
            if(empty($value['investment'])){
                $data_invest = 0;
            }
            $data_name = preg_replace('/[^0-9a-zA-Z]/','',strtolower($value['name'] ?? ''));
            $data_name_url = urlencode($value['name']);
            $data_category = preg_replace('/[^0-9a-zA-Z]/','',strtolower($value['category_name'] ?? ''));
            $image_url = $value['image_url'];
            if(substr(($image_url ?? ''), 0, 2 ) === '//'){
                $image_url = 'https:'.$value['image_url'];
            }
            $image_url = (is_null($image_url) || strlen($image_url ?? '') < 1) ? 'https://franchise-ventures-general.s3.amazonaws.com/global/images/checkered.png' : $image_url;
            $description = trim($value['ShortDesc']??"");
            $description = $shorts[0] = str_replace(array("\r", "\n"), '', $description);
            if(strlen($description ?? '') > 260) {
                $shorts = explode( "\n", wordwrap($description, 260));
            }
            $shorts[0] .= '&hellip;';
            $temp = '<div class="listing_upper"><div class="listing_banner lazy-background" style="--bg-image: url(\''.$tiny_src.'\');" data-style="--bg-image: url(\''.$image_url.'\');" data-name="'.$data_name_url.'" data-networth="'.$value['min_net_worth'].'" onclick="view_summary(this,'.$value['concepts_id'].')"></div>';
                    if(isset($_GET['page']) && $_GET['page'] == 'franchise'){
                        $temp .= '<span class="results_tile_icon icon_round_bg" data-id="'.$value['concepts_id'].'" data-type="general" data-image="'.$image_url.'" onclick="toggleMoreModal(this,'.$value['concepts_id'].');">
                            <svg width="800px" height="20px" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000" class="bi bi-three-dots-vertical">
                                <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                            </svg>
                        </span>
                                <div id="more_modal_'.$value['concepts_id'].'" class="more_modal hide">
                                    <ul> 
                                        <li id="modal_fav_'.$value['fbo_id'].'" class="modal_fav" data-fboid="'.$value['fbo_id'].'" onclick="moreFavorite('.$value['fbo_id'].','.$value['concepts_id'].');">
                                            <svg width="1.7em" height="1.5em" viewBox="0 0 24 24" fill="none" stroke="#AEAEAE" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 6.00019C10.2006 3.90317 7.19377 3.2551 4.93923 5.17534C2.68468 7.09558 2.36727 10.3061 4.13778 12.5772C5.60984 14.4654 10.0648 18.4479 11.5249 19.7369C11.6882 19.8811 11.7699 19.9532 11.8652 19.9815C11.9483 20.0062 12.0393 20.0062 12.1225 19.9815C12.2178 19.9532 12.2994 19.8811 12.4628 19.7369C13.9229 18.4479 18.3778 14.4654 19.8499 12.5772C21.6204 10.3061 21.3417 7.07538 19.0484 5.17534C16.7551 3.2753 13.7994 3.90317 12 6.00019Z" stroke-width="2" stroke-linejoin="round"></path>
                                            </svg> <span>Add to Favorites</span>
                                        </li>
                                        <li data-type="more_modal" data-id="'.$value['concepts_id'].'" data-image="'.$image_url.'" onclick="toggleMoreModal(this,'.$value['concepts_id'].');">
                                            <svg stroke="#AEAEAE" fill="#AEAEAE" stroke-width="0" viewBox="0 0 24 24" class="w-5 h-5 text-xl" height="1.5em" width="1.7em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M10 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h5v2h2V1h-2v2zm0 15H5l5-6v6zm9-15h-5v2h5v13l-5-6v9h5c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg> Compare
                                        </li>
                                    </ul> 
                                </div>';
                    }
                    $temp .= '<strong class="listing_name" data-name="'.$data_name_url.'" onclick="view_summary(this,'.$value['concepts_id'].')">'.$value['name'].'</strong>
                    <ul class="listing_options" data-name="'.$data_name_url.'" onclick="view_summary(this,'.$value['concepts_id'].')">';
            if(!is_null($value['category_name'] ?? null)){
                $temp .= '<li class="break">
                            <i class="icon cat'.$value['cat_id'].'"></i>
                            <span>'.$value['category_name'].'</span>
                            <i class="icon cash"></i>
                            <span>$'.format_investment($value['investment']).' Req\'d</span>
                        </li>';
            }else{
                $temp .= '<li class="break">
                            <i class="icon cash"></i>
                            <span>$'.format_investment($value['investment']).' Req\'d</span>
                        </li>';
            }
            if(!is_null($value['franchising_since'])){
                $top_temp .= '<i class="icon calendar"></i>
                                <span>Franchising Since '.$value['franchising_since'].'</span>';
                if(!is_null($value['franchise_units'])){
                    $temp_plural = ($value['franchise_units'] == 1) ? '' : 's';
                    $top_temp .= '<i class="icon franchise"></i>
                            <span>'.number_format($value['franchise_units']).' Unit'.$temp_plural.'</span>';
                }
                $temp .= '<li class="break">'.$top_temp.'</li>';
            }else if(!is_null($value['franchise_units'])){
                $temp_plural = ($value['franchise_units'] == 1) ? '' : 's';
                $temp .= '<li class="break">
                            <i class="icon franchise"></i>
                            <span>'.number_format($value['franchise_units']).' Unit'.$temp_plural.'</span>
                        </li>';
            }
            $temp .= '
                    </ul>
                    <p class="listing_details" data-name="'.$data_name_url.'" onclick="view_summary(this,'.$value['concepts_id'].')">'.$description.'</p>
                    </div>
                    <div class="listing_lower">';
            if(empty($requested)){
                $temp .= '<div data-name="'.$data_name_url.'" class="read_more" onclick="view_summary(this,'.$value['concepts_id'].')"><a href="javascript:;" class="button">Read More</a></div>';
            }else{
                $temp .= '<div class="requested">
                            <div class="request_time">
                                <i class="icon request"></i>
                                <span>Requested '.$requested[$value['fbo_id']].'</span>
                            </div>
                            <div data-name="'.$data_name_url.'" class="read_more" onclick="view_summary(this,'.$value['concepts_id'].')"><a href="javascript:;" class="button">Read More</a></div>
                        </div>';
            }  
            $temp .= '<div class="listing_favorites">
                        <input type="checkbox" class="favorites" data-fboid="'.$value['fbo_id'].'" onclick="favorites('.$value['fbo_id'].');">
                    </div>
                    </div>';
                    $listings .= '<div data-id="'.$value['concepts_id'].'" class="item" data-invest="'.substr("00000000{$data_invest}", -8).'" data-name="'.str_replace("&amp;", "and", $data_name).'" data-category="'.str_replace("&amp;", "and", $data_category).'" data-order="'.substr("0000{$i}", -4).'" data-year="'.substr("0000{$value['franchising_since']}", -4).'" data-units="'.substr("0000{$value['franchise_units']}", -4).'">'.$temp.'</div>';
            $i++;
            $sample++; 
            $sample = ($sample == 5) ? 1 : $sample;
        }
        return '<div id="results" class="'.$other.'listings">'.$listings.'</div>';
    }

function returnArticles($limit=null,$is_home=false,$is_latest=false,$master_types_id=null) {
    $limitVal = '';
    if ($limit) {
        $limitVal = '&limit='.$limit;
    }
    if ($is_home) {
        $limitVal .= '&article_type=302&is_random=1';
    }else{
        $limitVal .= '&article_type=303';
    }
    if ($master_types_id) {
        $limitVal .= '&master_types_id='.$master_types_id;
    }
    $temp_array = get_json('/api/myaccount_articles/list?active=1'.$limitVal);
    $temp_array = json_decode($temp_array,true);
    $temp_array = $temp_array['data'];

    $articles = '';

    if ($temp_array && count($temp_array) > 0) {
        $articles = return_articles($temp_array,$is_latest);
    }
    if ($is_latest) {
        return $articles;
    }else{
        return '<div class="articles">'.$articles.'</div>';
    }
}

// Return Articles
function return_articles($array,$is_latest=false){
    $articles = '';
    foreach ($array as $key => $value) {
        $description = trim($value['body_content']);
        $description = strip_tags($description);
        $description = $shorts[0] = str_replace(array("\r", "\n"), '', $description);
        if(strlen($description ?? '') > 260) {
            $shorts = explode( "\n", wordwrap($description, 260));
        }
        $shorts[0] .= '&hellip;';
        $img_url = $value['og_image'] ? $value['og_image'] : '/images/solid.png';
        $data_name_url = urlencode($value['title']);
        if ($is_latest) {
            $articles = '<a class="article_anchor" href="javascript:;" data-name="'.$data_name_url.'" onclick="fetchArticleModal(this,'.$value['id'].');">
            <div id="latest_article">
                <div class="latest_item">
                    <div class="latest_banner" style="--bg-image: url(\''.$img_url.'\');"></div>
                </div>
                <div class="latest_item">
                    <strong class="latest_title">'.$value['title'].'</strong>
                    <p class="latest_details articles_details">'.$shorts[0].'</p>
                    <div class="articles_read_more"><span class="button">Read More</span></div>
                </div>
            </div>
            </a>';
        }else{
            $temp = '<div class="articles_banner" style="--bg-image: url(\''.$img_url.'\');"></div>
                        <strong class="articles_title">'.$value['title'].'</strong>
                        <p class="articles_details">'.$shorts[0].'</p>
                        <div class="articles_read_more"><a href="javascript:;" data-name="'.$data_name_url.'" onclick="fetchArticleModal(this,'.$value['id'].');" class="button">Read More</a></div>';
                        $articles .= '<div class="item"><a class="article_anchor" href="javascript:;" data-name="'.$data_name_url.'" onclick="fetchArticleModal(this,'.$value['id'].');">'.$temp.'</a></div>';
        }
    }
    return $articles;
}

// Search Results
function return_results_array($filter = '',$json = false){
    global $prefix, $homeState;
    $url = $prefix.'/60/fv-searchresults.php?paid=0';
    if ($filter) {
        $url .= $filter;
    }
    if (strpos($filter,'state_code') === false && strpos($filter,'idlist') === false) {
        $url .= '&state_code='.$_SESSION['myac_state_code'];
    }
    // echo $url;die;
    if($json && isset($_GET['showsynd'])){
        if($_GET['showsynd'] == 'yes'){
            echo '<p class="view_demo_json"><a href="'.$url.'" target="_blank"><svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M10.409 0c4.857 0 3.335 8 3.335 8 3.009-.745 8.256-.419 8.256 3v11.515l-4.801-4.801c.507-.782.801-1.714.801-2.714 0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5c1.037 0 2-.316 2.799-.858l4.858 4.858h-18.657v-24h8.409zm2.591 12c1.656 0 3 1.344 3 3s-1.344 3-3 3-3-1.344-3-3 1.344-3 3-3zm1.568-11.925c2.201 1.174 5.938 4.884 7.432 6.882-1.286-.9-4.044-1.657-6.091-1.18.222-1.468-.186-4.534-1.341-5.702z"/></svg> View Endpoint JSON</a></p>';
        }
    }else{
        $json = get_json($url,false);
        return  json_decode($json, true); 
    }
}

//REFERRER DATA COLLECTION
function track_netacuity($website_id,$app_url,$app_response){
    global $api_url,$udid,$ipAddress;


    //SETTING DATA
    $temp_tracking = array();
    $temp_tracking['session_id'] = $udid;
    $temp_tracking['site_id'] = $website_id;
    $temp_tracking['query_string'] = @$_SERVER['QUERY_STRING'];
    $temp_tracking['browser_agent'] = @$_SERVER['HTTP_USER_AGENT'];
    $temp_tracking['full_url'] = strtok('https://'.@$_SERVER['HTTP_HOST'].@$_SERVER["REQUEST_URI"],"?");
    $temp_tracking['referer_url'] = @$_SERVER["HTTP_REFERER"];
    $temp_tracking['ip_address'] = $ipAddress;
    $temp_tracking['application_url'] = $app_url;
    $temp_tracking['application_response'] = json_encode($app_response);

    $array['data'] = $temp_tracking;

    $url = $api_url.'/api/track_netacuity';
    $ch = curl_init();  
    curl_setopt($ch, CURLOPT_URL,$url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HEADER, false); 
    curl_setopt($ch, CURLOPT_POST, count($array));
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($array));   
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
 
    $output=curl_exec($ch);
 
    curl_close($ch);
    return json_decode($output,true);
}

// RETURN $homeState VARIABLE
function return_home_state(){
    global $geo;
    $return = null; 
    if(isset($_SESSION['myac_state_code'])){
        $return = $_SESSION['myac_state_code'];
    }else if(isset($geo)){
        $temp = $geo['state'];
        if($geo['country'] == 'ca'){
            $temp = 'CAN';
        }
        $return = strtoupper($temp);
    }else if(is_null($return)){
        $return = 'CA';
    }
    return htmlspecialchars($return);
}

// Load Quiz
function load_quiz($quiz_id,$quiz_only = true){
    $url = 'https://admin.franchiseventures.com/quiz-preview/';
    $subdomain_check = explode('.',$_SERVER['HTTP_HOST']);
    if (!in_array($subdomain_check[0], ['www','old'])){
        $url = 'https://'.$subdomain_check[0].'.admin.franchiseventures.com/quiz-preview/'; 
    }
    $url .= $quiz_id.'?site_id=60';
    $url .= ($quiz_only) ? '&quiz_only=true' : '&css_js_only=true';
    // Set custom headers including User-Agent
    $options = [
        "http" => [
            "header" => "User-Agent: FVAgent/1.0\r\n"
        ]
    ];
    $context = stream_context_create($options);
    $return = file_get_contents($url, false, $context) or die("Error: Cannot create object");
    return $return;
}

// Get Survey
function get_survey($quiz){
    global $domain;
    $return = '';
    $show_data = get_json('/api/myaccount_survey/'.$_SESSION['myac_login_contact_id']);
    $show_data = json_decode($show_data,true);
    $show_data = $show_data['data'];
    $fields_string = [];
    $_SESSION['welcome_survey'] = $show_data;
    if($show_data == 0){
        $url = $domain.'/dashboard/survey/'.$quiz;
        //open connection
        $ch = curl_init();

        //set the url, number of POST vars, POST data
        curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_HTTPHEADER, array("Content-Type: text/html"));
        curl_setopt($ch,CURLOPT_POST, true);
        curl_setopt($ch,CURLOPT_POSTFIELDS, @$fields_string);

        //So that curl_exec returns the contents of the cURL; rather than echoing it
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true); 

        //execute post
        $return = curl_exec($ch);
    }
    return $return;
}

function getInitials($fullName) {
    // Split the full name into an array of words
    $words = explode(' ', $fullName);
    
    // Initialize an empty string for the initials
    $initials = '';
    
    // Loop through each word and add the first letter to the initials
    foreach ($words as $word) {
        // Trim any extra whitespace and get the first character
        $initials .= strtoupper($word[0]);
    }
    return $initials;
}

function getStates() {
    $temp_states_json = get_json('/api/location?site_id=60');
    $temp_states_array = json_decode($temp_states_json,true);
    if(is_array($temp_states_array)){
        foreach($temp_states_array['data']['states'] as $key => $value){
            $states[] = ['text' => $value['state_name'], 'value' => $value['state_code']];
        }
        return $states;
    }
}

// Unset all cookies
function unset_all_cookies(){
    if (isset($_SERVER['HTTP_COOKIE'])) {
        $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
        foreach($cookies as $cookie) {
            $parts = explode('=', $cookie);
            $name = trim($parts[0]);
            setcookie($name, '', time()-1000);
            setcookie($name, '', time()-1000, '/');
        }
    }
}

function toolTip($text='') {
    if ($text) {
        return '<svg class="svg-icon" onmousemove="showTooltip(evt, '.$text.');" onmouseout="hideTooltip();" style="width: 1em; height: 1em;vertical-align: middle;fill: #324972;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M512 81.408a422.4 422.4 0 1 0 422.4 422.4A422.4 422.4 0 0 0 512 81.408z m26.624 629.76a45.056 45.056 0 0 1-31.232 12.288 42.496 42.496 0 0 1-31.232-12.8 41.984 41.984 0 0 1-12.8-30.72 39.424 39.424 0 0 1 12.8-30.72 42.496 42.496 0 0 1 31.232-12.288 43.008 43.008 0 0 1 31.744 12.288 39.424 39.424 0 0 1 12.8 30.72 43.008 43.008 0 0 1-13.312 31.744z m87.04-235.52a617.472 617.472 0 0 1-51.2 47.104 93.184 93.184 0 0 0-25.088 31.232 80.896 80.896 0 0 0-9.728 39.936v10.24h-64v-10.24a119.808 119.808 0 0 1 12.288-57.344A311.296 311.296 0 0 1 555.52 460.8l10.24-11.264a71.168 71.168 0 0 0 16.896-44.032A69.632 69.632 0 0 0 563.2 358.4a69.632 69.632 0 0 0-51.2-17.92 67.072 67.072 0 0 0-58.88 26.112 102.4 102.4 0 0 0-16.384 61.44h-61.44a140.288 140.288 0 0 1 37.888-102.4 140.8 140.8 0 0 1 104.96-38.4 135.68 135.68 0 0 1 96.256 29.184 108.032 108.032 0 0 1 36.352 86.528 116.736 116.736 0 0 1-25.088 73.216z"/></svg>';
    }else{
        return '<svg class="svg-icon" onmousemove="showTooltip(evt, \'Password must be At least <ul><li>One <strong>uppercase</strong> letter</li><li>One of the <strong>! @ $</strong> characters</li><li><strong>7</strong> characters long</li></ul>\');" onmouseout="hideTooltip();" style="width: 1em; height: 1em;vertical-align: middle;fill: #324972;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M512 81.408a422.4 422.4 0 1 0 422.4 422.4A422.4 422.4 0 0 0 512 81.408z m26.624 629.76a45.056 45.056 0 0 1-31.232 12.288 42.496 42.496 0 0 1-31.232-12.8 41.984 41.984 0 0 1-12.8-30.72 39.424 39.424 0 0 1 12.8-30.72 42.496 42.496 0 0 1 31.232-12.288 43.008 43.008 0 0 1 31.744 12.288 39.424 39.424 0 0 1 12.8 30.72 43.008 43.008 0 0 1-13.312 31.744z m87.04-235.52a617.472 617.472 0 0 1-51.2 47.104 93.184 93.184 0 0 0-25.088 31.232 80.896 80.896 0 0 0-9.728 39.936v10.24h-64v-10.24a119.808 119.808 0 0 1 12.288-57.344A311.296 311.296 0 0 1 555.52 460.8l10.24-11.264a71.168 71.168 0 0 0 16.896-44.032A69.632 69.632 0 0 0 563.2 358.4a69.632 69.632 0 0 0-51.2-17.92 67.072 67.072 0 0 0-58.88 26.112 102.4 102.4 0 0 0-16.384 61.44h-61.44a140.288 140.288 0 0 1 37.888-102.4 140.8 140.8 0 0 1 104.96-38.4 135.68 135.68 0 0 1 96.256 29.184 108.032 108.032 0 0 1 36.352 86.528 116.736 116.736 0 0 1-25.088 73.216z"/></svg>';
    }
}

// Load Profile or Article
function load_profile_article(){
    $script = '';
    if(isset($_REQUEST['article']) && $_REQUEST['article']){
        $script = 'var article_url = "/'.$_GET['article'].'";';
    }else if(isset($_REQUEST['profile']) && $_REQUEST['profile']){
        $script = 'var profile_url = "/'.$_GET['profile'].'";';
    }
    return '<script>'.$script.'</script>';
}

// Login From External URL
function login_external(){
    if(isset($_COOKIE['myac_email'])){
        $loop = ['myac_email','myac_first_name','myac_last_name','myac_login_contact_id','myac_state_code','myac_phone','myac_zip_code','myac_utm_campaign','myac_utm_medium','myac_utm_source','myac_utm_type','myac_gclid_mlclkid','welcome'];
    
        foreach($loop as $cookie){
            $_SESSION[$cookie] = $_COOKIE[$cookie];
        }
    }
}