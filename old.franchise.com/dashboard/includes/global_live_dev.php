<?php
//Checks for live or dev
$domain = 'https://www.franchise.com';
$prefix = 'https://www.franchiseportals.com';
$api_url = 'https://franchiseinsights.franchiseportals.com';
$not_dev = true;
$global_robots = 'index, follow';
$subdomain_check = explode('.',$_SERVER['HTTP_HOST']);
if ($subdomain_check[0] != 'www' || isset($_GET['landing'])){
    $global_robots = 'noindex, nofollow';
}
if (in_array($subdomain_check[0],['beta'])){
    $domain = 'https://beta.franchise.com';
    $prefix = 'https://www.franchiseportals.com';    
    $api_url = 'https://franchiseinsights.franchiseportals.com';  
    $not_dev = true;
    $global_robots = 'index, follow';
}else if (!in_array($subdomain_check[0],['www'])){
    $sub = 'www';
    $mysub = '';
    if(!is_numeric($subdomain_check[0])){
        $mysub = $sub = $subdomain_check[0];
    }
    if ($subdomain_check[0] == 'old'){
        $domain = 'https://'.$mysub.'.franchise.com';
    }else{
        $domain = 'https://'.$mysub.'.franchise.com';
        $prefix = 'https://'.$sub.'.franchiseportals.com';    
        $api_url = 'https://'.$mysub.'.franchiseinsights.franchiseportals.com';  
    }
    $random_query = '?random='.mt_rand(100000, 999999);
    $not_dev = false;
}

$relative_path = (isset($_SERVER['HTTP_REFERER'])) ? $_SERVER['HTTP_REFERER'] : $_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];