table #results {
    display: grid;
    max-width: 74.375rem;
    gap: 1.25rem;
    margin: .875rem auto 3rem;
    padding: 0;
    width: calc(100% - 2rem);
    list-style-type: none;
    caption-side: bottom;
    border-collapse: collapse;
}
table caption {
    text-align: center;
    padding: 10px;
}
table:has(.results_tr_nope) caption {
    display: none;
}
#results tr.results_tr {
    position: relative;
    border: 1px solid #324972;
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0;
    flex: none;
}
#results tr.results_tr_cities {
    grid-column: 1/-1;
    text-align: center;
    display: block;
    background: #0077b3;
    border-color: #0077b3;
    padding: 10px;
}
#results tr.results_tr_cities a {
    font-size: 14px;
    width: 100%;
    display: block;
    color: #fff !important;
    text-decoration: none !important;
}
#results tr.results_tr_cities a span {
    display: block;
    font-size: 1.3rem;
}
#results tr.results_tr_cities a strong {
    font-weight: 400;
    font-size: 1rem;
    margin-top: 0.6rem;
    display: block;
    text-transform: uppercase;
}
#results tr.results_tr_nope{
    grid-column: 1/-1;
    text-align: center;
    display: block;
    background: #dedede;
    border-color: #dedede;
    padding: 10px;
}
.results_tr > *{
    display: block;
}
.results_td_image{
    display: flex;
    justify-content: center;
}
.results_td_image img {
    margin: 15px auto 0;
    height: 60px;
    width: auto;
    min-width: 120px;
    max-width: calc(100% - 10px);
    box-shadow: 0 0 0 1px rgb(0 0 0 / 10%);
    display: table;
}
.results_td_name {
    min-height: 45px;
    padding: 0 5px;
    font-size: 1rem;
    font-weight: 700;
    margin: 5px 0 10px;
    line-height: 18px;
    border-bottom: 1px solid #324972;
    overflow: hidden;
    text-align: center;
}
.results_td_name a {
    color: black;
}
.results_td_description {
    height: auto;
    font-size: 14px;
    margin: 0 10px;
    overflow: hidden;
}
.td_h4 {
    color: #31486e;
    font-size: 0.8rem;
    font-weight: 700;
    font-family: sans-serif;
    line-height: 1.2;
}
.td_h4:before {
    content: attr(data-before);
    display: inline-block;
    padding-right: .5rem;
}
.results_td_investment{
    margin-top: 30px;
    padding: 0 10px 0;
}
.results_td_category{
    padding: 0 10px 10px;
    min-height: 54.66px;
}

.result-checkbox {
    position: relative;
    cursor: pointer;
}
.results_td_checkbox input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
}
.results_td_checkbox button {
    background-color: #324972;
    color: #FFF;
    border: none;
    display: block;
    font-size: 0.875rem;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 15px 10px;
    text-transform: uppercase;
}
.results_td_checkbox button.noshow {
    background-color: #dcdbd8;
    cursor: default;
}
.results_td_checkbox input:checked + button {
    background-color: #CC1209;
    cursor: pointer;
}
.results_td_checkbox button.requested{
    background-color: #cedee5;
    color: rgba(0,0,0,.35);
    cursor: default;
    text-align: center;
}
.results_td_checkbox button.requested:before{
    content: '';
    margin-right: 10px;
    height: 0.7rem;
    width: 0.7rem;
    display: inline-block;
    -webkit-mask: url( 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMuMzM0IDExLjk2Yy0uNzEzLS43MjYtLjg3Mi0xLjgyOS0uMzkzLTIuNzI3LjM0Mi0uNjQuMzY2LTEuNDAxLjA2NC0yLjA2Mi0uMzAxLS42Ni0uODkzLTEuMTQyLTEuNjAxLTEuMzAyLS45OTEtLjIyNS0xLjcyMi0xLjA2Ny0xLjgwMy0yLjA4MS0uMDU5LS43MjMtLjQ1MS0xLjM3OC0xLjA2Mi0xLjc3LS42MDktLjM5My0xLjM2Ny0uNDc4LTIuMDUtLjIyOS0uOTU2LjM0Ny0yLjAyNi4wMzItMi42NDItLjc3Ni0uNDQtLjU3Ni0xLjEyNC0uOTE1LTEuODUtLjkxNS0uNzI1IDAtMS40MDkuMzM5LTEuODQ5LjkxNS0uNjEzLjgwOS0xLjY4MyAxLjEyNC0yLjYzOS43NzctLjY4Mi0uMjQ4LTEuNDQtLjE2My0yLjA1LjIyOS0uNjEuMzkyLTEuMDAzIDEuMDQ3LTEuMDYxIDEuNzctLjA4MiAxLjAxNC0uODEyIDEuODU3LTEuODAzIDIuMDgxLS43MDguMTYtMS4zLjY0Mi0xLjYwMSAxLjMwMnMtLjI3NyAxLjQyMi4wNjUgMi4wNjFjLjQ3OS44OTcuMzIgMi4wMDEtLjM5MiAyLjcyNy0uNTA5LjUxNy0uNzQ3IDEuMjQyLS42NDQgMS45NnMuNTM2IDEuMzQ3IDEuMTcgMS43Yy44ODguNDk1IDEuMzUyIDEuNTEgMS4xNDQgMi41MDUtLjE0Ny43MS4wNDQgMS40NDguNTE5IDEuOTk2LjQ3Ni41NDkgMS4xOC44NDQgMS45MDIuNzk4IDEuMDE2LS4wNjMgMS45NTMuNTQgMi4zMTcgMS40ODkuMjU5LjY3OC44MiAxLjE5NSAxLjUxNyAxLjM5OS42OTUuMjA0IDEuNDQ3LjA3MiAyLjAzMS0uMzU3LjgxOS0uNjAzIDEuOTM2LS42MDMgMi43NTQgMCAuNTg0LjQzIDEuMzM2LjU2MiAyLjAzMS4zNTcuNjk3LS4yMDQgMS4yNTgtLjcyMiAxLjUxOC0xLjM5OS4zNjMtLjk0OSAxLjMwMS0xLjU1MyAyLjMxNi0xLjQ4OS43MjQuMDQ2IDEuNDI3LS4yNDkgMS45MDItLjc5OC40NzUtLjU0OC42NjctMS4yODYuNTE5LTEuOTk2LS4yMDctLjk5NS4yNTYtMi4wMSAxLjE0NS0yLjUwNS42MzMtLjM1NCAxLjA2NS0uOTgyIDEuMTY5LTEuN3MtLjEzNS0xLjQ0My0uNjQzLTEuOTZ6bS0xMi41ODQgNS40M2wtNC41LTQuMzY0IDEuODU3LTEuODU3IDIuNjQzIDIuNTA2IDUuNjQzLTUuNzg0IDEuODU3IDEuODU3LTcuNSA3LjY0MnoiLz48L3N2Zz4=');
    mask: url( 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMuMzM0IDExLjk2Yy0uNzEzLS43MjYtLjg3Mi0xLjgyOS0uMzkzLTIuNzI3LjM0Mi0uNjQuMzY2LTEuNDAxLjA2NC0yLjA2Mi0uMzAxLS42Ni0uODkzLTEuMTQyLTEuNjAxLTEuMzAyLS45OTEtLjIyNS0xLjcyMi0xLjA2Ny0xLjgwMy0yLjA4MS0uMDU5LS43MjMtLjQ1MS0xLjM3OC0xLjA2Mi0xLjc3LS42MDktLjM5My0xLjM2Ny0uNDc4LTIuMDUtLjIyOS0uOTU2LjM0Ny0yLjAyNi4wMzItMi42NDItLjc3Ni0uNDQtLjU3Ni0xLjEyNC0uOTE1LTEuODUtLjkxNS0uNzI1IDAtMS40MDkuMzM5LTEuODQ5LjkxNS0uNjEzLjgwOS0xLjY4MyAxLjEyNC0yLjYzOS43NzctLjY4Mi0uMjQ4LTEuNDQtLjE2My0yLjA1LjIyOS0uNjEuMzkyLTEuMDAzIDEuMDQ3LTEuMDYxIDEuNzctLjA4MiAxLjAxNC0uODEyIDEuODU3LTEuODAzIDIuMDgxLS43MDguMTYtMS4zLjY0Mi0xLjYwMSAxLjMwMnMtLjI3NyAxLjQyMi4wNjUgMi4wNjFjLjQ3OS44OTcuMzIgMi4wMDEtLjM5MiAyLjcyNy0uNTA5LjUxNy0uNzQ3IDEuMjQyLS42NDQgMS45NnMuNTM2IDEuMzQ3IDEuMTcgMS43Yy44ODguNDk1IDEuMzUyIDEuNTEgMS4xNDQgMi41MDUtLjE0Ny43MS4wNDQgMS40NDguNTE5IDEuOTk2LjQ3Ni41NDkgMS4xOC44NDQgMS45MDIuNzk4IDEuMDE2LS4wNjMgMS45NTMuNTQgMi4zMTcgMS40ODkuMjU5LjY3OC44MiAxLjE5NSAxLjUxNyAxLjM5OS42OTUuMjA0IDEuNDQ3LjA3MiAyLjAzMS0uMzU3LjgxOS0uNjAzIDEuOTM2LS42MDMgMi43NTQgMCAuNTg0LjQzIDEuMzM2LjU2MiAyLjAzMS4zNTcuNjk3LS4yMDQgMS4yNTgtLjcyMiAxLjUxOC0xLjM5OS4zNjMtLjk0OSAxLjMwMS0xLjU1MyAyLjMxNi0xLjQ4OS43MjQuMDQ2IDEuNDI3LS4yNDkgMS45MDItLjc5OC40NzUtLjU0OC42NjctMS4yODYuNTE5LTEuOTk2LS4yMDctLjk5NS4yNTYtMi4wMSAxLjE0NS0yLjUwNS42MzMtLjM1NCAxLjA2NS0uOTgyIDEuMTY5LTEuN3MtLjEzNS0xLjQ0My0uNjQzLTEuOTZ6bS0xMi41ODQgNS40M2wtNC41LTQuMzY0IDEuODU3LTEuODU3IDIuNjQzIDIuNTA2IDUuNjQzLTUuNzg0IDEuODU3IDEuODU3LTcuNSA3LjY0MnoiLz48L3N2Zz4=');
    -webkit-mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    mask-size: cover;
    background-color: rgba(0,0,0,.35);
    margin-right: 10px;
   
}
.results_td_checkbox button:not(.noshow,.requested):before {
    content: '';
    margin-right: 10px;
    height: 0.7rem;
    width: 0.7rem;
    display: inline-block;
    -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=);
    mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=);
    -webkit-mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    mask-size: cover;
    background-color: #FFF;
}
.results_td_checkbox input:checked + button:before {
    -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+);
    mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+);
    -webkit-mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    mask-size: cover;
}
.results_td_checkbox input:checked + button span {
    font-size: 0px;
}
.results_td_checkbox input:checked + button span::after {
    font-size: 0.9rem;
    content: 'Added to cart';
}
@media only screen and (min-width: 48rem) {
    #results {
        grid-template-columns: repeat(2, 1fr);
    }
    .results_td_name a:hover {
        color: #cc1209;
    }
    .results_td_description {
        height: 175px;
    }
}
@media only screen and (min-width: 64rem) {
    #results{
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}
@media only screen and (min-width: 75rem) {
    #results{
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}