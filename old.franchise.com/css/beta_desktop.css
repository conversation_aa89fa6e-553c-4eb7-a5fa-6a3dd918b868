/* OVERALL */
h1 {
    font-size: 1.5rem;
    text-align: left;
}
h1 + h2 {
    text-align: left;
}
body:is(.resources,.profile){
    overflow: hidden;
}
@media only screen and (min-width : 64rem){
    h1 {
        margin-bottom: 1rem;
    }
    .sticky{
        top: -2rem;
    }
}

/* Shell */
@media only screen and (min-width : 64rem){
    #shell {
        flex-direction: row;
    }
}

/* Header */
header#header .logo img{
    height: 2rem;
}
#header_nav{
    width: 6rem;
}
.h1_counter{
    flex-direction: row;
}
.h1_counter span{
    padding: 0;
}
.h1_counter span:before{
    content: '|';
    padding: 0 .25rem;
    font-size: 1.5rem;
    color: var(--header);
}
.h1_counter em{
    font-size: 1.5rem;
}
@media only screen and (min-width : 64rem){
    .header_left {
        display: flex;
    }
    .desktop_nav{
        display: flex;
        align-items: center;
    }
    .desktop_nav ul {
        display: flex;
        flex-direction: row;
        width: 50vw;
        max-width: 40rem;
        justify-content: space-evenly;
    }
    .desktop_nav a{
        text-decoration: none;
        color: var(--black);
        font-weight: 400;
    }
    .desktop_nav li.active a{
        color: var(--button_blue);
    }
}

/* Main */
@media only screen and (min-width : 64rem){
    .franchise_search å#main{
        min-height: 100vh;
        border-top: 2rem solid var(--main_blue);
        padding: 4rem 2rem 2rem;
    }
}

/* Aside */
#aside {
    height: 6rem;
}
#aside a {
    font-size: 1rem;
}
#aside svg{
    height: 2rem;
}
@media only screen and (min-width : 64rem){
    #aside {
        display: none;
    }
}

/* Columns */
.columns svg{
    height: 2rem;
    width: 2rem;
}
.columns li{
    border-radius: 2rem;
}
.columns li span:after{
    content: ' Your Results';
}

/* SHARED */
:is(.resources,.profile) #main{
    background-repeat: no-repeat;
    background-size: cover;
}
#listing_favorites span{
    display: block;
    padding-right: .5rem;
}
#listing_favorites span:before {
    content: 'Add to';
}

/* MODAL */
.modal{
    min-height: auto;
    height: 100vh;
}
.modal_banner{
    position: sticky;
}
.modal .content{
    padding:  0;
}

/* RESOURCES */
.resources #main{
    background-image: url('/images/beta_resources.jpg');
}
.article_banner {
    width: 50%;
    float: right;
    margin-left: 1rem;
}

/* PROFILE */
.profile #main{
    background-image: url('/images/beta_profile.jpg');
}
#profile_header{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
}
.profile_header_item:first-child { grid-area: 1 / 1 / 2 / 2; }
.profile_header_item:last-child { grid-area: 1 / 2 / 2 / 3; }
#profile_columns{
    border: none;
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
    padding: 1rem 0;
    margin: 0;
}
#profile_columns .profile_column:not(:first-child) h2{
    margin-top: -1rem;
    border-top: none;
}
.profile_header_item{
    align-items: baseline;
    height: fit-content;
}
.profile_banner{
    min-height: 179px;
}
.profile_column{
    border-radius: 0 0 .5rem .5rem;
}
.profile_column,.profile_column:not(:last-child){
    border: 1px solid var(--gray);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
}
#profile_columns:has(.profile_column:last-child:nth-child(2)){
    grid-template-columns: 1fr 1fr;
}
#profile_columns:has(.profile_column:last-child:nth-child(3)){
    grid-template-columns: 1fr 1fr 1fr;
}
@media only screen and (min-width : 64rem){
    #profile_header{
        display: grid;
        grid-template-columns: 1fr;
        grid-gap: 1rem;
        grid-auto-columns: minmax(0, 1fr);
    }
    .profile_header_item:first-child { grid-area: 1 / 1 / 2 / 2; }
    .profile_header_item:last-child { grid-area: 1 / 2 / 2 / 3; }
    #profile_columns{
        border: none;
        display: grid;
        grid-template-columns: 1fr;
        grid-gap: 1rem;
        grid-auto-columns: minmax(0, 1fr);
        padding: 1rem 0;
        margin: 0;
    }
    #profile_columns .profile_column:not(:first-child) h2{
        margin-top: -1rem;
        border-top: none;
    }
    .profile_header_item{
        align-items: baseline;
        height: fit-content;
    }
    .profile_banner{
        min-height: 179px;
    }
    .profile_column{
        border-radius: 0 0 .5rem .5rem;
    }
    .profile_column,.profile_column:not(:last-child){
        border: 1px solid var(--gray);
        box-sizing: border-box;
        background-color: var(--white);
        padding: 1rem;
    }
    #profile_columns:has(.profile_column:last-child:nth-child(2)){
        grid-template-columns: 1fr 1fr;
    }
    #profile_columns:has(.profile_column:last-child:nth-child(3)){
        grid-template-columns: 1fr 1fr 1fr;
    }
}

/* Franchise */
.listings {
    grid-template-columns: 1fr 1fr;
}
#franchise_filter{
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    padding: 1rem;
}
#franchise_filter h3:before{
    content: 'Find Your Franchise';
}
#franchise_filter button.cancel{
    display: none;
}
@media only screen and (min-width : 64rem){
    #franchise_filter{
        display: block;
    }
    #franchise_filtering{
        position: sticky;
        top: 6rem;
        align-self: start;
        max-height: calc(100vh - 6rem);
        overflow-y: scroll;
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
    #franchise_filtering::-webkit-scrollbar {
        display: none;
    }
    #franchise_columns{
        display: grid;
        grid-auto-columns: minmax(0, 1fr);
        grid-auto-flow: column;
        grid-template-columns: repeat(1, 1fr);
        grid-template-rows: 1fr;
        grid-column-gap: 1rem;
        grid-row-gap: 0px;
    }
    #franchise_filtering { 
        grid-area: 1 / 1 / 2 / 2;
    }
    #franchise_main {
        grid-area: 1 / 2 / 2 / 4;
    }
    #franchise_buttons{
        display:none;
    }
    .franchise_wrap{
        position: sticky;
        top: 4rem;
        z-index: 2;
        background: var(--main_blue);
        padding-top: 2rem;
        margin-top: -2rem;
    }
    .sort_by {
        display: flex;
        align-items: center;
        margin-top: 1rem;
    }
    .sort_by label{
        margin-right: .5rem;
    }
    .listing_options li.break i:not(:first-child) {
        margin-left: .5rem;
    }
}
@media only screen and (min-width: 75rem) {
    .listings {
        grid-auto-columns: minmax(0, 1fr);
        grid-template-columns: 1fr 1fr 1fr;
    }
    #franchise_main {
        grid-area: 1 / 2 / 2 / 5;
    }
    #franchise_title {
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    .sort_by {
        margin-top: 0;
    }
}
@media only screen and (min-width: 100rem) {
    .listing_name {
        font-size: 1.25rem;
        padding: 1rem .5rem;
        font-weight: 400;
    }
    .listing_options li {
        font-size: .9rem;
    }
    .listing_details{
        font-size: .9rem;
        line-height: 1.5;
    }
    .read_more {
        padding-bottom: 1rem;
    }
}

.franchise_wrap {
    position: sticky;
    top: 4rem;
    z-index: 2;
    background: var(--main_blue);
    padding-top: 2rem;
    margin-top: -2rem;
}