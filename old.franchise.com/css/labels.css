.display_labels{
    position: relative;
    border: 2px solid #00F !important;
    box-shadow: 2px 2px 0 rgb(0 0 0 / 25%);
    border-radius: 0 !important;
    
}
.display_labels:before{
    color: #FFF;
    font-weight: 400;
    background: #00F;
    display: inline-flex;
    padding: 0 10px;
    height: 16px;
    line-height: 16px;
    font-size: 11px;
    white-space: nowrap;
    content: attr(data-labels);
    position: absolute;
    inset: -18px auto 0 -2px;
    margin-left: 0;
    z-index: 1;
    font-family: Arial, Helvetica, sans-serif;
    text-transform: none;
    transition: all .2s ease, background-color 1ms;
    letter-spacing: normal;
    -webkit-font-smoothing: auto;
}
.display_labels.labels_right:before{
    inset: -18px -2px 0 auto;
}
.display_labels.labels_bottom:before{
    top:100%;
    bottom:-2px;
}
.display_labels:after{
    inset: 0;
    background-color: #00F;
    opacity: .25;
    content: '';
    display: block;
    position: absolute;
    z-index: 0;
}
.display_labels.tint{
    background-color: rgba(0,0,255,.25) !important;
    background-blend-mode:multiply;
}
.display_labels.tint:after{
    display: none;
}
.display_labels:hover{
    border-color: #f94d00 !important;
    z-index: 11;
    background-color: #FFF;
    cursor: pointer;
    color:#000;
}
.display_labels:hover:before{
    background-color: #f94d00;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    inset: -22px auto 0 -2px;
}
.display_labels.labels_right:hover:before{
    inset: -22px -2px 0 auto;
}
.display_labels.labels_bottom:hover:before{
    top:0;
    bottom:-22px;
}
.display_labels:hover:after{
    background-color: #f94d00;
}
.display_labels.tint:hover{
    background-color: rgba(249,77,0,.25) !important;
}