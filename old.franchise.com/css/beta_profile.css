/* PROFILE */
#profile_header{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
}
.profile_header_item:first-child { grid-area: 2 / 1 / 3 / 2; }
.profile_header_item:last-child { grid-area: 1 / 1 / 2 / 2; }
.profile_header_item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.profile_header_item.add_border{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--main_blue);
    min-height: 143px;
}
.profile_header_item.add_border:has(.profile_video){
    border-radius: 0 0 .5rem .5rem;
}
.profile_logo {
    margin-bottom: 1rem;
    text-align: center;
    box-sizing: border-box;
    background-color: var(--white);
    padding: .5rem;
    background: linear-gradient(to bottom,  var(--light_gray) 0%,var(--white) 100%); 
    border: 1px solid var(--light_gray);
    border-bottom: none;
}
.profile_logo img {
    display: block;
    margin: 0 auto;
    max-width: 50%;
    max-height: 7rem;
    padding: .5rem;
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    border-radius: .5rem;
}
#profile_header .profile_title{
    margin: 0;
}
.profile_banner{
    position: relative;
    min-height: 143px;
}
.slider {
    position: absolute;
    top: calc(50% - .75rem);
    background: rgba(0, 0, 0, .25);
    padding: .5rem 0;
    border-radius: .25rem;
}
.slider.left {
    left: .25rem;
}
.slider.right {
    right: .25rem;
}
.slider svg{
    height: 1.5rem;
    display: flex;
    fill: var(--white);
}
.slider.left svg{
    transform: rotate(180deg);
}
.profile_banner img{
    object-fit: cover;
    width: 100%;
    max-height: 100%;
    display: block;
}
.profile_video {
    text-align: center;
    width: 100%;
    box-sizing: border-box;
    padding: .5rem;
    border-top: 1px solid var(--border_blue);
}
.profile_video a{
    color: var(--white);
    background-color: var(--button_blue);
    border-radius: 1rem;
    display: inline-flex;
    height: 1.5rem;
    padding: 0 1rem;
    font-size: .85rem;
    font-weight: 500;
    text-decoration: none;
    align-items: center;
}
.profile_details{
    margin: .5rem 0 1rem;
    font-size: .9rem;
}
#profile_columns{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
    margin: .5rem 0 1rem;
}
.profile_column:not(:last-child){
    padding: 0 0 1rem;
}
.profile_column h2 {
    text-align: left;
    margin: -1rem -1rem 1rem;
    padding: 1rem;
    color: var(--white);
    background: var(--logo_blue);
    border-bottom: 1px solid var(--border_blue);
}
#profile_columns .profile_column:not(:first-child) h2{
    margin-top: 0;
    border-top: 1px solid var(--gray);
}
.profile_column p{
    margin-bottom: .5rem;
}
.profile_column table{
    width: 100%;
    font-weight: 500;
    font-size: .85rem;
    padding-top: .25rem;
}
.profile_column table tr td{
    border-bottom: 1px dashed var(--border_blue);
    padding-bottom: .25rem;
    padding-top: .25rem;
}
.profile_column table tr:first-child td{
    padding-top: 0;
}
.profile_column table tr:last-child td{
    padding-bottom: 0;
    border: none;
}
.profile_column .subtitle {
    font-weight: 400;
}
.profile_column .title {
    font-size: .75rem;
}
h2.profile_category {
    margin-top: .5rem;
}
.profile_column .value {
    padding-left: .5rem;
    color: var(--logo_blue);
}
.profile_learn_more{
    text-align: center;
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
}
.profile_learn_more h2{
    text-align: center;
}
.profile_learn_more p{
    margin-bottom: .5rem;
}
.profile_learn_more strong{
    font-weight: 500;
}
.profile_long_description{
    padding: 1rem 0 0;
}
.profile_long_description :is(strong,b){
    font-weight: 500;
}
.profile_long_description :is(em,u){
    font-style: italic;
}
.profile_long_description p{
    margin-bottom: 1rem;
}
.profile_long_description :is(ol,ul) {
    list-style-type: initial;
    margin: 0 0 1rem 1rem;
}
.profile_long_description :is(ol,ul) li:not(:last-child){
    padding-bottom: .5rem;
}
.profile_scrolltop {
    display: none;
    text-align: center;
    position: sticky;
    top: 4rem;
}
.profile_scrolltop span {
    border-radius: 1rem;
    display: inline-flex;
    height: 1.5rem;
    padding: 0 1rem;
    font-size: .85rem;
    font-weight: 500;
    background: rgba(0, 0, 0, .5);
    color: var(--white);
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.profile_footer_button{
    text-align: center;
    position: fixed;
    inset: auto 0 1rem 0;
    box-shadow: 0 0 1rem rgba(255, 255, 255, .75);
    background-color: rgba(255, 255, 255, .75);
    -webkit-backdrop-filter: blur(.625rem);
    backdrop-filter: blur(.15rem);
    padding: 1rem 0;
    margin: 0 -1rem -1rem;
}
@media only screen and (min-width : 48rem){
    .profile_footer_button{
        position: sticky;
        bottom: -1rem;
    }
}