*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body{
    width: 100%;
    height: 100%;
    font-family: "Roboto","Gotham","Helvetica Neue",Helvetica,Arial,sans-serif;
    color: #333;
    position: relative;
}
#valid_cookies{
    display: none !important;
}

.container{
    max-width: 1190px;
    margin: auto;
}
#main{
    display: block;
}
img {
    vertical-align: middle;
    border-style: none;
    max-width: 100%;
    image-rendering: -webkit-optimize-contrast;
    vertical-align: middle;
    border-style: none;
}
header{
    border-bottom: 1px solid #324972!important;
}

.header-item-logo svg{
    padding: 0;
    max-width: 100%;
    display: inline-flex;
    width: 240px;
    margin-top: 10px;
    height: auto;
    pointer-events: none;
}

.header-icon{
    color: #324972;
    cursor: pointer;
}

.show-flex{
    display:flex !important;
}

.d-block{
    display: block !important;
}

.hide{
    display:none !important;
}

@media only screen and (max-width : 767px) {
    /* header img{
        padding: 20px 0 0;
    } */
}

/* @media only screen and (max-width : 600px) {
    .header-item .header-icon{
        margin: 0 !important;
    }
} */

.text-center{
    text-align: center;
}

/* Intro Section */

#intro .center{
    text-align: center;
    margin: auto;
    padding-bottom: 10px;
}

#intro h2{
    transition: opacity 0.1s ease 0s;
    color: #555;
    padding: 20px 0 0;
    font-family: sans-serif;
    font-weight: 700;
    font-size: 1.75rem;
    font-family: Trebuchet MS,sans-serif!important;
    line-height: 1;
    /* font-family: 'Oswald',sans-serif; */
}

#state_select{
    /* width: 167px; */
    position: relative;
    display: inline-block;
    background-size: 13px;
    background-repeat: no-repeat;
    background-position: right;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAgBAMAAACiDzYSAAAABGdBTUEAALGPC/xhBQAAADBQTFRFzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJAAAAzBIJf33wSwAAAA90Uk5TBllQZR8lhE1fA3J1XJAAsD0kKwAAAMhJREFUKM910NsNwjAMhWEvxZiM0BU6Ahv0DYEECrcXQGq9QWUSx3WOI9G3nD+fVIWYXyfuvndZiHlab13RhfhzlxTDrAvxXqRDky6UiUQ014XyhQ7ZQuVCQM+6nClJh5SIHGmRiOZK1gM97hEZuea/HgJykss3ICfldRA1UgqiRkoBBERLQ0C08GAPgaQWtodIQKws9bIAsWIIyVYArWMogC4ci6ONeHG0kVYMOWnFkBMoihqBoqgRLBkBwZIRkFCWNP4pvMPDD0Li7MoZk5jUAAAAAElFTkSuQmCC);
}

#state_select select{
    appearance: none;
    -webkit-appearance: none;
    margin: 0;
    color: #555;
    padding: 0;
    padding-right: 0;
    background: 0 0;
    border: none;
    position: relative;
    outline: 0;
    transition: width .2s;
    font-family: sans-serif;
    font-weight: 700;
    font-size: 1.75rem;
    font-family: Trebuchet MS,sans-serif!important;
}

#state_select:after{
    display: block;
    content: "";
    border-bottom: 2px dashed #ccc;
}

@media only screen and (max-width : 767px) {
    #intro h2{
        font-size: 18px;
        max-width: 100%;
        box-sizing: content-box;
        padding: 10px 10px 0;
    }

    #state_select #state, #state_select #state option{
        font-size: 18px;
    }
}

#intro p{
    margin: 1em 0;
    padding: 0 15px;
    font-size: 1rem;
    color: #333;
    font-family: "Roboto","Gotham","Helvetica Neue",Helvetica,Arial,sans-serif;
    line-height: 1.42857143;
    box-sizing: content-box;
}

@media only screen and (min-width : 768px){
    .hidden-lg{
        display: none;
    }
}

@media only screen and (max-width : 767px) {
    .hidden-xs{
        display: none;
    }
}

/* Results section */
#results {
    display: flex;
    flex-flow: row wrap;
    justify-content: left;
    margin: 0 auto;
    padding: 0 15px;
}

#results .result-item{
    /* margin: 15px auto 0; */
    padding: 15px 11.6px 0;
    flex: 0 1 25%;
    /* padding: 15px; */
}

#results .result-item .item{
    position: relative;
    border: 1px solid #324972;
    background: white;
}

#results .result-item .item::before{
    background-repeat: no-repeat !important;
    position: absolute;
    content: "";
    height: 90px;
    width: 90px;
    right: -4px;
    top: -4px;
    display: none;
}

#results .result-item .item.new-tag::before{
    background: url(/images/ribbon-newopp.svg);
    display: block;
}

#results .result-item .item.now-trending-tag::before{
    background: url(/images/ribbon-now_trending.svg);
    display: block;
}

#results .result-item .item.popular-tag::before{
    background: url(/images/ribbon-popular.svg);
    display: block;
}

#results .result-item .result-img{
    display: flex;
    justify-content: center;
    height: 80px;
    align-items: center;
}

#results .result-item 
.concept-logo{
    max-height: 60px;
    height: 60px;
    width: auto;
    max-width: calc(100% - 20px);
    box-shadow: 0 0 0 1px rgb(0 0 0 / 10%);
}

#results .result-item h3{
    min-height: 45px;
    padding: 0 5px;
    font-size: 1rem;
    font-weight: 700;
    margin: 5px 0 10px;
    line-height: 18px;
    border-bottom: 1px solid #324972;
}

#results .result-item p{
    height: 163px;
    font-size: 0.9rem;
    margin: 0 10px;
    overflow: hidden;
    line-height: 1.42857143;
}

#results .result-item h4{
    padding: 0 10px 10px;
    margin-top: 10px;
    margin-bottom: .5rem;
    color: #000;
    font-size: 0.9rem;
    font-weight: 700;
}

#results .result-item 
.add-concept span span{
    margin-right: 10px;
    font-size: 1.2rem;
}

#results .result-item .merch-banner{
    position: absolute;
    content: "";
    height: 90px;
    width: 90px;
    right: -4px;
    top: -6px;
}

.result-checkbox{
    position: relative;
    cursor: pointer;
}

.result-checkbox .temp-checkbox{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
}

#refresh{
    display: flex;
    justify-content: center;
}

#refresh button{ 
    background: #32ac2f;
    font-size: 18px;
    font-weight: 400;
    max-width: 400px;
    width: 100%;
    color: #fff;
    border: none;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 2rem;
}

#results .nope{
    border-top: 2px dashed #dedede;
    padding: 10px 0 0;
    text-align: center;
}

@media only screen and (max-width : 1024px) {
    #results .result-item{
        flex: 0 1 33.33%;
    }
}

@media only screen and (max-width : 768px) {
    #results .result-item{
        padding: 15px 7.375px 0;
        flex: 0 1 50%;
    }

    #results .result-item h3{
        min-height: auto;
        padding-bottom: 10px;
    }
}

@media only screen and (max-width : 767px) {
    #results .result-item p{
        height: auto;
    }
}

/* FCOM Header Section */
header#header{
    position: sticky;
    top:0;
    background: #FFF;
    z-index: 25;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 25%);
}
.header-row{
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    /* max-width: 75rem; */
    margin: 0 auto;
}

.header-item{
    padding: 20px 15px;
    /* margin-bottom: 15px; */
    flex: 0 1 25%;
    margin: auto 0;
}

.header-item-logo{
    padding: 0 !important;
    /* margin-bottom: 15px; */
    flex: 0 1 50%;
    text-align: center;
    margin: 0;
    justify-content: center;
    align-items: center;
}

.header-item .header-icon{
    font-size: 1.2rem;
    margin: 0 20px;
}
.header-icon#cart{
    color: #dedede;
}

span#total {
    background-color: #F00;
    font-size: 12px;
    font-weight: 600;
    color: #FFF;
    display: none;
    padding: 0 6px;
    justify-content: center;
    align-items: center;
    border-radius: 22px;
    height: 20px;
    position: absolute;
    top: -5px;
    right: 10px;
}
body.cart span#total{
    display: inline-flex;
}
.text-end{
    text-align: right;
}
.header-item.text-end{
    position: relative;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: flex-end;
}
svg#basketPath{
    height: 35px;
    width: auto;
    fill:#dcdbd8;
    cursor: pointer;
    margin: 0 20px;
}
body.cart svg#basketPath{
    fill: #324972;

}
@media only screen and (max-width: 767px) {
    .header-item-logo{
        padding: 0 15px !important;
    }

    .header-item-logo svg{
        margin-top: 20px;
    }

    .header-row{
        justify-content: flex-start;
        flex-wrap: nowrap;
    }

    .header-item{
        width: 25%;
    }
}

/* FCOM Footer Section */

#footer-fcom{
    background-color: #3d4454;
    width: 100%;
    color: #fff;
    margin-top: 30px;
}

#footer-fcom .upper{
    background-color: #383f4d;
    line-height: 50px;
    height: 50px;
    margin: 0;
}

#footer-fcom .upper 
.follow a{
    color: #90a0c6;
    text-decoration: none;
    font-size: 1.5rem;
    margin: 0 0 0 5px;
}
#footer-fcom .upper 
.follow svg{
    fill: #90a0c6;
    height:1.5rem;
}

#footer-fcom .lower{
    font-size: 0.9rem;
    padding-bottom: 10px;
}

#footer-fcom .lower h2{
    padding: 0;
    font-size: 1.1rem;
    margin: 0 0 15px;
    font-weight: 700;
    border-bottom: 1px dotted #999;
    text-transform: uppercase;
}

#footer-fcom .lower .lower-row{
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    margin: 0 auto;
}

#footer-fcom .lower .row-item{
    padding: 0 !important;
    margin-bottom: 15px;
    flex: 0 1 calc(25%);
}

#footer-fcom .lower .row-item ul{
    list-style: none;
    padding-left: 0;
}

#footer-fcom .lower 
.row-item ul li{
    line-height: 23px;
}

#footer-fcom .lower 
.row-item ul li a, .lower 
.row-item a, .lower .contact-details a{
    text-decoration: none;
    color: #90a0c6;
    font-size: 0.95rem;
}

#footer-fcom .lower 
.row-item .ifa-img{
    width: 100%;
}

.upper .container, .lower .container{
    margin: auto;
}

.lower .footer-contact{
    display: flex;
}

.lower .footer-contact .ct{
    width: 50%;
}

@media only screen and (max-width: 767px) {
    #footer-fcom .lower 
    .lower-row{
        padding : 15px;
    }
}

@media only screen and (max-width: 767px){
    .upper .follow{
        text-align: start !important;
    }

    #footer-fcom .lower h2{
        margin: 16px 0 8px;
    }

    #footer-fcom .lower .copyright{
        text-align: center !important;
        margin: 20px 0 0;
    }
}

/* Views Style */

.view-ports{
    height: 100vh;
    overflow: hidden;
}

/* #mobileView{
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    background: #fff;
    width: 100%;
} */

#mobileView .smartbanner{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
}

#mobileView .smartbanner.smartbanner-ios{
    background: linear-gradient(to bottom,#f4f4f4,#cdcdcd);
    box-shadow: 0 1px 2px rgb(0 0 0 / 50%);
    line-height: 80px;
    width: 100%;
    height: 80px;
}

#mobileView .smartbanner.smartbanner-android {
    background: #3d3d3d url(data:image/gif;base64,R0lGODlhCAAIAIABAFVVVf///yH5BAEHAAEALAAAAAAIAAgAAAINRG4XudroGJBRsYcxKAA7);
    box-shadow: inset 0 4px 0 #88b131;
    line-height: 82px;
}

.smartbanner .smartbanner-close{
    border: 0;
    width: 18px;
    height: 18px;
    line-height: 18px;
    color: #888;
    text-shadow: 0 1px 0 #fff;
    display: inline-block;
    vertical-align: middle;
    margin: 0 5px 0 5px;
    font-family: ArialRoundedMTBold,Arial;
    font-size: 1.2rem;
    text-align: center;
    text-decoration: none;
    border-radius: 14px;
    -webkit-font-smoothing: subpixel-antialiased;
}

.smartbanner.smartbanner-android .smartbanner-close{
    border: 0;
    width: 17px;
    height: 17px;
    line-height: 17px;
    margin-right: 7px;
    color: #b1b1b3;
    background: #1c1e21;
    text-shadow: 0 1px 1px #000;
    box-shadow: 0 1px 2px rgb(0 0 0 / 80%) inset, 0 1px 1px rgb(255 255 255 / 30%);
}

.smartbanner .smartbanner-icon img{
    box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    display: inline-block;
    vertical-align: middle;
    width: 57px;
    height: 57px;
    margin-right: 12px;
    border-radius: 10px;
}

.smartbanner-info{
    display: inline-block;
    vertical-align: middle;
    width: 44%;
    font-size: 0.7rem;
    line-height: 1.2em;
    font-weight: 700;
    font-family: 'Helvetica Neue',sans-serif;
}

.smartbanner.smartbanner-ios .smartbanner-info{
    color: #6a6a6a;
    text-shadow: 0 1px 0 rgb(255 255 255 / 80%);
}

.smartbanner.smartbanner-android .smartbanner-info{
    color: #ccc;
    text-shadow: 0 1px 2px #000;
}

.smartbanner .smartbanner-title{
    font-weight: 700;
    font-size: 0.8rem;
    line-height: 18px;
    font-family: 'Helvetica Neue',sans-serif;
}

.smartbanner.smartbanner-ios .smartbanner-title{
    color: #4d4d4d;
}

.smartbanner.smartbanner-android .smartbanner-title{
    color: white;
}

.smartbanner-button{
    position: absolute;
    right: 20px;
    top: 0;
    bottom: 0;
    margin: auto 0;
    height: 24px;
    font-size: 0.9rem;
    line-height: 24px;
    text-align: center;
    font-weight: 700;
    text-transform: uppercase;
    text-decoration: none;
}

.smartbanner.smartbanner-ios .smartbanner-button{
    padding: 0 10px;
    min-width: 10%;
    color: #6a6a6a;
    background: #efefef;
    background: linear-gradient(to bottom,#efefef,#dcdcdc);
    border-radius: 3px;
    box-shadow: inset 0 0 0 1px #bfbfbf, 0 1px 0 rgb(255 255 255 / 60%), 0 2px 0 rgb(255 255 255 / 70%) inset;
    color: #6a6a6a;
    text-shadow: 0 1px 0 rgb(255 255 255 / 80%);
}

.smartbanner.smartbanner-android .smartbanner-button{
    min-width: 12%;
    color: #d1d1d1;
    padding: 0 10px;
    border-radius: 0;
    background: linear-gradient(to bottom,#42b6c9,#39a9bb);
    box-shadow: 0 0 0 1px #333, 0 0 0 2px #dddcdc;
    color: #d1d1d1;
}

#path-direction {
    margin: auto;
    padding: 15px;
    width: 400px;
    max-width: 100%;
    font-family: 'Roboto',"Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 1rem;
    color: #333;
    background-color: #fff;
    line-height: 1.42857143;
}

.path h1, .path h2, .path h3 {
    line-height: 1.1;
    margin: 0;
    padding: 0 10px 10px;
    font-weight: 700;
    color: #555;
    font-size: 1.1rem!important;
}

#zip_input{
    background-color: #324972;
    position: relative;
    color: white;
    font-size: 18px;
    text-align: center;
    width: 100%;
    height: 45px;
    border-radius: 5px;
    border: 2px solid #cc1209!important;
}

#zip_inpuut:focus{
    border: 2px solid #f80;
}



.path img {
    display: block;
    margin: 5px auto;
}
#metro_area:empty:before{
    content: 'Your Area';
}

#soc_proof{
    background-color: #c9daf8!important;
    position: relative;
    padding: 5px;
    font-weight: bold;
    text-align: center;
}

.red_button{
    margin-top: 10px;
    background-color: rgb(50,172,47);
    width: 100%;
    border: none;
    height: 45px;
    box-shadow: 2px 2px 5px #666;
    border-radius: 5px;
    color: #fff;
    text-transform: uppercase;
    font-size: 1.1rem;
    line-height: 1.25;
    padding: 6px 15px;
    text-align: center;
    transition: box-shadow ease-in-out .5s;
}

#mobileView #header{
    display: none;
}

#mobileView input::-webkit-input-placeholder {
    color: #ddd;
}
#mobileView input:-moz-placeholder {
    /* FF 4-18 */
    color: #ddd;
    opacity: 1;
}
#mobileView input::-moz-placeholder {
    /* FF 19+ */
    color: #ddd;
    opacity: 1;
}
#mobileView input:-ms-input-placeholder {
    /* IE 10+ */
    color: #ddd;
}
#mobileView input::-ms-input-placeholder {
    /* Microsoft Edge */
    color: #ddd;
}
#mobileView input::placeholder {
    /* modern browser */
    color: #ddd;
}

#mobileView input:focus::placeholder {
    color: transparent;
}

#mobileView{
    display: none;
}

@media only screen and (max-width : 600px) {
    #mobileView{
        display: block;
        /* height: 100vh; */
        background: #fff;
        /* position: fixed;
        top: 0;
        left: 0;
        z-index: 1; */
    }
}

/* Listing Buttons */

.listing button {
    background-color: #324972;
    color: #FFF;
    border: none;
    display: block;
    /* width: calc(100% + 1.25rem); */
    font-size: 0.875rem;
    padding: 0.95rem 0.6rem;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 18px 10px;
    text-transform: uppercase;
}

.listing button::before{
    content: '';
    margin-right: 10px;
    height: 0.7rem;
    width: 0.7rem;
    display: inline-block;
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
    -webkit-mask-size: cover;
    mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: #FFF;
}

.listing input:checked + button{
    /* background-color: #0957a0; */
    background-color: #CC1209; 
    cursor: pointer;
}

.listing input:checked + button:before{
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
    -webkit-mask-size: cover;
    mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
}

.listing input:checked + button span{
    font-size: 0px;
}

.listing input:checked + button span::after{
    font-size: 0.9rem;
    content: 'Added to cart';
}

/* Container width calculation */

@media only screen and (min-width: 769px) and (max-width: 991px){
    #results .result-item{
        flex: 0 1 calc(33.33%);
        /* margin: 15px; */
    }

    #footer-fcom .lower .lower-row{
        padding: 15px;
    }

    #footer-fcom .lower .row-item{
        flex: 0 1 calc(50%);
    }
}

@media only screen and (max-width: 767px){
    #results .result-item, 
    #footer-fcom .lower .row-item {
        flex: 0 1 calc(50%);
        padding: 10px;
    }

    .follow, .footer-contact{
        padding: 0 15%;
    }
}

@media only screen and (max-width: 600px){
    #results{
        padding: 0 10px;
    }

    #results .result-item,
    #footer-fcom .lower .row-item {
        flex: 0 1 calc(100%) !important;
    }

    .footer-contact{
        flex-direction: column;
    }

    .lower .footer-contact .ct{
        width: 100% !important;
    }

    .copyright{
        margin: 10px 0;
    }
}


/* Modal Section */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99999;
    background: rgba(0,0,0,.75);
    display: none;
    transition: all .25s ease-out; 
}
.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}
.guts {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #FFF;
    display: flex;
    justify-content: center;
    max-height: 90%;
}

@media only screen and (max-width: 991px){
    .guts {
        width:calc(100% - 1.25rem);
    }
}

#waiting{
    display: none;
}

#waiting.wait {
    margin: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transition: opacity .3s ease;
    display: block;
    background-size: 7.5rem 7.5rem;
    background-image: linear-gradient(45deg, rgba(255,255,255, .6) 25%, rgba(255,255,255, .3) 25%, rgba(255,255,255, .3) 50%, rgba(255,255,255, .6) 50%, rgba(255,255,255, .6) 75%, rgba(255,255,255, .3) 75%, rgba(255,255,255, .3));
    animation: barberpole 2s linear infinite;
    z-index: 999999;
}

@keyframes barberpole {
    from {
      background-position: 0 0;
    }
    to {
      background-position: 15rem 7.5rem;
    }
  }

/* Bottom Cart Section */

#bottom{
    position: fixed;
    bottom: -5rem;
    right: 0;
    left:0;
    transition: bottom .5s ease-out;    
    background-color: #324972;
    z-index: 16;
    font-family: 'Roboto',"Helvetica Neue",Helvetica,Arial,sans-serif;
}
#bottom.show{
    bottom: 0;
}

button#cart_bottom {
    background: none;
    border: none;
    margin: 0 auto;
    display: flex;
    max-width: 100%;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    color: #FFF;
    font-size: 1rem;
    padding: 10px;
}
button#cart_bottom .cart_count {
    background: #FFF;
    display: block;
    height: 40px;
    width: 40px;
    line-height: 40px;
    border-radius: 50%;
    color :#cc1209;
    font-weight: 600;
    font-size: 1.2rem;
}
button#cart_bottom .button{    
    background-color: #cc1209;
    color: #FFF;
    height: 40px;
    padding: 0 25px;
    text-transform: uppercase;
    line-height: 40px;
    font-size: 1.2rem;
    height: 100%;
    font-weight: 700;
}
button#cart_bottom .text {
    color: white;
    text-transform: uppercase;
    padding: 0 15px;
    font-weight: 700;
    font-size: 1.2rem;
}

button#cart_bottom .text a{
    color: white;
}

.plural button#cart_bottom .text em:before{
    content: "s";
}

/* Accept Cookies Section */

#valid_cookies {
    background: rgba(0,0,0,.8);
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99999;
    color: #FFF;
    -webkit-backdrop-filter: blur(.625rem);
    backdrop-filter: blur(.625rem);
}
#valid_cookies.show {
    display: block;
}
#valid_cookies .flexxer {
    max-width: 90%;
    width: 56.25rem;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    align-items: center;
}
#valid_cookies .flexxer p {
    padding-right: .938rem;
    margin: 1.25rem 0;
    font-size: 1rem;
}
#valid_cookies .flexxer p a {
    color: #eee;
    text-decoration: underline;
}
#valid_cookies button.closer {
    font-weight: bold;
    text-transform: uppercase;
    padding: .625rem .938rem;
    line-height: 1;
    background-color: rgb(29, 161, 242);
    color: #FFF;
    border: none;
    display: block;
    width: 100%;
    font-size: 1rem;
    cursor: pointer;
    height: 2.125rem;
}

#top{
    display: none;
}

@media only screen and (max-width: 767px){
    button#cart_bottom .text {
        font-size: 12px;
        white-space: nowrap;
    }
    button#cart_bottom .button {
        padding:0 10px;
    }
    .hide_area #mobile-view, body:not(.hide_area) #main{
        display: none !important;
    }
    body:not(.hide_area) #footer-fcom .lower .lower-row, body:not(.hide_area) #footer-fcom .upper{
        display:none;
    }
    body:not(.hide_area) #footer-fcom .lower{
        background: #FFF;
    }
    body:not(.hide_area) #footer-fcom .lower .footer-contact{
        padding: 0 10px 10px;
    color: #333;
    }
    body:not(.hide_area) #footer-fcom .lower .ct.contact-details {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-evenly;
        font-weight: 500;
    }
    .hidden-xs{
        display:none !important;
    }
}

@media only screen and (min-width: 768px){
    #mobile-view{
        display: none !important;
    }
    #top {
        position: fixed;
        bottom: .625rem;
        right: .625rem;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all .5s ease-out;
        display: block;
        z-index: 20;
    }
}
#top.show{
    visibility:visible;
    opacity: .5;
}

#top:before{
    content: '';
    height: 3.75rem;
    width: 3.75rem;
    display: inline-block;
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMjRjNi42MjcgMCAxMi01LjM3MyAxMi0xMnMtNS4zNzMtMTItMTItMTItMTIgNS4zNzMtMTIgMTIgNS4zNzMgMTIgMTIgMTJ6bS0yLTloNHYxaC00di0xem0wIDN2LTFoNHYxaC00em0yLTEzbDYgNmgtNHYzaC00di0zaC00bDYtNnoiLz48L3N2Zz4=');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMjRjNi42MjcgMCAxMi01LjM3MyAxMi0xMnMtNS4zNzMtMTItMTItMTItMTIgNS4zNzMtMTIgMTIgNS4zNzMgMTIgMTIgMTJ6bS0yLTloNHYxaC00di0xem0wIDN2LTFoNHYxaC00em0yLTEzbDYgNmgtNHYzaC00di0zaC00bDYtNnoiLz48L3N2Zz4=');
    -webkit-mask-size: cover;
    mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: #9cb2bc;
}

#phone_tip {
    position: fixed;
    z-index: 999999;
    background: #8dc63f;
    color:#FFF;
    width: 12.5rem;
    max-width: calc(100% - 2.5rem);
    font-size: .875rem;
    padding: .625rem;
    visibility: hidden;
    top:calc(50% - 6.25rem);
    left:calc(50% - 6.25rem);
    border-radius: .938rem .313rem 0;
}
#phone_tip:after {
	top: 100%;
	right: 0;
	content: " ";
	position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 .625rem .625rem 0;
    border-color: transparent #8dc63f transparent transparent;
}
#phone_tip.tipped{ 
    visibility: visible;
}
.requested {
    color: #888;
    display: block;
    font-size: 0.875rem;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 18px 10px;
    text-transform: uppercase;
    background: #ccc;
    text-align: center;
}
#sort-results{
    display: flex;
    align-items: center;
    justify-content: center;
}
#sort-results span {
    font-weight: 700;
    font-family: 'Roboto', sans-serif;
    display: inline-flex;
    padding: 15px 7.5px;
    border: 1px solid #324972;
    cursor: pointer;
    margin: 0;
    font-size: 14px;
    text-transform: uppercase;
    transition: opacity .2s ease-in-out;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
}
#sort-results span:not(:first-child){
    border-left:none;
}
#sorting-results {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #4a4a4a;
    padding: 5px 15px 15px;
    border-bottom: 2px dashed #dedede;
}
#sorting-results::before {
    content: 'Order by:';
    display: inline-block;
    font-size: 1.1rem;
    font-family: Trebuchet MS,sans-serif!important;
    margin-right: 0.5rem;
    font-weight: 600;
}
@media only screen and (max-width: 767px){
    #sorting-results{
        flex-direction: column;
        padding: 5px 0 15px;
    }
#sorting-results:before {
    display: block;
    margin-bottom: 5px;
    text-align: center;
}}
#sort-results span.checked {
    cursor: default;
    background-color: #324972;
    border-color: #324972;
    color: #FFF;
    opacity: 1 !important;
}
#sort-results span svg {
    display: inline-flex;
    height: 0.75rem;
    width: 0.75rem;
    margin-left: 0.25rem;
    fill:#324972;
    opacity: .5;
}
#sort-results span.checked svg{
    fill:#fff;
}
#sort-results span.checked:not(#sort_order.checked){
    cursor: pointer;
}

/* LOADING */

#loading,#page_loading{
    position: fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index: 999999;
    display: none;
}
#loading.wait{
    display: block;
}
#loading div,#page_loading div {
    width: 100%;
    height: 100%;
    display:table;
    background: rgba(51,60,78,.75);
}
#loading div i,#page_loading i {
   display:table-cell;
    vertical-align:middle;
    text-align:center;
    font-size: 5rem;
    color:#FFF;
}


/* FILTER */
div#filter.version2{
    background: #404859;
    padding: 5px;
    color:#FFF;
}
div#filter.version2 .container{
    background-color: #3d4454;
    border-radius: 5px;
    text-align: center;
    padding-right: var(--bs-gutter-x,.75rem);
    padding-left: var(--bs-gutter-x,.75rem);
    border-radius: 5px;
}
.version2 #filters label {
    display: none;
}

.version2 #filter_toggle {
    font-size: 24px;
    padding: 10px 15px;
    text-align: left;
    line-height: 1;
    color: #fff;
    background-color: #383f4d;
    border-radius: 0;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px 5px 0 0;
    margin: 0 -15px;
}
.version2 #filter_form.closed #filters {
    display:none !important;
}
.version2 #filter_form.closed #filter_toggle{
    border-radius: 5px;
}

.version2 #filter_toggle svg{
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

.version2 #filter_form.closed #filter_toggle i, 
.version2 .rotate{
    -webkit-transform: rotate(360deg) !important;
    transform: rotate(360deg) !important;
}

.version2 .rotate{
    transition: all 0.2s;
}

/* #filter_form.closed #filter_toggle i,#toc-slider.closed #toc-toggle i{
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
} */
div#filter.version2 #filters{
    padding: 15px 0;
}
div#filter.version2 button, .version2 .changebutton button {
    color: #fff;
    font-weight: 200;
    padding: 10px;
    width: 100%;
    display: table;
    border-radius: 6px;
    font-size: 18px;
    font-weight: 400;
    cursor: pointer;
    margin: 0;
    border: none;
    height: 40px;
    background: #32ac2f;
}
div#filter.version2 select {
    border: 1px solid #90a0c6;
    height: 40px;
    background-image: url(https://dgsgass4s0wnj.cloudfront.net/image/select.png);
    padding: 0 10px;
    border-radius: 6px;
    box-sizing: border-box;
    margin: 0;
    width: 100%;
    appearance: none;
    -webkit-appearance: none;
    background-position: right 10px center;
    background-color: #fff;
    background-repeat: no-repeat;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

@media only screen and (min-width: 992px) {
    /* FILTER */
    div#filter.version2 {
        padding: 20px;
    }
    div#filter.version2 #filters {
        padding: 25px 0;
    }
    .version2 #filter_toggle span {
        font-family: 'Montserrat', sans-serif;
    }

    div#filter.version2 #filters{
        display: flex;
    }

    div#filter.version2 .col-md-3 {
        flex: 0 0 auto;
        width: 25%;
    }

    div#filter.version2 #filters .col-md-3 {
        padding-top: 0!important;
        padding-left: 12.5px;
        padding-right: 12.5px;
    }

    div#filter.version2 #filters .col-md-3:nth-child(1) {
        padding-left: 25px;
    }

    div#filter.version2 #filters .col-md-3:last-child{
        padding-right: 25px;
    }

    div#filter.version2 button {
        white-space: nowrap;
        font-size: 95%;
    }
}

@media only screen and (max-width: 850px) {
    .version2 #filters .col-12{
        margin: 0 0 10px;
    }
}

@media only screen and (max-width: 768px){

    div#filter.version2 button{
        line-height: 1;
    }

    .show-flex{
        display:block !important;
    }

    .version2 #filters .col-12:last-child{
        margin: 0;
    }

    .version2 #filters .col-12 {
        margin: 0 0 10px;
    }
}

/* View more buttons */
.center_guts {
    display: flex;
    flex-direction: column;
    /* flex-flow: row wrap; */
    justify-content: center;
    margin: 0 auto;
    max-width: calc(100% - 1rem);
    align-items: center;
}

a.continue_link,a.continue{
    padding: 10px;
    min-width: 300px;
    width: auto;
    text-align: center;
}

a.continue{
    background: #28af32;
    color: #fff;
    text-decoration: none;
    margin: 10px 0;
}

/* Backup Results Message */
#backup-results:not(:empty) {
    display: flex;
    flex-flow: row wrap;
    justify-content: left;
    margin: 0 auto;
    padding: 0 15px;
}
#backup-results:not(:empty) div {
    border-top: 2px dashed #dedede;
    padding: 10px 0 30px;
    text-align: center;
    display: block;
    width: 100%;
}

#para-new strong{
    text-transform: capitalize;
}
#para-new{
    background-color: #0957a0;
    color: white;
    padding: 10px;
}
#see-more-btn{
    text-align: center;
    margin: 40px;
}
#see-more-btn a{
    padding: 0.6em 0.5em;
    margin: 0 8px 8px;
    font-size: 1.2rem;
    font-weight: bold;
    background-color: #f26721;
    color: #fefefe;
    border-radius: 2px;
    transition: background-color .25s ease-out,color .25s ease-out;
    line-height: 1;
    text-transform: capitalize;
    border: none;
    text-decoration: none;
}