/* Base */
a{
    color: var(--button_blue);
}
h1,h2,h3 {
    line-height: 1;
    margin-bottom: .5rem;
    text-align: center;
    font-weight: 400;
    font-size: 1.25rem;
    color: var(--header);
}
#main:has(h1 + h2) h1:first-child{
    margin-bottom: .25rem;
}
h1 + h2{
    font-size: 1rem;
    font-weight: 300;
    margin-bottom: 1rem;
    text-align: center;
}
h1:not(:first-of-type){
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px dashed var(--border_blue);
    position: relative;
    z-index: 3;
}
.sticky{
    position: sticky;
    top: 0rem;
    z-index: 2;
    background-color: var(--main_blue);
}

.d-block{
    display: block !important;
}

body:has(#franchise_columns) .columns.sticky li{
    width: calc(50vw - 2rem);
    margin: 0 .5rem 1rem;
}

.red{
    color: var(--red);
}

.small{
    font-size: 80%;
}

.error,.error_msg{
    color: var(--red);
    font-size: .85rem;
    margin: 0.5rem 0.25rem 0;
    line-height: 1.5;
    font-weight: 400;
}

.error-outline{
    border-color: var(--red) !important;
}

.readonly{
    background: var(--main_blue);
    pointer-events: none;
}

.success_msg{
    text-align: center;
    background: var(--green);
    padding: 0.5rem;
    color: var(--white);
}

.error strong, .error_msg strong {
    font-weight: bold;
}

.style_ul{
    list-style: disc;
    padding-left: 1rem;
}

/* Slider */
.ui-slider {
    position: relative;
    text-align: left;
}
.ui-slider-horizontal {
    height: 0.8em;
}
.ui-widget {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.ui-widget-content {
    border: 1px solid var(--gray);
    background: var(--white);
}
.ui-corner-all{
    border-radius: 3px;
}
.ui-widget.ui-widget-content {
    background: var(--gray);
    height: .5rem;
    border-radius: 1rem;
    border: none;
    position: relative;
    margin: .5rem .5rem 1rem;
}
.ui-widget-header {
    background: var(--logo_blue);
}
.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0;
}
.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}
.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1.2em;
    height: 1.2em;
    cursor: pointer;
    -ms-touch-action: none;
    touch-action: none;
}
.ui-slider-horizontal .ui-slider-handle {
    top: -0.3em;
    margin-left: -0.6em;
}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
    font-weight: normal;
    outline: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    cursor: pointer;
    position: absolute;
    border: 2px solid var(--logo_blue);
    box-sizing: border-box;
    box-shadow: none;
    background: var(--white);
}
input#investment_amount,input#franchise_units_amount,input#investment_amount2,input#franchise_units_amount2 {
    border: none;
    padding-left: .5rem;
    color: var(--logo_blue);
    font-weight: 500;
}

/* PopUp */
body:has(.popup.show,.modal.show){
    overflow:hidden;
}
.popup:not(.show){
    display:none;
}
.popup.show{
    display: flex;
    inset: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 5;
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(.5rem);
}
.popup .guts{
    background: var(--white);
    display: block;
    padding: 1rem 0;
    width: 25rem;
    max-width: calc(100% - 2rem);
    box-sizing: border-box;
    margin: 0 auto;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
    border-radius: .5rem;
    position: relative;
    max-height: calc(100% - 2rem);
}
.popup .guts .content {
    padding: 0 1rem;
    overflow: auto;
    height: 100%;
}
.popup h3{
    margin-bottom: 1.5rem;
}
.popup label{
    font-weight: 500;
}
.popup .row:not(:last-child){
    margin-bottom: .5rem;
}

/* Alert Modal */
#alert{
    display: none;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 150;
    background: rgba(0,0,0,.5);
    backdrop-filter: blur(.5rem);
}
#alert.show{
    display: flex;
}
.alert{
    display: block;
    background: var(--white);
    padding: 1rem;
    width: 25rem;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    border-radius: .5rem;
    position: relative;
}
.alert h3{
    text-align: left;
}
.alert p{
    margin: 1rem 0;
}

/* Modal */
body:has(.modal.show) #main{
    overflow:hidden;
    padding-top: 0;
    border-top: none;
}
.modal:not(.show){
    display:none;
}
.modal.show{
    display: flex;
    inset: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 5;
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(.5rem);
    padding:0;
    width:100%;
}
.modal .guts{
    background: var(--white);
    display: block;
    padding: 0 1rem 1rem;
    width: 65rem;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
    position: relative;
    overflow-y: auto;
    height: 100vh;
}
.modal h3{
    margin-bottom: 1.5rem;
}
.modal_banner {
    position: sticky;
    padding: 1rem;
    top: 0;
    margin: 0 -1rem 1rem;
    background: var(--white);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    border-bottom: 1px dashed var(--gray);
}
.close_modal{
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
}
.close_modal:after{
    content: attr(data-before);
    display: inline-block;
    padding-left: .5rem;
}
.close_modal svg{
    height: 1.5rem;
    width: 1.5rem;
    fill: var(--gray);
}

/* Menu */
.overlay{
    background: rgba(0,0,0,0);
    position: fixed;
    top: 0;
    left: 0;
    right: 100%;
    bottom: 0;
    z-index: 100;
    transition: background .2s ease;
    display: none;
}
.overlay.show{
    right: 0;
    display: block;
    background: rgba(0,0,0,.5);
}
.overlay:before{
    content: '\00D7';
    position: fixed;
    top: 1rem;
    right: 18.15rem;
    color: var(--white);
    font-size: 2rem;
    line-height: 1;
    transition: opacity .2s ease-in;
    opacity: 0;
    background: rgba(255, 255, 255, .25);
    display: inline-flex;
    width: 1.5rem;
    height: 1.5rem;
    align-items: center;
    justify-content: center;
    border-radius: .25rem;
}
.overlay.show:before{
    opacity: 1;
    transition-delay: .1s;
    transition-property: opacity;
    transition: opacity .1s ease-out;
}
#menu_modal {
    transition: all .5s ease;
    position: fixed;
    background: #FFF;
    top: 0;
    bottom: 0;
    right: -18rem;
    width: 18rem;
    z-index: 100;
}
#menu_modal.show {
    right: 0 !important;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
}
.menu_header{
    padding: 1rem;
    color: var(--white);
    background-color: var(--header);
    line-height: 1.2;
    text-align: center;
}
.menu_header .avatar {
    background: var(--gray);
    display: inline-flex;
    width: 3rem;
    height: 3rem;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    border-radius: 3rem;
    margin-bottom: .5rem;
    color: var(--header);
}
.menu_header .name{
    font-weight: 300;
}
.menu_header .email {
    color: var(--gray);
    font-size: .85rem;
    word-break: break-all;
}
#menu_modal ul{
    padding: 1.5rem 1rem 1rem;
}
#menu_modal li {
    padding: 0 0 .5rem;
}
#menu_modal li a {
    color: #000;
    text-decoration: none;
    font-weight: 300;
}
#menu_modal li svg {
    height: 1rem;
    width: 1rem;
    vertical-align: middle;
    fill: var(--gray);
    margin-right: .5rem;
}
#menu_modal .button{
    margin: 0 1rem 1rem;
}

/* Header */
#header {
    background-color: var(--white);
    border-bottom: 1px solid var(--logo_blue);
    height: 4rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    box-sizing: border-box;
    position: fixed;
    inset: 0 0 auto 0;
    z-index: 2;
}
#header svg{
    height: 1.5rem;
    width: auto;
}
#header img{
    height: 1.5rem;
    width: auto;
}
@media only screen and (min-width: 24rem) {
    #header img{
        height: 1.75rem;
    }
}
.desktop_nav{
    display: none;
}
#header_nav{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
#header_nav{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 1rem;
    grid-row-gap: 0px;
    width: 6rem;
}
#header_nav div{
    cursor: pointer;
}
.notifications_nav{ 
    grid-area: 1 / 1 / 2 / 2;
    position: relative;
}
.favorites_nav{
    grid-area: 1 / 2 / 2 / 3;
    position: relative;
}
.menu{
    grid-area: 1 / 3 / 2 / 4;
}
#header_nav svg{
    fill: var(--button_blue);
    height: 1.5rem;
    width: auto;
}
:is(.notifications_nav:not([data-count="0"]),.favorites_nav:not([data-count="0"])):before{
    content: attr(data-count);
    position: absolute;
    right: -.25rem;
    top: -.25rem;
    background: var(--logo_red);
    color: var(--white);
    display: inline-flex;
    line-height: 1;
    font-size: .75rem;
    font-weight: 600;
    padding: .1rem .2rem;
    border-radius: .5rem;
    min-width: .5rem;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

/* Shell */
#shell { 
    height: 100vh;
    display: flex;
    flex-direction: column-reverse;
}
/* .bottom-align{
    z-index: 999;
    position: fixed;
    bottom :0rem;
} */
/* Aside */
#aside {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4rem;
    padding: 0;
    border-top: 1px solid var(--gray);
    background: var(--white);
    z-index: 2;
    position: fixed;
    inset: auto 0 0 0;
}
#aside ul{
   display: grid;
   grid-auto-columns: minmax(0, 1fr);
   grid-auto-flow: column;
   width: 100%;
}
#aside a {
    text-decoration: none;
    color: var(--dark_gray);
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: .65rem;
    padding: .25rem;
}
#aside svg{
    height: 1.5rem;
    width: auto;
    fill: var(--dark_gray);
}
#aside li.active :is(a,svg){
    fill: var(--button_blue);
    color: var(--button_blue);
}

/* Main */
#main{
    min-height: 100vh;
    border-top: 1.5rem solid var(--main_blue);
    padding: 4rem 1rem 5rem;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
    background: var(--main_blue);
    width: 100%;
}