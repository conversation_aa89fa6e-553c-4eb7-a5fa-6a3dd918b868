html, body{
	font-size: 100%;
    font-family: sans-serif;
	vertical-align: baseline;
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    }
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, textarea, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
}
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
    overflow-x: hidden;
}
ol, ul, dl {
    margin: 0;
    padding: 0;
    list-style-type: none;
}
.text-right{
    text-align:right;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
img{
    vertical-align: middle;
    border-style: none;
    max-width: 100%;
    image-rendering: -webkit-optimize-contrast;
}
a{
    color: #0d82df;
    text-decoration: none;
}
*{
    outline: none;
}
strong,b{
    font-weight: 700;
}
em,i{
    font-style: italic;
}
p {
    margin-bottom: 1.5rem;
}