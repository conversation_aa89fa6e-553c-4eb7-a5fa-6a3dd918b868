/* OVERALL */
html, html body {
    padding: 0;
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    background-color: #FFF;
    color: #000;
    font-family: "Poppins", sans-serif;
    font-weight: 200;
    font-style: normal;
}
h1, h2, h3 {
    line-height: 1;
    margin-bottom: .5rem;
    text-align: center;
    font-weight: 400;
    font-size: 1.25rem;
    color: var(--header);
}
h1 + h2 {
    font-size: 1rem;
    font-weight: 300;
    margin-bottom: 1rem;
    text-align: center;
}
.text-center{
    text-align: center;
}

/* SHARED */
.cancel_link{
    color: var(--button_blue);
    text-decoration: underline;
    cursor: pointer;
    margin-top: .5rem;
    display: inline-block;
}
button,.button{
    border-radius: .25rem;
    display: inline-flex;
    height: 2rem;
    padding: 0 1rem;
    color: var(--button_blue);
    background-color: var(--white);
    border: 2px solid var(--button_blue);
    box-sizing: border-box;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 1rem;
}
:is(button,.button).solid{
    color: var(--white);
    background-color: var(--button_blue);
}
.buttons{
    text-align: center;
    margin-top: 1rem;
}
.button.delete{
    background-color: var(--red);
    border-color: var(--red);
}
span.button.disabled{
    color: var(--white);
    background-color: var(--gray);
    border-color: var(--gray);
    cursor: not-allowed;
}

/* LOADING */
#loading{
    position: fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index: 999999;
    display: none;
}
#loading.show{
    display: block;
}
#loading div {
    width: 100%;
    height: 100%;
    display: flex !important;
    justify-content: center;
    align-items: center;
    background: rgba(51,60,78,.75);
}
@keyframes rotating {
    from{
        transform: rotate(0deg);
    }
    to{
        transform: rotate(360deg);
    }
}
#loading div svg{
    width: 80px;
    height: 80px;
    animation: rotating 2s linear infinite;
}

/* PopUp */
body:has(.popup.show,.modal.show){
    overflow:hidden;
}
.popup:not(.show){
    display:none;
}
.popup.show{
    display: flex;
    inset: 0;
    position: fixed;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    z-index: 5;
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(.5rem);
}
.popup .guts{
    background: var(--white);
    display: block;
    padding: 1rem 0;
    width: 25rem;
    max-width: calc(100% - 2rem);
    box-sizing: border-box;
    margin: 0 auto;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
    border-radius: .5rem;
    position: relative;
    max-height: calc(100% - 2rem);
}
.popup .guts .content {
    padding: 0 1rem;
    overflow: auto;
    height: 100%;
}
.popup h3{
    margin-bottom: 1.5rem;
}
.popup label{
    font-weight: 500;
}
.popup .row:not(:last-child){
    margin-bottom: .5rem;
}

/* MODAL */
.modal{
    background: var(--white);
    display: block;
    padding: 0 1rem 1rem;
    width: 67.5rem;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);
    position: relative;
    overflow-y: auto;
    min-height: 100vh;
}
.modal_banner {
    position: fixed;
    padding: 1rem;
    inset: 0 1rem auto 1rem;
    margin: 0 -1rem 1rem;
    background: var(--white);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    border-bottom: 1px dashed var(--gray);
}
.modal .content{
    padding: 5rem 0 4rem;
}
.close_modal {
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
}
.close_modal:after {
    content: attr(data-before);
    display: inline-block;
    padding-left: .5rem;
}
.close_modal svg {
    height: 1.5rem;
    width: 1.5rem;
    fill: var(--gray);
}
.close_open_modal {
    display: inline-flex;
    position: absolute;
    inset: .25rem .25rem auto auto;
    width: 2rem;
    height: 2rem;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.close_open_modal span{
    font-size: 2rem;
    line-height: 2rem;
}
#listing_favorites {
    display: flex;
    align-items: center;
    justify-content: center;
}
.listing_favorites {
    position: absolute;
    top: .5rem;
    right: .5rem;
    padding: .25rem;
    background: rgba(0, 0, 0, .5);
    line-height: 1;
    border-radius: .25rem;
    display: flex;
}
#listing_favorites span {
    display: block;
    padding-right: .5rem;
}
#listing_favorites .listing_favorites {
    background: rgba(0, 0, 0, .25);
    position: relative;
    width: 2rem;
    height: 2rem;
    box-sizing: border-box;
    top: auto;
    right: auto;
    cursor: pointer;
}
.listing_favorites:before {
    content: '';
    display: inline-block;
    height: 1.5rem;
    width: 1.5rem;
    -webkit-mask: url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    mask: url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    -webkit-mask-size: contain;
    mask-size: contain;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: var(--white);
}
.listing_favorites.saved:before {
    content: '';
    display: inline-block;
    height: 1.5rem;
    width: 1.5rem;
    -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTYgMnYxNy41ODJsLTQtMy41MTItNCAzLjUxMnYtMTcuNTgyaDh6bTItMmgtMTJ2MjRsNi01LjI2OSA2IDUuMjY5di0yNHoiLz48L3N2Zz4=);
    mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTYgMnYxNy41ODJsLTQtMy41MTItNCAzLjUxMnYtMTcuNTgyaDh6bTItMmgtMTJ2MjRsNi01LjI2OSA2IDUuMjY5di0yNHoiLz48L3N2Zz4=);
    -webkit-mask-size: contain;
    mask-size: contain;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: var(--white);
}
#listing_favorites .listing_favorites em {
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}