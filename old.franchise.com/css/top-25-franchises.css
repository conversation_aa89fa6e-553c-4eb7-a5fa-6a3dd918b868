#top25_table img{
    box-shadow: 0 0 0 1px rgb(0 0 0 / 10%);
}
@media (max-width: 767px){
    #top25_table thead,.top25_rank,#top25_table input{
        display: none;
    }
    #top25_table{
        width: calc(100% - 2rem);
        margin: 0 auto .5rem;
    }
    #top25_table tr {
        display: grid;
        grid-auto-rows: min-content;
        grid-template-columns: repeat(7, 1fr);
        grid-column-gap: .5rem;
        grid-row-gap: .5rem;
        border: 1px solid #324972;
    }
    #top25_table tr{
        margin-bottom: 1rem;
    }
    .top25_logo { 
        grid-area: 1 / 1 / 4 / 4; 
        display: flex;
        align-items: center;
        justify-content: center;
        padding: .5rem 0 .5rem .5rem;
    }
    .top25_name { 
        grid-area: 1 / 4 / 2 / 8; 
        display: flex;
        align-items: center;
        line-height: 1.1;
        padding: .5rem .5rem 0 0;
    }
    .top25_name:before{
        height: 2rem;
        width: 2rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: .5rem;
        content: attr(data-before);
        background: #dedede;
        font-weight: 700;
        font-size: 14px;
        color: #333;
        margin-right: .5rem;
    }
    .top25_name a{
        font-size: 1rem;
        font-weight: 700;
        color: #000;
    }
    .top25_name a:hover{
        color: #cc1209;
    }
    :is(.top25_category,.top25_investment) {
        color: #31486e;
        font-size: 0.85rem;
        font-weight: 700;
    }
    .top25_category:before,.top25_investment:before{
        content: attr(data-before);
        display: block;
        line-height: 1;
        font-weight: 400;
        text-transform: uppercase;
        font-size: .75rem;
    }
    .top25_category { 
        grid-area: 2 / 4 / 3 / 8; 
        padding-right: .5rem;
        line-height: 1;
    }
    .top25_investment { 
        grid-area: 3 / 4 / 4 / 8; 
    }
    .top25_button { 
        grid-area: 4 / 1 / 6 / 8; 
        position: relative;
        cursor: pointer;
    }
    .top25_button:empty{
        display: none;
    }
    #top25_table tr:has(.top25_button:empty){
        padding-bottom: .5rem;
    }
    .top25_button:not(:empty) {
        background-color: #324972;
        height: 50px;
    }
    .top25_button button{
        background-color: #324972;
        color: #FFF;
        border: none;
        display: block;
        font-size: 0.875rem;
        cursor: pointer;
        width: 100%;
        margin: 0;
        padding: 15px 10px;
        text-transform: uppercase;
        height: 100%;
    }
    .top25_button button:before{
        content: '';
        margin-right: 10px;
        height: 0.7rem;
        width: 0.7rem;
        display: inline-block;
        -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
        mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
        -webkit-mask-size: cover;
        mask-position: 50% 50%;
        mask-repeat: no-repeat;
        mask-size: cover;
        background-color: #FFF;
    }
    .top25_button input:checked + button:before{
        -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
        mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
        -webkit-mask-size: cover;
        mask-position: 50% 50%;
        mask-repeat: no-repeat;
        mask-size: cover;
    }
    .top25_button:has(input:checked),.top25_button input:checked + button{
        background-color: #CC1209;
        cursor: pointer;
    }
    .top25_button input:checked + button span {
        font-size: 0px;
    }
    .top25_button input:checked + button span::after {
        font-size: 0.9rem;
        content: 'Added to cart';
    }
}
@media (min-width: 768px){
    #top25_table{
        width: 100%;
        border: 1px solid #383f4d;
        margin-bottom: 1rem;
    }
    #top25_table th{
        background: #383f4d;
        font-weight: 700;
        font-family: Montserrat, sans-serif;
        color: #fff;
        padding: .5rem;
    }
    #top25_table tbody{
        font-size: 14px;
        color: #333;
    }
    #top25_table td{
        padding: .5rem;
    }
    #top25_table tr:has(input:checked) td:not(.top25_rank){
        background: #f5f5f5;
    }
    #top25_table tr + tr td{
        border-top: 1px solid #404859;
    }
    .top25_button{
        position: relative;
        text-align: center;
        border-right: 1px solid #dedede;
    }
    .top25_button input{
        z-index: 1;
        height: 1.25rem;
        width: 1.25rem;
    }
    .top25_button button{
        opacity: 0;
        position: absolute;
        inset: 0;
        z-index: 2;
    }
    .top25_button button span{
        font-size: 1px;
    }
    .top25_rank{
        background: #dedede;
        border-right: 1px solid #dedede;
        text-align: center;
    }
    .top25_rank strong{
        background-color: #fff;
        height: 2rem;
        width: 2rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: .5rem;
    }
    .top25_name a{
        font-size: 1rem;
        font-weight: 700;
        color: #000;
    }
    .top25_name a:hover{
        color: #cc1209;
    }
    :is(.top25_category,.top25_investment) {
        color: #31486e;
        font-size: 0.8rem;
        font-weight: 700;
    }
}