/* MODAL POPUPS */
.overflow{
    overflow: hidden;   
}
.modal{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99999;
    background: rgba(0,0,0,.5);
    display: none;
    transition: all .25s ease-out; 
}
.close_button,.closer {
    color: #c0c0c0;
    font-size: 1.25rem;
    cursor: pointer;
}
.guts {
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    background: #FFF;
    display: flex;
    justify-content: flex-start;
    max-height: calc(100% - 30px);
    overflow-y: auto;
    flex-direction: column;
    width: 600px;
    max-width: calc(100% - 30px);
    margin: 15px auto;
    padding: 0;
    box-shadow: 0 0 0 10px rgb(255 255 255 / 50%);
    border-radius: 5px;
    z-index: 1;
}

@media only screen and (max-width: 991px){
    .guts {
     width:calc(100% - 1.25rem);
    }
}
.modal-header {
    padding: 15px;
    border-bottom: 1px solid #324972;
}
.modal-content:not(#survey-modal) {
    padding: 15px;
}
.modal .homepage-title {
    margin-top: 10px;
}
.modal .homepage-title h2{
    font-size: 2rem;
    margin-bottom: 10px;
}

.backdrop{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
}

#thanks .guts {
    max-width: 90%;
    overflow-y: initial;
    top:35px;
}
#thanks .modal-content:not(#survey-modal){
    padding-top: 45px;
}
#thanks .modal-content:not(#survey-modal):before{
    content: ""; 
    width: 100px; 
    position: absolute;
    display: block;
    background-size: 100px;
    left: calc(50% - 50px);
    height: 100px;
    top: -50px;
    background-image: url('data:image/png;base64,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');
}
#thanks.warning .modal-content:not(#survey-modal)::before {
    background-image: url('data:image/png;base64,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');
}

#thanks .modal-content:not(#survey-modal) h3{
    font-size: 1rem;
    margin-bottom: 8px;
}

.modal button {
    color: #fff;
    font-weight: 200;
    display: table;
    border-radius: 0;
    cursor: pointer;
    text-transform: uppercase;
    border: none;
    height: 40px;
    width: 90%;
    font-size: 18px;
    padding: 6px 15px;
    line-height: 1;
    margin: 10px auto 20px;
    background: #cc1209;
}

@media only screen and (min-width: 48rem) {
    .guts {
        top:15px;
    }

    #thanks .guts {
        max-width: 400px;
        top: 100px;
    }
}
