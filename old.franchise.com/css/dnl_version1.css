#profile button {
    background-color: #cc1209;
    color: #FFF;
    border: none;
    display: flex;
    font-size: 0.875rem;
    cursor: pointer;
    width: 100%;
    margin: 0;
    text-transform: uppercase;
    justify-content: center;
    align-items: center;
    line-height: 1;
}

#profile button::before{
    content: '';
    margin-right: 10px;
    height: 0.7rem;
    width: 0.7rem;
    line-height: 1;
    display: inline-block;
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
    -webkit-mask-size: cover;
    mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: #FFF;
}

#profile.checked button{
    /* background-color: #0957a0; */
    background-color: rgb(50, 73, 114); 
    cursor: pointer;
}

#profile.checked button:before{
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
    -webkit-mask-size: cover;
    mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
}

#profile.checked button span{
    font-size: 0px;
    line-height: 1;
}
#profile button span::after{
    font-size: 0.9rem;
    content: 'Get Free Info';
}
#profile.checked button span::after{
    content: 'Added to cart';
}
#profile_content {
    text-align: center;
}
#profile .close_button {
    color: #c0c0c0;
    font-size: 2rem;
    cursor: pointer;
    text-align: right;
    padding: 5px 15px;
    border-bottom: 1px solid #c0c0c0;
    line-height: 1;
    background: #f0f0f0;
}

#profile_content h1 {
    border-bottom: 1px solid #324972;
    font-size: 20px;
    line-height: 1.1;
    padding: 5px 0 15px;
    margin-bottom: 15px;
}
.grayArea{
    background: #dedede;
    padding: 10px 10px 25px;
    max-width: 300px;
    width:100%;
    margin:15px auto
}
#profile_content h2 {
    font-family: 'Montserrat',sans-serif;
text-align: center;
font-weight: 600;
font-size: 1rem;
padding: 15px 0 5px;
}
#profile_content span{
    font-family: 'Roboto',sans-serif;
    display: block;
    font-weight: 400;

}
#profile_content p {
    padding: 0 20px;
    font-size: 15px;
    text-align: left;
}
@media only screen and (max-width : 600px) {
    #profile .guts {
        top: 0;
    }
}
.clickable{
    cursor: pointer;
    transition: all .2s ease;
}
#results .result-item:hover .item.clickable{
    background-color: #eaeef1;
}
#results .result-item p:after {
    content: 'More Info';
    background-color: #9cb2bc;
    color: #FFF;
    padding: 0.313rem 0.625rem;
    display: table;
    position: absolute;
    border-radius: 1rem;
    text-transform: uppercase;
    cursor: pointer;
    font-size: .75rem;
    opacity: .5;
    transition: all .5s ease-out;
    bottom: 0;
    left: 50%;
    right: 50%;
    margin-left: -42px;
    white-space: nowrap;
}
#results .result-item p{
    position: relative;
}
#results .result-item p:before {
    content: '';
    background: linear-gradient(to bottom,rgba(255,255,255,0) 0%,rgba(255,255,255,1) 100%);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 6.25rem;
}
#results .result-item:hover p:before {
    content: '';
    background: -moz-linear-gradient(top,rgba(234,238,241,0) 0%,rgba(234,238,241,1) 100%);
    background: -webkit-linear-gradient(top,rgba(234,238,241,0) 0%,rgba(234,238,241,1) 100%);
    background: linear-gradient(to bottom,rgba(234,238,241,0) 0%,rgba(234,238,241,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00eaeef1',endColorstr='#eaeef1',GradientType=0);
}
#results .result-item:hover p:after {
    opacity: 1;
}