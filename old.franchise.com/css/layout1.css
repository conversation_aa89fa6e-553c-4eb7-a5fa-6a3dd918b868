@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, html body {
    font-size: 16px;
    color: #fff;
    background-color: #000;
    font-family: 'Source Sans Pro', sans-serif;
    width: 100%;
    font-weight: 400;
    line-height: 1.5;
} 

@media only screen and (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

.margin-top-20 {
    margin-top: 20px;
}

.padding-vertical-40 {
    padding-top: 40px;
    padding-bottom: 40px;
}

.margin-xs-top-40 {
    margin-top: 40px;
}

.title-red {
    color: #ed1c24;
}

.clearfix {
    font-size: 0px;
}

.clear-float {
    clear: both;
}

.text-right{
    text-align: right;
}

.form-section {
    background: linear-gradient( rgba(100, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('https://d9hhrg4mnvzow.cloudfront.net/franchise.schoolofrock.com/franchise-growth/26d214d2-583x400-full.jpeg');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    align-items: center;
}


#layout1 .form-group label{
    margin-bottom: 8px;
}

.form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    margin-bottom: 10px;
}

.title-text {
    font-weight: bolder;
    font-size: 2.375rem;
}

.sub-title-text {
    font-weight: bolder;
    font-size: 1.5rem;
}

.sub-title {
    font-size: 1.5rem;
}

.responsive-img-container {
    position: relative;
    /* width: 100%; */
    min-height: 277px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.img-container {
    text-align: center;
}

.submit-button {
    border: 5px solid rgba(195,156,8,1);
    background-color: rgba(195,156,8,1);
    width: 100%;
    padding: 20px 0px;
    color: #fff;
    font-weight: bolder;
    font-size: 34px;
    border-radius: 5px;
}

.lp-multi-body {
    width: 100%;
    padding: 10px;
    font-family: 'Helvetica Neue', 'Arial';
    line-height: 1.8em;
    font-weight: 300;
    font-size: 0.75rem;
}

.lp-multi-body a{
    text-decoration: none;
}

@media only screen and (min-width: 580px) {
    .lp-multi-body {
        max-width: 900px;
        margin: 0 auto;
    }
}

.videoWrapper {
    margin-left: auto;
    margin-right: auto;
    max-width: 800px;
}

.bg-image-2 {
    background: linear-gradient( rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), url('https://d9hhrg4mnvzow.cloudfront.net/franchise.schoolofrock.com/franchise-growth/7b413bae-girl-bass-boy-guitar.jpg');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
}

li{
    margin: 0px; 
    padding: 0px; 
    border: 0px; 
    outline: 0px; 
    font-weight: inherit; 
    font-style: inherit; 
    font-family: inherit; 
    vertical-align: baseline; 
    color: rgb(255, 255, 255);
}

li span{
    font-size: 1.125rem;
}

.bg-image-3 {
    background: linear-gradient( rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), url('http://d9hhrg4mnvzow.cloudfront.net/franchise.schoolofrock.com/franchise-growth/51993caa-girl-vocals-guitar-dark.jpg');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
}

.get-franchise-button {
    color: #FFF;
    text-decoration: none;
    margin: 20px;
    padding: 10px;
    background: rgba(195,156,8,1);
}

.get-franchise-button:hover{
    color: #FFF;
}