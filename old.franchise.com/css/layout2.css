
@import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, html body {
    font-size: 16px;
    color: #000;
    font-family: 'Work Sans', sans-serif;
    width: 100%;
    font-weight: 400;
    line-height: 1.5;
} 

.container{
    max-width: 900px;
}

@media only screen and (max-width: 768px) {
    .container{
        padding: 0;
    }
}

.l2-1-header{
    margin: 0 auto;
    width: 100%;
}

.l2-1-header .l2-logo-header{
    max-width: 100%;
    height: auto;
    width: 100%;
}

.lp-multi-body h2 {
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
    margin-bottom: 25px;
    margin-top: 50px;
    font-family: 'Work Sans', 'Helvetica Neue';
    font-weight: 400;
}

p, ul li{
    font-size: 14px;
    color: #333;
    margin: 0 0 10px;
    font-family: 'Helvetica Neue', 'Arial';
}

.lp-single-form-div {
    width: 90%;
    max-width: 600px;
    margin: 0 auto;
    font-family: 'Helvetica Neue', 'Arial';
    line-height: 1.5em;
    font-weight: 300;
}

.lp-single-names-div, .lp-single-phone-zip-div {
    position: relative;
}

.lp-single-float-left,
.lp-float-left {
    float: left;
    width: 100%;
}

@media only screen and (min-width: 640px){
    .lp-single-float-left {
        width: 48%;
    } 

    .lp-single-float-left:nth-child(even) {
        margin-right: 0;
        margin-left: 4%;
    }
}

.lp-single-submit-button-div .lp-single-form-submit{
    width: 100%;
    max-width: 300px;
    height: 80px;
    font-size: 20px;
    line-height: 80px;
    font-weight: 500px;
    letter-spacing: 3px;
    background-color: #41b6e6;
    -webkit-appearance: none;
    border-radius: 0;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
    cursor: pointer;
    margin: 30px auto;
}

.lp-single-form-div input {
    height: 50px;
    padding-left: 10px;
    width: 100%;
    margin-left: 0;
    margin-bottom: 20px;
}

.lp-multi-body {
    width: 100%;
    padding: 10px;
    font-family: 'Helvetica Neue', 'Arial';
    line-height: 1.8em;
    font-weight: 300;
    font-size: 0.75rem;
}

@media only screen and (min-width: 580px){
    .lp-multi-body {
        max-width: 900px;
        margin: 0 auto;
    }
}

@media only screen and (max-width: 767px) {
    .lp-single-form-div input{
        margin-bottom: 0;
    }
}