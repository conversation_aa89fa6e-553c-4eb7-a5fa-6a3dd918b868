
html, body{
    font-size: 16px;
    color: #333;
    font-weight: 400;
    font-family: sans-serif;
    line-height: 1.42857143;
}

/* BASE */
h1, h2, h3, h4, h5, #breadcrumbs {
    font-family: sans-serif;
} 
.container {
    max-width: 100%;
}
/* @media (min-width: 992px){
    .container {
        max-width: 960px;
    }
} */
@media (min-width: 1200px){
    .container {
        max-width: 1190px;
    }
}

/* BREADCRUMBS */
#breadcrumbs{
    font-family: 'Montserrat', sans-serif;
    flex-direction: row;
}
#breadcrumbs li{
    align-items: center;
}
#breadcrumbs li+li::before {
    margin: 0 .7rem;
}

/* HEADER */
header#header {
    height: 70px;
    padding:0;
}
header#header .row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 70px;
    align-items: center;
}
.headerItem {
    line-height: 70px;
    padding-top: 0;
    padding-bottom: 0;
    padding: 0 1rem;
}

#basketPath.headerItem{
    padding: 0 22px;
}
.freeUpdates {
    background: #cc1209;
    color: #fff !important;
    text-decoration: none !important;
    padding: 0 1.375rem;
    text-transform: uppercase;
    font-weight: 700;
    line-height: 70px;
    border: none;
}

#copy #base{
    padding: 0 0 2rem;
}

#intro .city-title{
    padding: 20px 0 0;
    font-size: 2.25rem;
    font-weight: 700;
    font-family: Montserrat,sans-serif;
}

#heading .page-title {
    font-weight: 700;
    font-family: Montserrat,sans-serif;
}

@media (min-width: 1025px){
    #results .result-item p{
        height: 200px;
    }
}

#results .result-item p {
    height: 175px;
}

/* MAIN MENU */
#menuToggle {
    border-right: 1px solid #324972;
}
nav#main-menu ul li.has-sub ul a:hover {
    background: #ddd;
}


.overflow,.overflow .container{ 
    padding-right: .938rem;
}

/* FILTER */
div#filter, section#toc-slider {
    padding: 20px;
}
div#filter #filters, section#toc-slider #toc-filters {
    padding: 25px 0;
}
#filter_toggle span,#toc-toggle span {
    font-family: 'Montserrat', sans-serif;
}
div#filter #filters .col-md-4 {
    padding-top: 0!important;
    padding-left: 12.5px;
    padding-right: 12.5px;
}

section#toc-slider #toc-filters{
    padding:25px;
}
#toc-filters li a:hover{
    text-decoration: underline;
}

div#filter #filters .col-md-4:nth-child(1) {
    padding-left: 25px;
}

div#filter #filters .col-md-4:last-child{
    padding-right: 25px;
}

div#filter button {
    white-space: nowrap;
    font-size: 95%;
}

#home #bannerWrap{
    background-image: url(https://franchise-ventures-general.s3.amazonaws.com/cdn_fcom/images/banner-desktop.jpg);
}

#results .result-item h3 a:hover {
    color: #cc1209;
}
#subcat a:hover{
    text-decoration: underline;
}

.directory-container #sort-results {
    text-align: right;
    margin: 0 10px 15px;
}

@media screen and (min-width:851px) and (max-width:1200px) {
    #results .result-item {
        padding: 10px;
        flex: 0 1 33% !important;
        margin: 0;
    }
}

@media only screen and (min-width: 768px) and (max-width: 850px){
    #results .result-item {
        padding: 0 .313rem .625rem;
        flex: 0 1 50% !important;
    }
}

@media only screen and (min-width: 992px) {
    #home #franCat #franCatDiv .franTabs .franTabItem a{
        font-family: sans-serif;
        font-size: 1.1rem;
    }

    .home-content-text{
        padding: 0 15px 30px;
    }

    #results{
        padding: 0 15px;
    }

    #home-news .container{
        padding: 0;
    }

    footer#lower .row .col-lg-3:first-child{
        padding-left: 12px;
    }
    
    footer#lower .row .col-lg-3:nth-child(4) {
        padding-right: 12px;
    }

}

@media only screen and (min-width:992px) {
    #home-filter #home-filters .home-filter-item:first-child{
        padding-left: 25px;
    }
    
    #home-filter #home-filters .home-filter-item:last-child{
        padding-right: 25px;
    }
}

/* #filter_toggle{
    margin: 0 -15px;
} */

@media screen and (min-width:768px) and (max-width:769px) {
    #image-slider .splide__track,
    .splide--draggable>.splide__track>.splide__list>.splide__slide{
        width: 744px;
    }

    .splide__slide img, .splide__slide iframe{
        width: auto;
    }
}

@media screen and (min-width:769px) and (max-width:1024px){
    #image-slider .splide__track{
        width: 402px;
    }

    .splide--draggable>.splide__track>.splide__list>.splide__slide{
        width: 402px;
    }

    .splide__slide img{
        height: auto;
    }

    .profile-locations-block.container{
        padding: 0 !important;
        padding-bottom: 2rem !important;
    }

    .profile-video{
        height: 100%;
    }

    .splide__slide iframe{
        width: 100%;
        height: 100%;
    }

}
strong.hover_strong:hover{
    text-decoration: underline;
}
#si-results .si-results-row .si-results-item{
    flex: 0 1 50%;
}

#si-results .si-results-row .si-results-item > div{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

#si-results .container{
    padding: 0;
}

@media only screen and (min-width:992px) {
    #si-results .si-results-row .si-results-item{
        flex: 0 1 33.33%;
    }
}

/* franchise usa */
.franchise-usa-main div#filter {
    background: url('https://franchise-ventures-general.s3.amazonaws.com/cdn_fcom/images/faf-map.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 60%;
}

.franchise-usa-main div#filter #filters {
    margin: 50px 15% 50px;
}

.franchise-usa-main div#filter #filters {
    display: block;
}

.franchise-usa-main div#filter #filters {
    padding: 25px 0 5px;
}

@media (min-width: 1024px){
    .franchise-usa-main div#filter #filters {
        margin: 50px 30% 100px;
    }
}

.franchise-usa-main .white-section li,
.franchise-by-investment li {
    padding-right: 1%;
    width: 49.5%;
}

.find-a-franchise div#filter{
    background: url('https://dst8t9gntqrhb.cloudfront.net/image/fs-homepage-background-6-min.jpg');
    background-size: cover;
    background-repeat: no-repeat;
}

@media (min-width: 992px){
    .franchise-usa-main .white-section li,
    .franchise-by-investment li {
        width: 32.5%
    }
}

@media (min-width: 1200px){
    .franchise-usa-main .white-section li,
    .franchise-by-investment li {
        width: 24.5%;
    }
}

/* Contact form */

@media (min-width: 1024px){
    .contact-form{
        max-width: 80%;
    }
}

.contact-form form .ct-form-row{
    display: flex;
}

.contact-form form .ct-form-row .medium-6{
    flex: 0 1 50%;
}

@media (max-width: 991px){
    #filters.row,#home-filter #home-filters{
        flex-flow: row wrap;
        justify-content: flex-start;
    }
    #filters.row .col-md-3,#home-filter #home-filters .home-filter-item{
        flex: 0 1 33%;
        padding: 0 15px;
    }
    #filters.row .col-md-3:last-child,#home-filter #home-filters .home-filter-item:last-child{
        flex: 0 1 100%;
        padding: 15px 15px 0;
        text-align: center;
    }
    #filters.row .col-md-3:last-child button,#home-filter #home-filters .home-filter-item:last-child button{
        width: 20rem;
        margin: 0 auto;
    }
}