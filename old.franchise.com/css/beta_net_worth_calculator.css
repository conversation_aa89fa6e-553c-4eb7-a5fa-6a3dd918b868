


/* net_worth_calculator */
#calculator_form{
    display: flex;
    justify-content: center;
}
.calculator_main{
    display: flex;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    gap:15px;
    padding: 20px 30px 30px 30px;
    background-color: white;
    border:1px solid var(--gray);
}
.asset_liab{
    display: flex;
    flex-direction: column;
    gap:20px
}

.asset_liab .label_inline{
    position: relative;
}

#calculator_asset,#calculator_liabilities{
    display: flex;
    width: 100%;
    flex-direction: column;
    gap:8px;
}
.calc_heading{
    padding-bottom: 0 !important;
    font-weight: 600;
    font-size: 18px;
}
.calc_heading span{
    margin-left: 44px !important;
}
.calculator_main .box label{
    font-weight: 500;
}     
.calc_result{
    position: relative;
    top: 22px;
} 
.calc_result p span{
    color: var(--button_blue);
    font-size: 1rem;
    font-weight: 500;
    margin-left: 20px;
}
.calc_result button{
    background-color: var(--button_blue);
    color: white;
    font-size:13.5px;
    width:100%;
    padding: 0 1rem;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 20px;
}
    
.net_worth_totals{
    position: sticky;
    padding: 1rem 2rem;
    bottom: 0;
    margin: 0 -1rem 0;
    background: var(--white);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    border-top: 1px dashed var(--gray);
}

.net_worth_totals p{
    padding-bottom: 0 !important;
    color: var(--logo_blue);
    font-weight: 600;
}

.net_worth_totals p span{
    color: var(--button_blue);
}

.net_worth_totals button{
    background-color: var(--button_blue);
    color: white;
    font-size: 13.5px;
    padding: 0 1rem;
    font-weight: 600;
}

.calc_intro{
    text-align: justify;
    text-align-last: left;
}

.modal{
    padding-bottom: 0;
}

@media only screen and (min-width: 64rem){
    .calculator_main {
        width: 80%;
    }

    .asset_liab {
        flex-direction: row;
    }
}

@media only screen and (max-width:768px) {
    #calculator_modal.modal.show{
        z-index: 6;
    }

    .net_worth_totals{
        padding: 1rem;
    }
}

@media only screen and (max-width:767px) {
    .net_worth_totals{
        display: block;
        padding: 0.5rem;
    }

    .net_worth_totals p{
        padding-bottom: 0.5rem !important;
        font-size: 0.875rem;
    }

    .net_worth_totals button{
        width: 100%;
    }
}