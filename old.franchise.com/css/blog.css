.sidebar {
    padding: 15px 15px 5px;
    background: #efefef;
    margin-bottom: 1rem
}

.sidebar-widget {
    margin-top: 15px
}

.sidebar-widget>div {
    background: #FFF
}

.sidebar-widget h3 {
    padding: 10px 0 0;
    font-size: 16px;
    text-transform: uppercase;
    margin: 0 10px
}

.sidebar-widget ul {
    list-style-type: none;
    padding-left: 0;
    margin: 0 0 15px;
    padding: 10px
}

.sidebar-widget li {
    margin: 10px 0 0
}

button.search-submit {
    background: #cc1209;
    color: #FFF;
    letter-spacing: 1px;
    padding: 12px 9px 11px;
    border-radius: 0;
    margin: 0 auto 5px;
    font-size: 13px;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: 200;
    border: none
}

input.search-input {
    padding: 9px;
    border: 1px solid #b2b2b2
}

.pagination {
    text-align: center;
    background: #efefef;
    padding: 1rem;
    border: none!important;
    margin: 1.5rem 0 1rem;
    border-radius: 0;
    justify-content: center
}

.pagination>* {
    display: inline-block;
    min-width: 1.5rem
}

article.post + article.post {
    margin-top: 1rem;
    border-top: 5px solid #dedede;
    padding-top: 1rem
}

a.page-numbers {
    padding: 0 5px
}

.post a {
    overflow-wrap: break-word
}
#heading .page-title {
    margin-bottom: 2rem;
    border-bottom: 2px dashed #DEDEDE;
    padding-bottom: 1.5rem;
}
.post h2 {
    font-size: 1.5rem !important;
}
main h2 {
    color: #000;
    margin: 0 0 0.5rem;
    font-size: 24px;
    line-height: 1;
}
.details {
    color: #000;
    margin: 0 0 1rem;
    font-size: 18px
}

.details span + span:before {
    content: " | ";
    display: inline-block;
    padding: 0 10px
}

article .post {
    border-top: 1px dotted #dedede;
    padding: 15px 0
}
.categories {
    text-align: center;
    background: #efefef;
    padding: 1rem;
    border: none!important;
    margin-bottom: 1rem
}

.categories strong {
    text-transform: uppercase;
    font-family: 'Montserrat',sans-serif;
    font-weight: 700
}

main article ul,main article ol {
    list-style-type: square;
    padding-left: 20px;
    margin-bottom: 1rem
}

main article ol {
    list-style-type: decimal
}

main article ul ul {
    list-style-type: circle
}

main article ul ul ul {
    list-style-type: disc
}

main article strong,main article b {
    font-weight: 700
}

main article em,main article i {
    font-style: italic
}

.alignright {
    float: right;
    padding: 1rem 0 1rem 1rem
}

.size-full {
    width: 100%;
    height: auto
}

.aligncenter {
    margin: 1rem auto;
    display: block
}

.wp-caption {
    border: 3px solid #efefef
}

.wp-caption img {
    display: block;
    width: 100%;
    height: auto;
    margin: 0
}

.wp-caption p {
    padding: 0 1rem;
    margin: 1rem 0
}

img.avatar {
    float: left;
    margin: 0 1rem 10px 0
}

.auth-bio {
    background-color: #dedede;
    padding: 1rem
}

.details span {
    white-space: nowrap
}

.wp-caption.alignright {
    padding: .7rem;
    margin: 0 0 1rem 1rem
}

.wp-caption p {
    padding: 0;
    margin: 1rem 0 0
}
@media only screen and (min-width: 48rem) {
    #blog_left {
        padding-right: 2rem;
    }
}
.single_post {
    margin-bottom: 2rem;
    border-bottom: 2px dashed #DEDEDE;
    padding-bottom: 1.5rem;
}
@media only screen and (max-width: 767px) {
    .details {
        font-size:15px
    }

    input.search-input {
        width: calc(100% - 20px);
        border-bottom: none
    }

    button.search-submit {
        width: 100%
    }

    img {
        max-width: 100%
    }

    .wp-caption.alignright {
        float: none;
        width: 100%!important;
        display: block;
        padding: .5rem;
        margin: 0 0 2rem
    }
    aside.sidebar{
        display: none;
    }
}
img:is(.alignleft,.alignright,.aligncenter){
    padding:.5rem;
    width:100%;
    max-width:25rem;
    height:auto;
    display:block;
}
.alignleft {
    float: left;
}
.alignright {
    float: right;
}
.aligncenter {
    margin: 0 auto;
}