

.hide{
    display: none !important;
}

.bold{
    font-weight: bold;
}
#open_modal h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
}
#open_modal ul {
    list-style-type: disc;
    margin-bottom: 1rem;
    padding: 0 0 0 2rem;
}
#open_modal li {
    line-height: 1.4;
}


/* Form Elements */
input:is([type="number"],[type="email"],[type="password"],[type="text"]){
    border-radius: .25rem;
    border: 2px solid var(--gray);
    font-size: .85rem;
    height: 2rem;
    width: 100%;
    padding-left: .5rem;
    box-sizing: border-box;
}
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA3LjMzbDIuODI5LTIuODMgOS4xNzUgOS4zMzkgOS4xNjctOS4zMzkgMi44MjkgMi44My0xMS45OTYgMTIuMTd6Ii8+PC9zdmc+) 98% 50% no-repeat #fff;
    background-size: .5rem;
    border-radius: .25rem;
    border: 2px solid var(--gray);
    font-size: .85rem;
    height: 2rem;
    width: 100%;
    padding-left: .85rem;
    transition: all .1s ease;
    text-transform: none;
    word-wrap: normal;
}
:is(.radio_button,.checkbox_button){
    display: inline-flex;
    position: relative;
    margin-right: 1rem;
    height: 2rem;
}
:is(.radio_button,.checkbox_button) input {
    height: 2rem;
    width: 1.5rem;
    opacity: 0;
    position: absolute;
    top: .5rem;
    left: 0;
    z-index: 10;
    margin: 0;
    cursor: pointer;
}
:is(.radio_button,.checkbox_button) label {
    cursor: pointer;
}
:is(.radio_button,.checkbox_button) span {
    padding-left: 1.25rem;
    display: block;
    line-height: 2rem;
    position: relative;
    font-size: .85rem;
}
.radio_button input + span::before {
    display: flex;
    background-color: var(--white);
    position: absolute;
    left: 0;
    top: .5rem;
    user-select: none;
    z-index: 1;
    height: 1rem;
    width: 1rem;
    border-radius: 2rem;
    content: '';
    cursor: pointer;
    align-content: center;
    justify-content: center;
    border: 2px solid var(--gray);
    box-sizing: border-box;
}
.radio_button input:checked + span::after {
    content: '';
    background-color: var(--gray);
    height: .5rem;
    width: .5rem;
    border-radius: .5rem;
    position: absolute;
    left: .25rem;
    top: .75rem;
    z-index: 2;
}
.checkbox_button input + span::before {
    display: flex;
    background-color: var(--white);
    position: absolute;
    left: 0;
    top: .5rem;
    user-select: none;
    z-index: 1;
    height: 1rem;
    width: 1rem;
    border-radius: .25rem;
    content: '';
    cursor: pointer;
    align-content: center;
    justify-content: center;
    border: 2px solid var(--gray);
    box-sizing: border-box;
}
.checkbox_button input:checked + span::before {
    background-color: var(--logo_blue);
    border-color: var(--logo_blue);
}
.checkbox_button input:checked + span::after {
    display: block;
    content: '';
    z-index: 2;
    position: absolute;
    left: .4rem;
    top: .7rem;
    width: 0.25rem;
    height: .5rem;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    box-sizing: border-box;
}
.label_inline{
    position: relative;
    padding-top: .35rem;
    margin-top: 1rem;
}
.label_inline:not(:first-of-type), .help_feedback_section .label_inline{
    margin-top: 1.5rem;
}
.label_inline label{
    background-color: #FFF;
    padding: 0 .25rem;
    inset: -0.35rem auto auto .75rem;
    position: absolute;
    display: inline-block;
    line-height: 1;
    font-size: .85rem;
}
.label_inline :is(select,input){
    height: 2.5rem;
}

#open_modal button[type="button"] {
    width: 100%;
}
.flex_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.flex_item button {
    max-width: calc(50% - .5rem);
}

/* TOOLTIP */
.tool_tip {
    background: var(--logo_blue);
    border: 1px solid var(--border_blue);
    color: #fff;
    border-radius: 5px;
    padding: 0.5rem;
    z-index: 9;
    font-size: 0.8rem;
    position: absolute;
    bottom: calc(100% + 1rem);
    left: -.75rem;
}
.tool_tip:after {
	top: 100%;
	left: 50%;
	border: solid transparent;
	content: "";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(136, 183, 213, 0);
	border-top-color: var(--logo_blue);
	border-width: 10px;
	margin-left: -10px;
}
.tool_tip ul{
    list-style: disc;
    padding-left: 1rem;
}
.tool_tip strong{
    font-weight: bold;
}

/* ERRORS */
.error,.error_msg{
    color: var(--red);
    font-size: .85rem;
    margin: 0.5rem 0.25rem 0;
    line-height: 1.5;
    font-weight: 400;
}
.error-outline{
    border-color: var(--red) !important;
}
.error-outline + .error{
    display: block;
}