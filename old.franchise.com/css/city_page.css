#city_page_table{
    caption-side: bottom;
    border-collapse: collapse;
}
#city_page_table caption {
    text-align: center;
    padding: 10px;
}
table:has(.results_tr_nope) caption {
    display: none;
}
#city_page_table div{
    height:60px;
    width:120px;
    display: flex;
    justify-content: center;
    align-items: center;
}
#city_page_table div img{
    max-width: 120px;
    max-height: 60px;
    display: inline-flex;
    height: auto;
    width: auto;
}
:is(.top100_category,.top100_investment) {
    color: #31486e;
}
#city_page_details h2{
    margin-top: 1rem;
}
#city_table_details h2 {
    font-size: 1.5rem !important;
    margin: 10px;
    transition: opacity 0.1s ease 0s;
    color: #000;
    padding: 20px 0 0;
}
@media (max-width: 767px){
    #city_table_details h2 {
        font-size: 1.25rem !important;
        padding: 10px 0 0;
    }
    #city_container {
        display: flex;
        flex-direction: column-reverse;
    }
    #city_page_table thead,.top100_rank,#city_page_table input{
        display: none;
    }
    #city_page_table{
        width: 100%;
        margin: 0 auto .5rem;
    }
    #city_page_table tr {
        display: grid;
        grid-auto-rows: min-content;
        grid-template-columns: repeat(7, 1fr);
        grid-column-gap: .5rem;
        grid-row-gap: .5rem;
        box-shadow: 0 1px 1px rgb(0 0 0 / 5%);
        border: 1px solid #d9dcde;
    }
    #city_page_table tr{
        margin-bottom: .5rem;
    }
    .top100_logo { 
        grid-area: 1 / 1 / 4 / 4; 
        display: flex;
        align-items: center;
        justify-content: center;
        padding: .5rem 0 .5rem .5rem;
    }
    .top100_name { 
        grid-area: 1 / 4 / 2 / 8; 
        display: flex;
        align-items: center;
        line-height: 1.1;
        padding: .5rem .5rem 0 0;
    }
    .top100_name:not(#resultsSection .top100_name):before{
        height: 2rem;
        min-width: 2rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: .5rem;
        content: attr(data-before);
        background-color: #404859;
        font-weight: 700;
        font-size: 14px;
        color: #fff;
        margin-right: .5rem;
    }
    .top100_name a{
        font-size: 1rem;
        font-weight: 700;
        font-family: sans-serif;
        color: #000;
    }
    .top100_name a:hover{
        color: #cc1209;
    }
    :is(.top100_category,.top100_investment) {
        font-size: 0.85rem;
        font-weight: 700;
    }
    .top100_category:before,.top100_investment:before{
        content: attr(data-before);
        display: block;
        line-height: 1;
        font-weight: 400;
        text-transform: uppercase;
        font-size: .75rem;
    }
    .top100_category { 
        grid-area: 2 / 4 / 3 / 8; 
        padding-right: .5rem;
        line-height: 1;
    }
    .top100_investment { 
        grid-area: 3 / 4 / 4 / 8; 
    }
    .top100_button { 
        grid-area: 4 / 1 / 6 / 8; 
        position: relative;
        cursor: pointer;
    }
    .top100_button:empty{
        display: none;
    }
    #city_page_table tr:has(.top100_button:empty){
        padding-bottom: .5rem;
    }
    .top100_button:not(:empty) {
        background-color: #FFF;
        height: 50px;
    }
    .top100_button button{
        background-color: #324972;
        color: #FFF;
        border: none;
        display: block;
        font-size: 0.875rem;
        cursor: pointer;
        width: 100%;
        margin: 0;
        padding: 15px 10px;
        text-transform: uppercase;
    }
    .top100_button button:before{
        content: '';
        margin-right: 10px;
        height: 0.7rem;
        width: 0.7rem;
        display: inline-block;
        -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=);
        mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=);
        -webkit-mask-size: cover;
        mask-position: 50% 50%;
        mask-repeat: no-repeat;
        mask-size: cover;
        background-color: #FFF;
    }
    .top100_button input:checked + button:before{
        -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+);
        mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+);
        -webkit-mask-size: cover;
        mask-position: 50% 50%;
        mask-repeat: no-repeat;
        mask-size: cover;
    }
    .top100_button:has(input:checked) button{
        background-color: #CC1209;
    }
    .top100_button input:checked + button span {
        font-size: 0px;
    }
    .top100_button input:checked + button span::after {
        content: 'Added to Requests';
        font-size: 0.875rem;
    }
}
@media (min-width: 768px){
    #city_page_table{
        width: 100%;
        border: 1px solid #dedede;
        margin: 1rem 1rem 1rem 0;
    }
    #city_page_table tbody#results{
        padding: 0;
        display: revert;
    }
    #city_page_table th{
        background: #dedede;
        font-weight: 600;
        font-family: sans-serif;
        color: #333;
        padding: .5rem;
    }
    #city_page_table tbody{
        font-size: 14px;
        color: #333;
    }
    #city_page_table td{
        padding: .5rem;
    }
    #city_page_table tr:has(input:checked) td{
        background: #efefef;
    }
    #city_page_table tr td{
        border-top: 1px solid #dedede;
    }
    .top100_button{
        position: relative;
        text-align: center;
        border-right: 1px solid #dedede;
    }
    .top100_button input{
        z-index: 1;
        height: 1.25rem;
        width: 1.25rem;
    }
    .top100_button button{
        opacity: 0;
        position: absolute;
        inset: 0;
        z-index: 2;
    }
    .top100_button button span{
        font-size: 1px;
    }
    .top100_rank{
        text-align: center;
    }
    .top100_rank strong{
        background-color: #404859;
        color: #fff;
        height: 2rem;
        width: 2rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: .5rem;
    }
    .top100_name a{
        font-size: 1rem;
        font-weight: 400;
        font-family: sans-serif;
        color: #000;
        word-break: break-word;
        text-decoration: underline;
    }
    .top100_name a:hover{
        color: #CC1209;
    }
    :is(.top100_category,.top100_investment) {
        font-size: 0.9rem;
    }
}
@media (min-width: 1024px){
    #city_page_table:not(#resultsSection #city_page_table){
        margin: 1rem auto;
    }
}
#main .container.border_bottom {
    margin-bottom: 1.5rem;
    border-bottom: 2px dashed #dedede;
    padding-bottom: 0.5rem;
}
#main .container.border_top {
    margin-top: 1.5rem;
    border-top: 2px dashed #dedede;
    padding-top: 1.5rem;
}
.faq-h2{
    font-size: 1.25rem;
    line-height: 1.2;
    margin: 2rem 0 .5rem;
}
.faq-p {
    font-family: sans-serif;
    font-size: 1rem;
}
.city-info-item{
    margin-bottom: .5rem;
}
@media (min-width: 768px){
    .faq-h2{
        font-size: 1.5rem;
    }
    .city-info-item{
        margin-bottom: 2rem;
    }
}