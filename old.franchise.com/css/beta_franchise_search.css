/* Franchise */
.listings {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
}
.listings .item {
    position: relative;
}
.listing_banner{
    aspect-ratio: 2 / 1;
    display: block;
    overflow: hidden;
    background-image: var(--bg-image);
    /* background-size: cover;
    background-position: top center; */
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    border-bottom: 1px solid var(--border_blue);
}
.listing_name{
    display: block;
    padding: .5rem;
    color: var(--black);
    font-weight: 600;
    line-height: 1;
}
.listing_options {
    padding: 0 0 .5rem .5rem;
}
.listing_options li {
    padding-right: .5rem;
    color: var(--logo_blue);
    font-size: .75rem;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
}
.listing_options li.break{
    width: 100%;
}
.listing_options li.break i:not(:first-child) {
    margin-left: .25rem;
}
.listing_options li svg{
    height: 1.5rem;
    width: 1.5rem;
    fill: var(--gray);
    margin-right: .25rem;
}
.listing_details{
    padding: 0 .5rem 1rem;
    font-size: .95rem;
    line-height: 1.2;
}
.read_more {
    padding: 0 .5rem .5rem;
    text-align: right;
}
.listing_favorites {
    position: absolute;
    top: .5rem;
    right: .5rem;
    padding: .25rem;
    background: rgba(0,0,0,.5);
    line-height: 1;
    border-radius: .25rem;
    display: flex;
}
.listing_favorites input{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}
.listing_favorites:before{
    content: '';
    display: inline-block;
    height: 1.5rem;
    width: 1.5rem;
    -webkit-mask: url('data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
    mask: url('data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'); 
    -webkit-mask-size: contain;
    mask-size: contain;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    background-color: var(--white);
}
#franchise_columns .listings .item{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
#franchise_filter{
    display: none;
}
#franchise_filtering h3{
    text-align: center;
    margin: -1rem -1rem 1rem;
    padding: 1rem;
    color: var(--white);
    background: var(--logo_blue);
    border-bottom: 1px solid var(--border_blue);
}
#franchise_filter label{
    font-weight: 500;
    font-size: .85rem;
}
#franchise_filter .row:not(.label_inline) label{
    padding-left: 1rem;
    margin-top: 1rem;
    display: block;
}
#franchise_filter .row:not(.label_inline) label input{
    display: inline-flex;
    width: fit-content;
    background: none;
}
#franchise_filter .row:not(:last-child){
    margin-bottom: .5rem;
}
.h1_counter {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin: 0;
}
.h1_counter span{
    font-size: 1rem;
    padding-top: .5rem;
    color: var(--logo_blue);
}
.h1_counter em{
    font-size: 1.25rem;
    color: var(--header);
}
#franchise_title {
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}
#sort_by{
    min-width: 15rem;
}
.sort_by{
    display: none;
}
:is(#franchise_filtering,#franchise_filter2) button[type="submit"]:not([data-count="1"]):after{
    content: 's';
}
.franchise_count{
    padding: 0 .25rem;
}
:is(#franchise_filtering,#franchise_filter2) button[type="submit"][data-count="0"]{
    background-color: var(--gray);
    border-color: var(--gray);
    cursor: not-allowed;
}
#empty{
    padding: 1rem;
}
#franchises:not(:empty) + #empty,#articles_content:not(:empty) + #empty{
    display: none;
}

/* Profile */
#franchise_buttons{
    position: sticky;
    top: 4rem;
    margin-top: -1rem;
    z-index: 2;
    background: var(--main_blue);
    backdrop-filter: blur(1rem);
    padding: 1rem 0;
    max-width: 100%;
}
#franchise_buttons ul {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 100%;
}
#franchise_buttons li {
    display: flex;
    align-items: center;
    color: var(--logo_blue);
    cursor: pointer;
    background: var(--white);
    width: 40%;
    border-radius: .25rem;
    padding: .25rem 1rem;
    box-sizing: border-box;
    justify-content: center;
    border: 1px solid var(--logo_blue);
    font-weight: 400;
}
#franchise_buttons svg{
    fill: var(--gray);
    margin-right: .5rem;
    height: 1rem;
    width: 1rem;
}
#franchise_buttons li.open :is(span,svg){
    fill: var(--green);
    color: var(--green);
}
#listing_favorites {
    display: flex;
    align-items: center;
    justify-content: center;
}
#listing_favorites span{
    display: none;
}
#listing_favorites .listing_favorites {
    background: rgba(0, 0, 0, .25);
    position: relative;
    width: 2rem;
    height: 2rem;
    box-sizing: border-box;
    top: auto;
    right: auto;
    cursor: pointer;
}
#listing_favorites .listing_favorites input{
    margin: 0;
}

#profile_header{
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1rem;
    grid-auto-columns: minmax(0, 1fr);
}
.profile_header_item:first-child { grid-area: 2 / 1 / 3 / 2; }
.profile_header_item:last-child { grid-area: 1 / 1 / 2 / 2; }
.profile_header_item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.profile_header_item.add_border{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--main_blue);
    min-height: 143px;
}
.profile_header_item.add_border:has(.profile_video){
    border-radius: 0 0 .5rem .5rem;
}
.profile_logo {
    margin-bottom: 1rem;
    text-align: center;
    box-sizing: border-box;
    background-color: var(--white);
    padding: .5rem;
    background: linear-gradient(to bottom,  var(--light_gray) 0%,var(--white) 100%); 
    border: 1px solid var(--light_gray);
    border-bottom: none;
}
.profile_logo img {
    display: block;
    margin: 0 auto;
    max-width: 50%;
    max-height: 7rem;
    padding: .5rem;
    border: 1px solid var(--border_blue);
    background-color: var(--white);
    border-radius: .5rem;
}
#profile_header .profile_title{
    margin: 0;
}
.profile_banner{
    position: relative;
    min-height: 143px;
}
.slider {
    position: absolute;
    top: calc(50% - .75rem);
    background: rgba(0, 0, 0, .25);
    padding: .5rem 0;
    border-radius: .25rem;
}
.slider.left {
    left: .25rem;
}
.slider.right {
    right: .25rem;
}
.slider svg{
    height: 1.5rem;
    display: flex;
    fill: var(--white);
}
.slider.left svg{
    transform: rotate(180deg);
}
.profile_banner img{
    object-fit: cover;
    width: 100%;
    max-height: 100%;
    display: block;
}
.profile_video {
    text-align: center;
    width: 100%;
    box-sizing: border-box;
    padding: .5rem;
    border-top: 1px solid var(--border_blue);
}
.profile_video a{
    color: var(--white);
    background-color: var(--button_blue);
    border-radius: 1rem;
    display: inline-flex;
    height: 1.5rem;
    padding: 0 1rem;
    font-size: .85rem;
    font-weight: 500;
    text-decoration: none;
    align-items: center;
}
.profile_details{
    margin: .5rem 0 1rem;
    font-size: .9rem;
}
#profile_columns{
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
    margin: .5rem 0 1rem;
}
.profile_column:not(:last-child){
    padding: 0 0 1rem;
}
.profile_column h2 {
    text-align: left;
    margin: -1rem -1rem 1rem;
    padding: 1rem;
    color: var(--white);
    background: var(--logo_blue);
    border-bottom: 1px solid var(--border_blue);
}
#profile_columns .profile_column:not(:first-child) h2{
    margin-top: 0;
    border-top: 1px solid var(--gray);
}
.profile_column p{
    margin-bottom: .5rem;
}
.profile_column table{
    width: 100%;
    font-weight: 500;
    font-size: .85rem;
    padding-top: .25rem;
}
.profile_column table tr td{
    border-bottom: 1px dashed var(--border_blue);
    padding-bottom: .25rem;
    padding-top: .25rem;
}
.profile_column table tr:first-child td{
    padding-top: 0;
}
.profile_column table tr:last-child td{
    padding-bottom: 0;
    border: none;
}
.profile_column .subtitle {
    font-weight: 400;
}
.profile_column .title {
    font-size: .75rem;
}
h2.profile_category {
    margin-top: .5rem;
}
.profile_column .value {
    padding-left: .5rem;
    color: var(--logo_blue);
}
.profile_learn_more{
    text-align: center;
    border: 1px solid var(--border_blue);
    box-sizing: border-box;
    background-color: var(--white);
    padding: 1rem;
}
.profile_learn_more h2{
    text-align: center;
}
.profile_learn_more p{
    margin-bottom: .5rem;
}
.profile_learn_more strong{
    font-weight: 500;
}
.profile_long_description{
    padding: 1rem 0 0;
}
.profile_long_description :is(strong,b){
    font-weight: 500;
}
.profile_long_description :is(em,u){
    font-style: italic;
}
.profile_long_description p{
    margin-bottom: 1rem;
}
.profile_long_description :is(ol,ul) {
    list-style-type: initial;
    margin: 0 0 1rem 1rem;
}
.profile_long_description :is(ol,ul) li:not(:last-child){
    padding-bottom: .5rem;
}
.profile_scrolltop {
    display: none;
    text-align: center;
    position: sticky;
    top: 4rem;
}
.profile_scrolltop span {
    border-radius: 1rem;
    display: inline-flex;
    height: 1.5rem;
    padding: 0 1rem;
    font-size: .85rem;
    font-weight: 500;
    background: rgba(0, 0, 0, .5);
    color: var(--white);
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.profile_footer_button{
    text-align: center;
    position: sticky;
    bottom: -1rem;
    box-shadow: 0 0 1rem rgba(255, 255, 255, .75);
    background-color: rgba(255, 255, 255, .75);
    -webkit-backdrop-filter: blur(.625rem);
    backdrop-filter: blur(.15rem);
    padding: 1rem 0;
    margin: 0 -1rem -1rem;
}

/*  */
.compare_section{
    width: 100%;
    padding-bottom: 1.5rem;
}

.compare-flex{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.compare_container .compare_thumbnail{
    width: 7rem;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDUwIDUwIiBmaWxsPSIjN0Y5QUM3Ij4KPHBhdGggZD0iTSAyNSAyIEMgMTIuMzA5NTM0IDIgMiAxMi4zMDk1MzQgMiAyNSBDIDIgMzcuNjkwNDY2IDEyLjMwOTUzNCA0OCAyNSA0OCBDIDM3LjY5MDQ2NiA0OCA0OCAzNy42OTA0NjYgNDggMjUgQyA0OCAxMi4zMDk1MzQgMzcuNjkwNDY2IDIgMjUgMiB6IE0gMjUgNCBDIDM2LjYwOTUzNCA0IDQ2IDEzLjM5MDQ2NiA0NiAyNSBDIDQ2IDM2LjYwOTUzNCAzNi42MDk1MzQgNDYgMjUgNDYgQyAxMy4zOTA0NjYgNDYgNCAzNi42MDk1MzQgNCAyNSBDIDQgMTMuMzkwNDY2IDEzLjM5MDQ2NiA0IDI1IDQgeiBNIDM0Ljk4ODI4MSAxNC45ODgyODEgQSAxLjAwMDEgMS4wMDAxIDAgMCAwIDM0LjE3MTg3NSAxNS40Mzk0NTMgTCAyMy45NzA3MDMgMzAuNDc2NTYyIEwgMTYuNjc5Njg4IDIzLjcxMDkzOCBBIDEuMDAwMSAxLjAwMDEgMCAxIDAgMTUuMzIwMzEyIDI1LjE3NzczNCBMIDI0LjMxNjQwNiAzMy41MjUzOTEgTCAzNS44MjgxMjUgMTYuNTYwNTQ3IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAzNC45ODgyODEgMTQuOTg4MjgxIHoiPjwvcGF0aD4KPC9zdmc+");
    background-origin: content-box;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 30px;
    height: 4rem;
    border: 2px dashed var(--logo_blue);
    border-radius: 8px;
}

.compare_container .compare_thumbnail.has_image{
    background-color: var(--white);
    background-size: cover;
    border: none;
    position: relative;
}

.compare_container .compare_thumbnail.has_image svg{
    position: absolute;
    right: -8px;
    top: -8px;
    width: 20px;
    fill: #be0000;
    cursor: pointer;
}

.compare_section .btn_compare{
    background-color: var(--gray);
    border: none;
    color: var(--white);
    pointer-events: none;
}

#btn_compare.active{
    background-color: var(--logo_blue);
    pointer-events: all;
}


.icon_round_bg{
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    height: 30px;
    width: 30px;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--border_blue);
}

.compare_thumbnail .compare-icon {
    color: var(--border_blue);
    opacity: 0.5;
    border-width: 3px;
}

.results_tile_icon{
    background-color: var(--white);
    position: absolute;
    /* bottom: -1rem; */
    margin-top: -1rem;
    right: 0.5rem;
    height: 35px;
    width: 35px;
    cursor: pointer;
}

.results_tile_icon input{
    opacity: 0;
    position: absolute;
    top: 0;
    height: 30px;
    width: 30px;
    cursor: pointer;
}

.results_tile_icon:has(input:checked){
    background-color: var(--logo_blue);
}

.results_tile_icon input:checked + svg{
    fill: var(--white);
}

.more_modal {
    position: absolute;
    right: 0.5rem;
    /* height: 3rem; */
    /* width: 7rem; */
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 5px -3px rgba(0, 38, 77, .14), 0 8px 10px rgba(0, 38, 77, .1), 0 3px 14px 2px rgba(0, 38, 77, .08);
    color: #565d65;
    line-height: 1.5;
    outline: none;
    padding: 0.75rem;
    width: 200px;
    z-index: 1;
    margin-top: 1.5rem;
}

.more_modal .fa-retweet{
    font-size: 1.3rem;
    color: var(--gray);
    width: 24px;
    height: 24px;
}

.more_modal .cash::before{
    margin-right: 0;
}

.more_modal i.heart:before {
    -webkit-mask: url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    mask: url(data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    -webkit-mask-size: contain;
    mask-size: contain;
    content: '';
    display: inline-flex;
    height: 1.5rem;
    width: 1.5rem;
    background-color: var(--gray);
}

.more_modal i.heart.checked:before {
    background-color: var(--red);
    -webkit-mask: url(data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    mask: url(data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
    -webkit-mask-size: contain;
    mask-size: contain;
}

.more_modal ul li{
    display: flex;
    gap: 0.25rem;
    line-height: 1;
    align-items: center;
    color: var(--text-color);
    font-weight: 400;
    font-size: 0.85rem;
    cursor: pointer;
}

.more_modal ul li:not(:last-child){
    padding-bottom: 0.5rem;
}

.modal_fav svg.checked{
    fill: #ca201b;
}


@media only screen and (max-width:1024px) {
    .compare_section.compare-flex{
        display: block;
    }

    .compare_section .compare_button{
        text-align: center;
    }

    .compare_section button{
        margin-top: 1rem;
    }
}

@media only screen and (max-width:767px) {
    .compare_container{
        /* display: inline-flex; */
        /* flex-wrap: nowrap; */
        overflow-x: scroll;
        justify-content: flex-start;
        padding-bottom: 0.5rem;
    }

    .compare_container .compare_thumbnail{
        flex: 0 0 80px;
    }

    .compare_container .compare_thumbnail.has_image svg{
        right: -5px;
        top: -3px;
        width: 15px;
    }

    .compare_section button{
        margin-top: 0.5rem;
    }

    .compare_section{
        padding-bottom: 0;
    }

    #main.main_compare{
        padding: 3.25rem 0.5rem 5rem;
    }
}

/*  */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    margin-left: 0.5rem;
}
  
.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}
  
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 34px;
}
  
.slider::before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 6px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
}
  
.switch input:checked + .slider {
    background-color: var(--logo_blue);
}
  
.switch input:focus + .slider {
    box-shadow: 0 0 1px var(--logo_blue);
}
  
.switch input:checked + .slider::before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

#franchises{
    padding-top: 0.5rem;
}

@media only screen and (max-width:992px) {
    .switch{
        width: 50px;
        height: 25px;
        margin: 0;
    }

    .slider::before{
        height: 20px;
        width: 20px;
        left: 0;
        bottom: 3px;
    }

    .switch input:checked + .slider::before {
        -webkit-transform: translateX(28px);
        -ms-transform: translateX(26px);
        transform: translateX(28px);
    }

    #franchise_title{
        flex-direction: row;
        justify-content: space-between;
    }
}

@media only screen and (max-width:767px) {
    .compare_by{
        display: flex;
        flex-direction: column;
        justify-content: end;
        border-left: 1px solid var(--light_gray);
        padding-left: 0.25rem;
    }

    .compare_by span{
        font-size: 0.8rem;
        margin-bottom: 0.1rem;
    }

    #franchise_title{
        margin-bottom: 0.8rem;
    }

    .slider::before{
        left: 2px;
        bottom: 2px;
    }

    .switch input:checked + .slider::before {
        -webkit-transform: translateX(24px);
        -ms-transform: translateX(26px);
        transform: translateX(24px);
    }
}

.compare_select + svg {
    padding: 8px;
}

.franchise_search.external_compare .compare_by .switch{
    pointer-events: none;
}