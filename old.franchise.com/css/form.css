/* BASE */
html, html body {
    font-size: 16px;
    color: #333;
    background: #fff;
    /* font-weight: 400; */
    line-height: 1.42857143;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}
h1, h2, h3, h4, h5 {
    font-weight: 700;
    font-family: sans-serif;
}

.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    max-width: 1140px;
}

.hide{
    display:none !important;
}
.bad .step2{
    display: none !important;
}

/* Form1 */

#header.fcom-header{
    border-bottom: none !important;
}

.fcom-header{
    padding: 0 !important;
    height: 70px;
}

.fcom-header svg{
    padding: 20px 0 0;
    display: inline-block; 
    width: 240px;
    max-width: 100%;
    pointer-events: none;
}

#form1 #form-step1,#form2 #form-step1{
    border: 1px solid #c0c0c0;
    border-radius: 5px;
    padding: 0 15px;
    margin-bottom: 50px;
}
form{
    font-family: 'Roboto', 'Gotham', "Helvetica Neue", Helvetica, Arial, sans-serif;
}

#form-step1 h1{
    padding: 20px 0 0;
    font-size: 2.2rem;
    color: #000;
    font-family: 'Montserrat', sans-serif;
    line-height: 1.2;
    text-align: center;
    font-weight: 400;
}

#form-step1 .center-block{
    padding: 15px 0 0;
    max-width: 66%;
    margin: auto;
    margin-bottom: 2rem;
    font-size: 1rem;
    
}

#form-step1 .form-email{
    width: 100%;
    position: relative;
}

#form-step1 .form-email label{
    background-color: white;
    padding: 0 10px;
    top: -8px;
    left: 30px;
    font-size: 11px;
    position: absolute;
    display: inline-block;
    margin-bottom: .5rem;
}

#form-step1 .form-email input{
    border-radius: 5px;
    border: 1px solid #c0c0c0;
    font-size: 1rem;
    height: 45px;
    width: 100%;
    padding-left: 10px;
    margin: 0 0 25px;
}

.form1-checkbox{
    margin-bottom: 8px;
}

#form-step1 button{
    font-weight: 400;
    border-radius: 5px;
    color: #FFF;
    min-height: 40px;
    width: 80%;
    font-size: 1.1rem;
    padding: 5px 15px 5px;
    line-height: 1;
    margin: 20px 0;
    background: #cc1209;
    text-align: center;
    border: none;
    font-family: 'Roboto', 'Gotham', "Helvetica Neue", Helvetica, Arial, sans-serif;
}

#pre-mail{

    margin: 0 0 25px;
    padding: 0 10px 10px;
}
@media only screen and (max-width : 767px) {
    #form-step1 h1{
        font-size:1.5rem;
        padding: 0;
    }
    #form-step1 h1+ p{
        padding: 0 10px;
        line-height: 1.3;
        font-size: 14px;
    }
    #form1 #form-step1, #form2 #form-step1{
        padding: 15px;
        border: none;
    }
    #form-step1 .center-block{
        max-width: 100%;
    }
    #form2 > .container,#form1 .container{
        padding: 0;
    }
    form#request_form .row {
        margin: 0;
    }
    #pre-mail {
        padding: 0 0 10px;
    }
    .row>.col-md-6{
        padding: 0;
    }
}

/* Form 2 Section */
.noconsent{display:none;}
#noconsent.required{
    border: 2px solid #cc1b00;
    background-color: #f8bfc5;
    padding: 10px;
    margin-bottom: 0.625rem;
    color: #cc1b00 !important;
}
#noconsent.required h3.noconsent:after{
    content:":";
}
#noconsent.required h3.noconsent{
    text-transform: capitalize;
    display:block;
    color: #cc1b00 !important;
    background: #cc1b00;
    font-weight: 700;
    font-size: 1rem !important;
    border: none;
    margin: -10px -10px 0.625rem;
    color: #FFF !important;
    padding: 5px 10px;
    line-height: 1.1;
}
#noconsent.required a{
    color: #600 !important;
}
#cart_selections{
    padding: 5px 10px;
    margin-bottom: 30px;
    background-color: #f1f1f1;
    border-radius: 5px;
}
#cart_selections > strong{
    font-family: 'Montserrat', sans-serif;
    display: block;
    text-align: center;
    font-weight: 500;
}
#cart_selections > em{
    display: block;
    text-align: center;
    line-height: 1.1;
    font-style: normal;
    font-size: 12px;
    color: #58647b;
}
li#nolistings {
    justify-content: center;
}
#form_basket {
    padding: 0;
    margin: 5px 0;
}
#form_basket li{
    transition: all .5s ease;
    padding: 10px;
    font-size: 15px;
    background-color: #fff;
    display: flex;
    align-items: center;
    line-height: 1.1;
    box-shadow: 0 0 3px rgb(0 0 0 / 25%);
}
#form_basket li + li{
    margin-top: 5px;
}
#form_basket li i {
    padding-right: 15px;
}
#form_basket li strong {
    margin-left: 5px;
}
#form_basket li:not(.for_display):not(#nolistings):hover {
    background: #efefef;
    cursor: pointer;
}
#form_basket{
    list-style: none !important;
}
#form_basket li strong{
    padding-left: 5px;
}

#form_basket input{
    margin-right: 10px;
}

#form_basket li.checked i{
    color: #5ea226;
}

#form_basket li:not(.checked):not(.for_display){
    color: #bbb;
}

#form_basket li:not(.checked) i{
    color: #bbb;
}

.backdrop{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
}

#cartIsEmpty, #oops{
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(0,0,0,.5);
    z-index: 100;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0 15px;
}

#cartIsEmpty .container,
#oops .container{
    position: relative;
    width: 600px;
    background: #fff;
    padding: 0 0 1px;
    box-shadow: 0 0 0 10px rgb(255 255 255 / 50%);
    border-radius: 5px;
    margin: 30px auto;
}

#cartIsEmpty .logo,
#oops .logo{
    padding: 15px;
    border-bottom: 1px solid #324972;
}

#cartIsEmpty .logo img,
#oops .logo img{
    max-width: 75%;
}

#cartIsEmpty p.logo,
#oops p.logo{
    margin: 10px 0;
}

#cartIsEmpty i,
#oops i {
    display: block;
    float: right;
    font-size: 70px;
    margin: 0 auto 10px;
}

#cartIsEmpty .logo a{
    text-decoration: underline;
    cursor: pointer;
}

#cartIsEmpty .button,
#oops .button{
    color: #fff;
    font-weight: 200;
    padding: 15px 10px;
    width: 100%;
    display: table;
    border-radius: 0;
    font-size: 14px;
    cursor: pointer;
    text-transform: uppercase;
    border: none;
    text-align: center;
    max-width: 200px;
    margin: 0 auto 10px;
    background: #cc1209;
}

#oops .close{
    padding: 15px;
    opacity: .25;
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
}

@media only screen and (max-width: 767px) {
    #cartIsEmpty .container,
    #oops .container{
        width: 100%;
    }
}
@media only screen and (min-width: 768px) {
    #cart_selections > em br{
        display: none;
    }
}

/* ERRORS */
#errors div {
    border: .063rem solid red;
    background-color: #ffc4c4;
    padding: .625rem;
    padding: .313rem;
    margin-bottom: .625rem;
    color: #cc1b00 !important;
}
#errors h3 {
    border-bottom: .063rem dashed red;
    margin: 0 0 .625rem;
    font-size: 1rem;
}
#loading,#page_loading{
    position: fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index: 999999;
    display: none;
}
#loading.wait{
    display: block;
}
#loading div,#page_loading div {
    width: 100%;
    height: 100%;
    display:table;
    background: rgba(51,60,78,.75);
}
#loading div i,#page_loading i {
   display:table-cell;
    vertical-align:middle;
    text-align:center;
    font-size: 5rem;
    color:#FFF;
}
#pre-mail label{
    padding: 0 10px;
    font-size: 11px;
    display: inline-block;
    margin-bottom: .5rem;
    font-family: 'Roboto',sans-serif;
}
#pre-mail span{
    color: #007bff;
    cursor: pointer;
}
#pre-mail span:hover{
    text-decoration: underline;;
}
#re-mail{
    display: none;
}