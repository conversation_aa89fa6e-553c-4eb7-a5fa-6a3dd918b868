/* BASE */
:root {
    --blue: #0F77CC;
    --green: #298826;
}

html, html body {
    font-size: 16px;
    color: #333;
    background: #fff;
    font-weight: 400;
    font-family: sans-serif; 
    line-height: 1.42857143;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}
h1, h2, h3, h4, h5 {
    font-weight: 700;
    font-family: sans-serif;
}
#valid_cookies{
    display: none !important;
}
p{
    margin-bottom: 1rem;
}
a{
    color: #0066b8;
}
#subcat_description ul,#subcat_description ol{
    padding-left: 1rem;
    margin-bottom: 1rem;
    list-style-type: square;

}
#subcat_description ol{
    list-style-type:decimal;
}
.pd-0{
    padding: 0 !important;
}

.pb-0{
    padding-bottom: 0 !important;
}

.mb-0{
    margin-bottom: 0 !important;
}

.no-border{
    border: none !important;
}
 
.red{
    color: red !important;
}

.green{
    color: green !important;
}

#breadcrumbs_container:after,.clear:after{
    clear: both;
    display: table;
    content: '';
}

.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    max-width: 1300px;
}

.no-events{
    pointer-events: none;
}

@media (min-width: 576px){
    .container {
        max-width: 540px;
    }
}
@media only screen and (max-width: 767px){
    .hidden-xs {
        display: none !important;
    }
}
@media only screen and (max-width: 992px){
    .hidden-sm {
        display: none !important;
    }
}
#basketPath i {
    color: #c0c0c0;
}
#basketPath.fill i  {
    color: #324972;
}

.text-center{
    text-align: center;
}

.text-left{
    text-align: left;
}

.text-end{
    text-align: right;
}

/* HEADER */
header#header {
    border-bottom: 1px solid #324972;
    padding: .5rem 0;

}
header#header .row {
    padding: 0;
}
header#header .col-xl-3 > * {
    display: inline-block;
    font-size: 1rem;
}
.home-body header#header .col-xl-3 > * {
    font-size: .938rem;
}
header#header .row > *:first-child {
    padding-left: 0;
}
@media (max-width: 767px){
    header#header .row > * {
        padding: 0;
    }

    header#header .row > *:first-child{
        display: flex;
        align-items: center;
    }
}
header#header #logo a{
    display: block;
    padding: 0 20px;
    line-height: 70px;
}
header#header .row  > *:nth-child(3) {
    padding-right: 0;
}

header#header #logo a{
    line-height: 1;
}

header#header #logo svg {
    padding: 0;
    margin: 4px 0 0;
    display: inline-block; 
    width: 240px;
    max-width: 100%;
    pointer-events: none;
}

@media only screen and (max-width:768px) {
    header#header #logo{
        padding: 0 15px;
    }

    header#header #logo a{
        padding: 0;
    }
}

#menuToggle{
    /* padding-left: 20px; */
    padding: 0 22px 0 20px;
}
#menuToggle i{
    font-size: 0.92rem;
}
.headerItem {
    color: #2b2b2b;
    padding: .5rem 1.375rem;
    cursor: pointer;
    text-transform: uppercase;
    border:none;
}
.headerItem i{
    color: #324972;
}

@media only screen and (max-width:767px) {
    #basketPath.headerItem{
        padding: 8px 16px;
    }
}

#textToggle {
    position: relative;
    padding:0;
}
#textToggle i{
    padding: 1rem;
    padding: 0 22px;
    /* font-size: .938rem; */
    font-size: 0.9rem;
}
#wordSearch {
    height: 70px;
    background: #324972;
    position: absolute;
    top: 70px;
    left: -1px;
    z-index: 99999;
    width: 320px;
    display: none;
    border: 1px solid #fff;
    border-top: none;
}
#wordSearch input[type=text] {
    width: calc(100% - 30px);
    height: 40px;
    padding-left: 10px;
    border-radius: 0;
    margin: 15px;
    border: 1px solid #90a0c6;
}
#textToggle.active, #textToggle.active i {
    background: #324972;
    color: #fff;
}
#textToggle.active, #textToggle.active i {
    background: #324972;
    color: #fff;
}
#textToggle.active #wordSearch {
	display:block;
}
.cart #header .text-right svg {
    fill: #324972;
    cursor: pointer;
    margin: 20px;
}
#cart.hidden,.hide{
    display:none !important;
}

/* MAIN MENU */
nav#main-menu {
    display: none;
    position: fixed;
    top: 0;
    height: 100%;
    z-index: 999;
    width: 260px;
    overflow-x: hidden;
    overflow-y: auto;
    background: #fff;
    border-right: 1px solid #324972;
}
body.opened nav#main-menu{
    left:0;
    display: block;
}
body.opened{
    opacity: 1 !important;
    overflow: hidden;
    width: 100vw;
    position: absolute;
    left: 260px;
}

nav#main-menu ul li:hover, nav#main-menu ul li.active, nav#main-menu ul li.sidr-class-active {
    border-top: 0;
    line-height: 49px;
}
nav#main-menu ul>li {
    border-bottom: 1px solid #324972;
}
nav#main-menu ul li.has-sub>span, nav#main-menu ul>li>a {
    color: #2b2b2b;
    padding: 0 1.375rem;
    cursor: pointer;
    text-transform: uppercase;
    display: block;
    line-height: 70px;
    position: relative;
    text-decoration: none !important;
}
nav#main-menu ul li.has-sub>span:hover, nav#main-menu ul>li>a:hover {
    background: #dedede;
}
nav#main-menu ul li.has-sub>span, nav#main-menu ul.main-menu>li>a {
    color: #2b2b2b;
    padding: 0 1.375rem;
    cursor: pointer;
    text-transform: uppercase;
    display: flex;
    line-height: 70px;
    position: relative;
    text-decoration: none !important;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
}
nav#main-menu ul li.has-sub>span i {
    float: right;
}

nav#main-menu ul li.has-sub>span.show i{
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}
nav#main-menu ul li.updates span {
    background: #cc1209;
    color: #fff;
    padding: 0 1.375rem;
    cursor: pointer;
    text-transform: uppercase;
    display: block;
    line-height: 70px;
}
nav#main-menu ul li.has-sub ul {
    display: none;
}
nav#main-menu ul li.has-sub .show + ul {
    display: block;
}
nav#main-menu ul li.has-sub ul a {
    color: #2b2b2b;
    padding: 0 1.375rem;
    cursor: pointer;
    display: block;
    line-height: 40px;
    position: relative;
    border-top: 1px solid #ddd;
    text-decoration: none !important;
    text-transform: none;;
}
nav#main-menu ul li.has-sub ul li{
    border:none;
}
#modal_open {
    display: none;
    background: rgba(0,0,0,.5);
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    position: absolute;
}
#modal_open.show{
    display: block;
}



/* Intro Section */

#main .container{
    padding: 0 12px;
}

#intro .center{
    text-align: center;
    margin: auto;
}

#intro h2{
    transition: opacity 0.1s ease 0s;
    color: #000;
    padding: 20px 0 0;
    font-size: 2rem;
}

.custom-html h2{
    font-size: 1.4rem;
    margin-bottom: 1rem;
}
.custom-html h2:first-of-type{
    margin-top: 1rem;
}

#intro .city-title{
    padding: 10px 0 0;
    font-size: 1.5rem;
    color: #000;
    font-family: sans-serif;
    line-height: 1;
    text-align: center;
}

#intro .br-bottom{
    margin-bottom: 0.5rem;
    border-bottom: 2px dashed #dedede;
    padding-bottom: 1.5rem;
    text-align: center;
}

#state_select{
    position: relative;
    display: inline-block;
    background-size: 13px;
    background-repeat: no-repeat;
    background-position: right;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAgBAMAAACiDzYSAAAABGdBTUEAALGPC/xhBQAAADBQTFRFzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJzBIJAAAAzBIJf33wSwAAAA90Uk5TBllQZR8lhE1fA3J1XJAAsD0kKwAAAMhJREFUKM910NsNwjAMhWEvxZiM0BU6Ahv0DYEECrcXQGq9QWUSx3WOI9G3nD+fVIWYXyfuvndZiHlab13RhfhzlxTDrAvxXqRDky6UiUQ014XyhQ7ZQuVCQM+6nClJh5SIHGmRiOZK1gM97hEZuea/HgJykss3ICfldRA1UgqiRkoBBERLQ0C08GAPgaQWtodIQKws9bIAsWIIyVYArWMogC4ci6ONeHG0kVYMOWnFkBMoihqBoqgRLBkBwZIRkFCWNP4pvMPDD0Li7MoZk5jUAAAAAElFTkSuQmCC);
}

#state_select select{
    appearance: none;
    -webkit-appearance: none;
    margin: 0;
    color: #555;
    padding: 0;
    padding-right: 15px;
    background: 0 0;
    border: none;
    position: relative;
    outline: 0;
    transition: width .2s;
    font-size: 1.75rem;
}

#state_select:after{
    display: block;
    content: "";
    border-bottom: 2px dashed #ccc;
}

@media only screen and (max-width : 767px) {
    #intro h2{
        font-size: 1.2rem;
    }

    #state_select #state, #state_select #state option{
        font-size: 0.8rem !important;
    }
}

#intro p, #intro div.search-description{
    margin: 1em 0;
    font-size: 1rem;
    color: #333;
    font-family: "Roboto","Gotham","Helvetica Neue",Helvetica,Arial,sans-serif;
    line-height: 1.42857143;
}

@media only screen and (min-width : 768px){
    .hidden-lg{
        display: none;
    }
}

@media only screen and (max-width : 767px) {
    .hidden-xs{
        display: none;
    }
}

.city-info{
    margin: 20px 0;
    display: flex;
}

.city-info-item{
    flex: 0 0 33.33%;
    background: #efefef;
    padding: 20px;
    margin-bottom: 2rem;
}

.city-info-item h3{
    font-size: 1.2rem;
    padding: 0 0 20px;
}

.city-info p{
    margin-top: 0.5rem;
}

.city-info .gray-section label {
    font-weight: 600;
    clear: both;
    display: block;
    font-family: 'Montserrat',sans-serif;
    margin-top: 10px;
}

.city-explore-btn .explore{
    background: #cc1209;
    color: #fff;
    font-weight: 400;
    padding: 15px 20px;
    width: auto;
    display: table;
    border-radius: 0;
    font-size: 0.9rem;
    cursor: pointer;
    text-transform: uppercase;
    margin: 0 auto;
    border: none;
    max-width: 100%;
    text-decoration: none !important;
    margin-bottom: 2rem;
    text-align: center;
    line-height: 1.1;
}

.city-explore-btn .explore:hover{
    color: white !important;
}

.city-explore-btn h2{
    font-size: 1.5rem !important;
    margin: 10px;
}

@media only screen and (max-width : 767px){
    .city-info{
        display: block;
    }
}

/* Results section */
#results {
    display: flex;
    flex-flow: row wrap;
    /* justify-content: center; */
    margin: 0 auto;
    width: 100%;
    margin-bottom: 3rem;
}

#results .result-item{
    padding: 10px;
    flex: 0 1 25%;
}

#results .result-item .item{
    position: relative;
    border: 1px solid #324972;
    background: white;
}

#results .result-item .result-img{
    display: flex;
    justify-content: center;
}

#results .result-item 
.concept-logo{
    margin: 15px auto 0;
    height: 60px;
    width: auto;
    min-width: 120px;
    max-width: calc(100% - 10px);
    box-shadow: 0 0 0 1px rgb(0 0 0 / 10%);
    display: table;
}

#results .result-item h3{
    min-height: 45px;
    padding: 0 5px;
    font-size: 1rem;
    font-weight: 700;
    margin: 5px 0 10px;
    line-height: 18px;
    border-bottom: 1px solid #324972;
    overflow: hidden;
}

#results .result-item h3 a{
    color: black;
}

#results .result-item p{
    height: auto;
    font-size: 14px;
    margin: 0 10px;
    overflow: hidden;
}

#results .result-item h4{
    /* padding: 0 10px 5px; */
    color: #31486e;
    font-size: 0.8rem;
    font-weight: 700;
    font-family: sans-serif;
}

#results .result-item h4:last-child{
    height: 35px;
    overflow: hidden;
}

#results .result-item .cash-block{
    /* margin: 2rem 0; */
    padding: 0 10px 10px;
    margin-top: 30px;
    min-height: 70px;
}

#results .result-item 
.add-concept span span{
    margin-right: 10px;
    font-size: 1.2rem;
}

#results .result-item .merch-banner{
    position: absolute;
    content: "";
    height: 90px;
    width: 90px;
    right: -4px;
    top: -6px;
}

.result-checkbox{
    position: relative;
    cursor: pointer;
}

.result-checkbox .temp-checkbox{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
}

#results .view-cities {
    margin: 2% 1% 0;
    flex: 0 0 98%;
    text-align: center;
    display: block;
    background: #0077b3;
    padding: 10px;
}

#results .view-cities a{
    font-size: 14px;
    width: 100%;
    display: block;
    color: #fff !important;
    text-decoration: none !important;
}

#results .view-cities a span{
    display: block;
    font-size: 1.3rem;
}

#results .view-cities a strong{
    font-weight: 400;
    font-size: 1rem;
    margin-top: 0.6rem;
    display: block;
    text-transform: uppercase;
}

#refresh{
    display: flex;
    justify-content: center;
}

#refresh button{ 
    background: var(--green);
    font-size: 18px;
    font-weight: 400;
    max-width: 400px;
    width: 100%;
    color: #fff;
    border: none;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 2rem;
}

#results .result-item .item::before{
    background-repeat: no-repeat;
    position: absolute;
    content: "";
    height: 90px;
    width: 90px;
    right: -4px;
    top: -4px;
    display: none;
}

#results .result-item .item.new-tag::before{
    background: url(/images/ribbon-newopp.svg);
    display: block;
}

#results .result-item .item.now-trending-tag::before{
    background: url(/images/ribbon-now_trending.svg);
    display: block;
}

#results .result-item .item.popular-tag::before{
    background: url(/images/ribbon-popular.svg);
    display: block;
}

#results .result-item .item.fast-seller-tag::before{
    background: url(/images/ribbon-fast.svg);
    display: block;
}

#results .nope{
    border-top: 2px dashed #dedede;
    padding: 10px 0 0;
    text-align: center;
}

@media only screen and (max-width: 767px){
    #results{
        padding: 0;
        /* display: block; */
    }

    #results .result-item{
        /* flex: 0 1 calc(100%) !important; */
        flex: 0 1 50%;
        padding: 15px 7.4375px 0;
    }

    .follow{
        padding: 0 5%;
    }
}

@media only screen and (max-width: 767px){
    #results{
        display: block;
    }

    #results .result-item .cash-block{
        margin-top: 10px;
    }

    #home #results .result-item p{
        height: 120px;
    }
    #results .result-item h3{
        min-height: auto;
        padding-bottom: 10px;
    }

}

/* Listing Buttons */

.listing button {
    background-color: #324972;
    color: #FFF;
    border: none;
    display: block;
    /* width: calc(100% + 1.25rem); */
    font-size: 0.875rem;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 15px 10px;
    text-transform: uppercase;
}

.listing button:before{
    content: '';
    margin-right: 10px;
    height: 0.7rem;
    width: 0.7rem;
    display: inline-block;
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgMTBoLTEwdi0xMGgtNHYxMGgtMTB2NGgxMHYxMGg0di0xMGgxMHoiLz48L3N2Zz4=');
    -webkit-mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    mask-size: cover;
    background-color: #FFF;
}

.listing button.noshow {
    background-color: #dcdbd8;
    cursor: default;
}

.listing button.noshow:before {
    display:none;
}
.listing button.noshow:after{
    color: #dcdbd8;
    content:'Unavailable';
}
.listing input:checked + button{
    background-color: #CC1209;
    cursor: pointer;
}

.listing input:checked + button:before{
    -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
    mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+');
    -webkit-mask-size: cover;
    mask-position: 50% 50%;
    mask-repeat: no-repeat;
    mask-size: cover;
}

.listing input:checked + button span{
    font-size: 0px;
}

.listing input:checked + button span::after{
    font-size: 0.9rem;
    content: 'Added to cart';
}

.nope {
    background-color: #dedede;
    /* padding: 1.5rem 1.5rem 0; */
    padding: 1.5rem;
    margin: 1rem auto 0;
}

.nope p{
    margin: 0;
}

/* Bottom Cart Section */
#bottom{
    position: fixed;
    bottom: -5rem;
    right: 0;
    left:0;
    transition: bottom .5s ease-out;    
    background-color: #324972;
    z-index: 16;
    font-family: 'Roboto',"Helvetica Neue",Helvetica,Arial,sans-serif;
}
#bottom.show{
    bottom: 0;
}

#bottom.show footer#lower{
    padding-bottom: 60px !important;
} 

button#cart_bottom {
    background: none;
    border: none;
    margin: 0 auto;
    display: flex;
    max-width: 100%;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    color: #FFF;
    font-size: 1rem;
    padding: 10px;
}
button#cart_bottom .cart_count {
    background: #FFF;
    display: block;
    height: 40px;
    width: 40px;
    line-height: 40px;
    border-radius: 50%;
    color :#cc1209;
    font-weight: 600;
    font-size: 1.2rem;
}
button#cart_bottom .button{    
    background-color: #cc1209;
    color: #FFF;
    height: 40px;
    padding: 0 25px;
    text-transform: uppercase;
    line-height: 40px;
    font-size: 1.2rem;
    height: 100%;
    font-weight: 700;
}
button#cart_bottom .text {
    color: white;
    text-transform: uppercase;
    padding: 0 15px;
    font-weight: 700;
    font-size: 1.2rem;
}

button#cart_bottom .text a{
    color: white;
}

.plural button#cart_bottom .text em:before{
    content: "s";
}

@media only screen and (max-width:767px) {
    footer#lower{
        font-weight: 200;
    }
}

@media only screen and (max-width:375px) {
    #bottom .container{
        padding: 10px;
    }

    button#cart_bottom{
        padding: 0;
    }
}

/* FOOTER */
footer#upper {
    background-color: #383f4d;
    color: #fff;
    line-height: 50px;
    height: 50px;
    font-weight: 200;
}
footer#upper a {
    color: #90a0c6;
    text-decoration: none;
    font-size: 22px;
    height: 18px;
    line-height: 19px;
    width: 18px;
    vertical-align: text-bottom;
    margin: 0 1px 0 10px;
}

#subcat{
    text-align: center;
    padding: 5px;
    font-size: 14px;
    background-color: #3d4454;
}
#subcat span{
    font-family: Montserrat,sans-serif;
    font-size: 15px;
    font-weight: 700;
    color: #fff;
    text-transform: uppercase;
    padding-right: 10px;
}
#subcat a {
    color: #90a0c6;
    text-transform: none;
}
#subcat a + a:before{
    color:#FFF;
    content:'-';
    padding:0 5px;
    display: inline-block;
}
#subcat_description {
    font-weight: 200;
}
#subcat_description h3,
#subcat_description h2 {
    font-size: 16px;
    margin-bottom: .5rem;
    line-height: 1.2;
}

#upper1{
    background-color: #383f4d;
    color: #fff;
    line-height: 50px;
    height: 50px;
    text-align: right;
}

#upper1 a{
    text-decoration: none!important;
    font-size: 1rem;
}

#upper1 a i{
    color: #90a0c6;
    font-size: 1.2rem;
    /* padding: 0 5px; */
    height: 18px;
    line-height: 19px;
    width: 18px;
    vertical-align: text-bottom;
    margin: 0 1px 0 10px;
}

#upper1 a:hover{
    color: initial !important;
}

footer#lower{
    background-color: #3d4454;
    font-weight: 200;
    color: #fff;
    font-size:14px;
}

footer#lower .pad-10{
    padding : 0 10px;
}

footer#lower h2{
    text-transform: uppercase;
    padding-top: 30px;
    border-bottom: 1px solid #383f4d;
    padding-bottom: 20px;
    width: 100%;
    font-size: 0.938rem;
    margin-bottom: 15px;
    letter-spacing: -.5px;
    line-height: 1.339rem;
}

footer#lower ul{
    list-style: none;
    line-height: 2;
}

footer#lower a {
    color: #ffffff;
    /* color: #90a0c6; */
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.42857143;
}

footer#lower p, footer#lower .block2-item{
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.apps a {
    display: inline-block;
    width: 45%;
}

.apps a:last-child{
    margin-left: -4px;
}

footer .lower-block{
    display: flex;
    padding-bottom: 15px;
}

footer .lower-block .lower-item{
    flex: 0 1 calc(25%);
}

footer .lower-block .block2-item{
    flex: 0 1 calc(50%);
}

footer .ruler{
    background: #383f4d;
    width: 100%;
    height: 1px;
    margin: 1rem 0 2.5rem;
}

footer .footer-divider{
    margin-bottom: 30px;
    min-height: 1px;
    width: 100%;
    border-bottom: 1px solid #383f4d;
}

footer#lower .ifa{
    max-height: 55px;
}

footer#lower .row .col-lg-3 {
    margin-bottom: 15px;
    padding: 0;
}
footer#lower .col-lg-4, footer#lower .col-lg-8 {
    margin-bottom: 15px;
    color: #fff;
    font-size: 0.938rem;
    font-weight: 400;
}

footer#lower .col-lg-8 a{
    font-size: 0.938rem;
}

footer#lower .ft-copyright{
    text-align: end;
}

@media only screen and (max-width : 767px) {
    footer .lower-block .lower-item{
        flex: 1 0 calc(50%);
        width: 100%;
    }

    footer .lower-block{
        padding: 15px;
        flex-wrap: wrap;
    }

    footer#lower .ft-copyright{
        text-align: left;
    }
    
}

@media only screen and (max-width : 600px) {
    footer .lower-block .lower-item, 
    footer .lower-block .block2-item {
        flex: 0 1 calc(100%);
    }

    footer .lower-block{
        display: block;
    }

    #upper1 .row .col-md-12{
        text-align: left;
    }

    footer#lower .row .col-lg-3{
        padding: 0 12px;
    }
}

#breadcrumbs{
    width: 100%;
    text-align: left;
    list-style-type: none;
    padding: 20px 10px 0;
    color: #58647b;
}

#breadcrumbs li {
    font-weight: 400;
    /* display: inline-flex; */
}

#breadcrumbs li a {
    color: #58647b;
    font-size: .8rem;
    text-transform: uppercase;
    text-decoration: none;
}

#breadcrumbs li a i{
    margin-right: .5rem;
    font-size: 0.65rem;
}

@media only screen and (min-width: 768px) {
    #breadcrumbs li a:hover{
        color: #cc1209;
    }
}

#breadcrumbs li+li::before {
    content: '\00bb';
    margin: 0 .7rem 0 0;
}

#breadcrumbs li:last-child{
    pointer-events: none;
}

@media only screen and (max-width: 767px) {
    #breadcrumbs{
        line-height: 2;
        flex-direction: row !important;
        flex-wrap: wrap;
        gap: 0 8px;
    }
}

#heading:not(.franchise-header){
    padding-bottom: .5rem !important;
}

#heading{
    margin-top: 1rem;
    margin-bottom: 2rem;
    border-bottom: 2px dashed #dedede;
}

#heading .page-title{
    /* display: block;
    font-size: 2.2rem;
    color: #000;
    font-family: sans-serif;
    line-height: 1.2;
    text-align: center; */
    padding: 20px 0 0;
    font-size: 36px;
    color: #000;
    font-family: sans-serif;
    line-height: 1.2;
    margin-bottom: 10px;
    text-align: center;
}

#copy #base {
    padding: 0 1rem 2rem;
}

#copy #base p,
#heading .description{
    font-size: 1rem;
    font-family: sans-serif;
    font-weight: 400;
    color: #333;
    margin-bottom: 1rem;
}

#heading .description{
    margin: 1rem 0;
    text-align: center;
}

#copy #base .contact-row{
    display: flex;
    justify-content: center;
    width: 100%;
}

#copy #base .contact-row 
.contact-item{
    flex: 0 1 calc(33.3333333333%);
    margin: 15px;
    text-align: center;
}

#copy #base .contact-item h3 {
    font-size: 1.25rem;
    line-height: 1.2;
    margin: 1rem 0 .5rem;
}

.contact-form{
    width: 100%;
    margin: auto;
    border: 1px solid #dedede;
    padding: 2rem;
    background: #dedede;
}

.contact-form form .ct-form-row{
    display: block;
}

.contact-form form .ct-form-row .small-12{
    width: 100%;
    padding: 0 10px;
}

.contact-form form .ct-form-row label{
    font-size: 0.875rem;
}

.contact-form form .ct-form-row label span{
    color: #cc1209;
    font-size: 75%;
}

.contact-form form input, .contact-form form select {
    height: 45px;
    width: 100%;
    padding-left: 10px;
    margin: 0 10px;
    font-size: 0.95rem;
    margin: 0 0 15px;
    border: 2px solid #dedede;
    caret-color: #009cf7;
}

.contact-form form textarea{
    border: 2px solid #dedede;
    width: 100%;
    margin: 0 0 15px;
    padding: 5px 10px;
}

.contact-form form .contact-btn-block{
    width: 100%;
}

.contact-form form .btn-orange{
    color: #fff;
    font-weight: 200;
    display: table;
    border-radius: 0;
    cursor: pointer;
    text-transform: uppercase;
    border: none;
    height: 40px;
    width: auto;
    font-size: 18px;
    padding: 6px 10px;
    line-height: 1;
    margin: 10px auto 20px;
    background: #cc1209;
}

.contact-us-page .horizontal-line{
    margin-top: 2rem;
    margin-bottom: 1rem;
    border-bottom: 2px dashed #dedede;
}

.flagged{
    border-color: #cc1b00 !important;
}
.formError {
    background: #faeeee;
    border: 2px solid #cc1b00;
    margin: 0 auto 2rem;
    width: 25rem;
    max-width: 100%;
}
.formError span {
    background: #cc1b00;
    padding: .5rem;
    font-weight: bold;
    color: #FFF;
    display: block;
}
.formError ul {
    margin: .5rem;
}
.formError ul li + li {
    padding-top: .25rem;
    margin-top: .25rem;
    border-top: 1px dashed #cc1b00;
}
@media only screen and (max-width: 767px) {
    #copy #base .contact-row{
        display: block;
    }

    #copy #base p{
        padding-bottom: 1rem;
        margin-bottom: 0;
    }

    #copy.container{
        padding: 0;
    }

    .contact-us-page{
        padding: 15px;
    }

    .contact-form{
        padding: 10px;
    }
}

@media only screen and (max-width: 600px) {
    #copy #base .contact-row{
        flex-wrap: wrap;
    }

    #copy #base .contact-row 
    .contact-item {
        flex: 0 1 calc(100%);
    }
}

#copy #base .faq-h2, 
#fcom-financing h2{
    font-size: 1.75rem;
    line-height: 1.2;
    margin: 2rem 0 .5rem;
}

#copy #base .faq-p{
    font-family: sans-serif;
    font-size: 1rem;
}

#fcom-financing h3{
    font-size: 1.3rem;
    line-height: 1.2;
    margin: 1rem 0 .5rem;
}

#fcom-financing ul {
    list-style-type: square;
}

#fcom-glossary #goto{
    margin-bottom: 2rem;
}

#fcom-glossary #goto ul{
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 100%;
}

@media only screen and (max-width : 600px) {
    #fcom-glossary #goto ul{
        flex-wrap: wrap;
        justify-content: left;
    }
}

#fcom-glossary #goto ul a{
    background: #dedede;
    padding: 2px 7px;
    font-size: 1rem;
    border: 1px solid #FFF;
    text-decoration: none !important;
    display: inline-block;
    color: var(--blue);
}

.goback{
    display: none;
}

#fcom-glossary {
    scroll-behavior: smooth;
    width: 100%;
    height: 100%;
    transition: all 0.2s ease-in;
}

#fcom-glossary #goto ul a.null{
    color: #bbb;
    cursor: default;
}

#fcom-glossary dl.news {
    padding-left: 2rem;
    margin-bottom: 1rem;
}

#fcom-glossary dl.news dt{
    display: list-item;
    font-size: 1.25rem;
    line-height: 1.2;
    margin: 1rem 0 .5rem;
    list-style-type: square;
    font-family: sans-serif;
    font-weight: 700;
}

#fcom-glossary dl.news dd{
    font-size: 1rem;
    margin: 0 0 16px 40px;
    font-family: sans-serif;
}

#fcom-glossary dl.news::after {
    margin: 2rem 0 0 -2rem;
    border-bottom: 5px solid #dedede;
    content: "";
    display: block;
    height: 2.5px;
    width: auto;
    background: #dedede;
}

#fcom-glossary .gotop {
    background: #dedede;
    display: inline-block;
    padding: 5px 20px;
    font-family: 'Montserrat', sans-serif;
    margin-bottom: 1rem;
    text-decoration: none !important;
    color: var(--blue);
}

#fcom-glossary .gotop i{
    margin: 0 0 0 10px;
    position: relative;
    animation: bounce 2s infinite;
    font-style: normal;
}

@keyframes bounce {
    0% {
      top: 0;
      animation-timing-function: ease-in;
    }

    35% {
      top: 0.3rem;
      animation-timing-function: ease-out;
    }

    70% {
        top: 0;
    }

    100% {
      top: -20px;
    }
  }

  @media only screen and (max-width : 600px) {
    #heading .page-title{
        font-size: 2.2rem;
        margin-top: 0;
    }

    #fcom-glossary dl.news dt{
        font-size: 1.2rem;
    }
  }

  #franchise-events .bf-events{
      display: flex;
  }

  #franchise-events .bf-events .events-item{
      flex: 0 1 33.3333%;
      padding: 0 12px;
  }

  #franchise-events .bf-events .events-item h2{
      font-size: 1.25rem;
      margin: 2rem 0 .5rem;
  }

  #franchise-events .bf-events 
  .events-item hr{
    margin: 8px 0;
  }

  #franchise-events .bf-events 
  .events-item p{
      font-size: 1rem;
  }

  @media only screen and (max-width : 767px) {
    #franchise-events .bf-events{
        display: block;
    }

    #franchise-events .bf-events .events-item{
        flex: 0 1 100%;
        margin: 0;
    }
  }

  /* Net Worth Calculator */

  #base form{
      margin-bottom: 2rem;
  }

  #base .nw-calc-row{
      display: flex;
  }

  #base .nw-calc-row .nw-calc-item{
      flex: 0 0 33.33%;
      padding: 0 1%;
  }

  #base .nw-calc-row .item{
    background: #dedede;
    box-sizing: border-box;
    margin-bottom: 1rem;
    padding: 0 12px;
    height: 100%;
  }

  #base .net-worth-totals .item{
    background: #404859;
    color: #FFF;
  }

  #base .nw-calc-row .item h3{
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.2rem;
    line-height: 1.2;
    padding: 1rem 0 .5rem;
    font-family: sans-serif;
    font-weight: 700;
  }

  #base .nw-calc-row input {
    height: 45px;
    width: 100%;
    padding-left: 10px;
    font-size: 12px;
    margin: 0;
    border: none;
  }

  #base form .nw-calc-row label{
    padding-bottom: .25rem;
  }

#base form .nw-calc-row label:not(:first-of-type) {
    display: block;
    border-top: 2px solid #000;
    padding: 1rem 0 .25rem;
}

.fa-money::before{
    content: "\f0d6";
}

.pad-change{
    padding-bottom: 0 !important;
    margin-bottom: 2rem;
}

#base .net-worth-subtitle h3 i{
    padding-right: 5px;
}

@media only screen and (max-width : 767px) {
    #base .nw-calc-row{
        display: block;
    }
}

#base .privacy p b{
    font-family: 'Montserrat',sans-serif;
    font-size: 1rem;
}

#base .privacy .date b{
    font-family: sans-serif;
}

#base .privacy a{
    color: #0d82df;
}
#base .privacy :is(h2,h3) {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.75rem;
    line-height: 1.2;
    margin: 2rem 0 .5rem;
}
#base .privacy h3 {
    font-size: 1.25rem;
    margin: 1rem 0 .5rem;
}
#base .privacy :is(ul,ol) {
    padding-left: 1.5rem;
}

/* Sales Index */
#si-results .si-results-row .si-results-item.active{
    position: relative;
}
#si-results .si-results-row .si-results-item::before {
    background-repeat: no-repeat !important;
    position: absolute;
    content: "";
    height: 90px;
    width: 90px;
    right: 7px;
    top: 7px;
    display: none;
    z-index: 1;
}

#si-results .si-results-row .si-results-item.active::before {
    background: url(/images/ribbon-fcomsale.svg);
    display: block;
}
#base #salesindex #si-intro 
.si-top-container{
    max-width: 800px;
    margin: auto;
}

#base #salesindex #si-intro 
.si-top-container .si-top-row{
    display: flex;
    margin: 1rem 0 2rem;
}

.si-top-container .si-top-row 
.si-top-item1,
.si-top-container .si-top-row 
.si-top-item2{
    padding: 0 12px;
}

.si-top-container .si-top-row 
.si-top-item1{
    flex: 0 0 66.66%;
    text-align: center;
}

.si-top-container .si-top-row 
.si-top-item1 h1{
    font-family: 'Montserrat',sans-serif;
    padding: 20px 0 0;
    color: #324972;
    padding-top: 0;
    font-size: 1.5rem !important;
    margin-bottom: 10px;
    font-weight: 700;
}

.si-top-container .si-top-row 
.si-top-item1 p strong{
    color: #cc1209;
}

.si-top-container .si-top-row 
.si-top-item2{
    flex: 0 0 33.33%;
    text-align: center;
}

#si-report-header .subtitle{
    color: #666;
    font-weight: 400;
    text-align: center;
    text-transform: uppercase;
    font-size: 1.8rem;
    font-family: 'Montserrat',sans-serif;
    line-height: 1;
    margin: 20px 0;
}

#si-report-header .subtitle strong {
    display: flex;
    font-family: sans-serif;
    font-size: 20px;
    color: #333;
    font-weight: 700;
    align-items: center;
    justify-content: center;
    display: flex;
    align-items: center;
    justify-content: center;
}
#si-report-header .subtitle strong a,#si-report-header .subtitle strong em{
    margin: 0 10px;
}
#si-report-header .subtitle strong a{
    color: var(--green);
    font-weight: 700;
    font-size: 120%;
    line-height: 20px;
    display: flex;
    align-items: center;
}
span#calendar_year {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin: 0;
}
span#calendar_year span {
    padding: 0 1rem;
}
#si-report-header .sorting{
    color: #324972;
    text-transform: uppercase;
    text-align: center;
    font-size: 1rem;
    font-family: sans-serif;
    font-weight: 700;
}
strong.hover_strong{
    cursor: pointer;
}

#si-report-header .sorting::after{
    content: '';
    background: #27ae61;
    height: 3px;
    width: 40px;
    display: block;
    margin: 10px auto 20px;
}

#si-report-header #sort-results{
    max-width: 100%;
    display: flex;
    margin: 0 auto;
    justify-content: center;
}

#si-report-header #sort-results>*{
    padding: 0 10px;
    border-left: 5px solid #fff;
    height: 40px;
    color: #324972;
    text-transform: uppercase;
    font-size: 1rem;
    width: 180px;
    max-width: 33.33333%;
    cursor: pointer;
    border-right: 1px solid #eee;
    display: flex;
    align-items: center;
    line-height: 1.2;
    text-align: left;
}

#si-results{
    margin: 1rem 0 2rem;
}

#sort-results>*::before{
    content: '\220E';
    color: #cecece;
    margin-right: 10px;
    float: left;
}

#si-report-header #sort-results>*.active {
    border-left-color: #355894;
    background: #235bb5;
    color: #fff;
}

#si-results .si-results-row{
    display: flex;
    flex-wrap: wrap
}

#si-results .si-results-row .si-results-item{
    flex: 0 0 33.33%;
    padding: 10px;
}

#si-results .si-results-row .si-results-item .item{
    position: relative;
    border: 1px solid #e7e7e7;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAYAAABytg0kAAAAGklEQVQImQXBAQEAMAzDIHb/Pisjh6uCtw18UOsGtZ+3RSYAAAAASUVORK5CYII=);
    padding: 15px;
    height: 100%;
}

.si-results-row .si-results-item:nth-child(even) .item
{
    border: 1px solid #27ae61 !important;
}

#si-results .si-results-row
.si-results-item .item .sale-img{
    position: absolute;
    height: 90px;
    width: 90px;
    right: -4px;
    top: -6px;
}

#si-results .si-results-row .si-results-item .item .upping{
    font-family: sans-serif;
    /* height: 208px; */
    overflow: hidden;
    /* margin-bottom: 2rem; */
}

#si-results .si-results-row .si-results-item .item .upping h3.top{
    padding-right: 38px;
    margin-bottom: 8px;
}

#si-results .si-results-row .si-results-item .item .upping .top a{
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #333 !important;
    text-transform: uppercase;
}

#si-results .si-results-row .si-results-item .item .upping .top>span{
    font-size: 1rem;
    font-weight: 500;
}

#si-results .si-results-row .si-results-item .item .upping h4.top{
    padding-right: 38px;
    display: table;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 400;
    color: #324972;
}

#si-results .si-results-row .si-results-item .item span.type{
    font-size: 16px;
    margin-top: 10px;
    font-weight: 400;
    color: #324972;
}

#si-results .si-results-row .si-results-item .item .flexxed {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

#si-results .si-results-row .si-results-item .item .flexxed div {
    text-align: center;
}

#si-results .si-results-row .si-results-item .item .flexxed .added {
    text-transform: uppercase;
    font-size: 24px;
    color: #324972;
    font-family: sans-serif;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.flexxed button.getfree,.flexxed a.getfree,.flexxed div.learn_more {
    background: #cc1209;
    font-family: 'Montserrat',sans-serif;
    color: #fff;
    font-weight: 700;
    padding: 14px 10px;
    width: 180px;
    display: table;
    font-size: 0.8rem;
    cursor: pointer;
    text-transform: uppercase;
    margin: 0 auto;
    border: none;
    letter-spacing: 1px;
    line-height: 1;
    border-radius: 10px;
}
.flexxed div.learn_more {
    background: #235bb5;
    cursor: pointer;
}

.si-checkbox{
    position: relative;
}

.si-checkbox input{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
}

.si-checkbox input:checked + button.getfree{
    background: #235bb5;
    pointer-events: none;
    cursor: default;
}

.flexxed button.learn {
    background: #235bb5;
}

@media (max-width: 767px){
    #si-results .si-results-row .si-results-item{
        flex: 0 0 50%;
    }
}

@media (max-width: 600px){
    #si-results .si-results-row,
    .si-top-container .si-top-row{
       display: block !important;
    }

    .si-top-container .si-top-row .si-top-item2 img{
        max-width: 200px;
    }

    #si-results .container{
        padding: 0 !important;
    }

    .si-results-item .item .upping{
        height: auto;
    }

    .si-results-item .item .upping p{
        display: none;
    }

    .si-results-item .item .upping .top a{
        font-size: 1rem;
    }

    .si-results-item .item .flexxed .added{
        font-size: 1.2rem;
    }

    #si-results .si-results-row .si-results-item,
    #si-results .si-results-row .si-results-item .item {
        padding: 5px;
    }

    .flexxed button{
        width: 150px !important;
    }

    #si-report-header #sort-results>*{
        padding: 0 5px;
        font-size: 0.6rem;
    }

}

/* Can i qualify */
#base .pages-section h2{
    font-size: 1.75rem;
    line-height: 1.2;
    margin: 2rem 0 .5rem;
}

#base .pages-section ul {
    margin-left: 2rem;
    list-style-type: square;
}

#base .pages-section h3 {
    font-size: 1.25rem;
    line-height: 1.2;
    margin: 1rem 0 .5rem;
}


/* Franchise USA */
.franchise-usa-main div#filter{
    position: relative;
}

.franchise-usa-main div#filter::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: -moz-linear-gradient(top, rgba(46,71,111,0) 0%, rgba(46,71,111,1) 150%);
    background: -webkit-linear-gradient(top, rgba(46,71,111,0) 0%,rgba(46,71,111,1) 150%);
    background: linear-gradient(to bottom, rgba(46,71,111,0) 0%,rgba(46,71,111,1) 150%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#002e476f', endColorstr='#2e476f',GradientType=0 );
    content: '';
    display: block;
    mix-blend-mode: multiply;
}

.franchise-usa-main div#filter #filters {
    padding: 15px 0 5px;
    width: auto;
}

.franchise-usa-main div#filter .container{
    background: none !important;
    border-radius: 0;
}

.franchise-usa-main div#filter form{
    z-index: 999;
    position: relative;
}

.franchise-usa-main div#filter #filters #fafh1{
    margin: 0 0 15px;
}

.franchise-usa-main div#filter #filters #fafh1 h1{
    padding: 10px 25px;
    cursor: pointer;
    font-family: 'Montserrat',sans-serif;
    line-height: 1;
    margin: 0 -15px;
    color: #fff;
    border-radius: 5px 5px 0 0;
    font-size: 2.25rem !important;
    background-color: transparent!important;
    text-align: center!important;
    text-shadow: 0 0 7.5px rgb(0 0 0 / 75%);
}

.franchise-usa-main div#filter #filters .item{
    background-color: rgba(61,68,84,.8);
    border-radius: 0 0 5px 5px;
    padding: 0 25px 20px;
    display: block;
    width: 100% !important;
    flex: none;
    max-width: 100%;
}

.franchise-usa-main div#filter #filters .item.item-1{
    border-radius: 5px 5px 0 0;
    padding-top: 20px!important;
}

.franchise-usa-main #lower{
    background: #dedede;
    padding: 2rem 0 !important;
}

.franchise-usa-main .white-section {
    padding: 20px;
    background: #fff;
}

.franchise-usa-main .white-section h2 {
    font-size: 1.75rem;
    padding: 0 0 20px;
}

.franchise-usa-main .white-section ul {
    margin: 0;
    border-top: 2px dashed #dedede;
    padding-top: 20px;
}

.franchise-usa-main .white-section li,
.franchise-by-investment li {
    line-height: 1.5;
    display: inline-block;
    width: 100%;
}

.franchise-usa-main .explore-btn{
    margin-top: 2rem;
}

.franchise-usa-main .explore-btn .button.explore,
.fbi-btn.explore-btn .button.explore {
    background: #cc1209;
    color: #fff;
    font-weight: 400;
    padding: 15px 20px;
    width: auto;
    display: table;
    border-radius: 0;
    font-size: 14px;
    cursor: pointer;
    text-transform: uppercase;
    margin: 0 auto;
    border: none;
    max-width: 100%;
    text-decoration: none !important;
    margin-bottom: 0;
    text-align: center;
    line-height: 1.1;
}

.franchise-usa-main .white-section li,
.franchise-by-investment li {
    width: 49%;
}

/* Find a franchise */
.find-a-franchise div#filter #filters #fafh1{
    font-size: 2.25rem !important;
    font-weight: 700;
}

.find-a-franchise div#filter button{
    font-size: 1.125rem;
    line-height: 1;
}

.find-a-franchise-content img{
    width: 100%;
    max-width: 150px;
    vertical-align: middle;
}

.faf-quote-block{
    padding-bottom: 2rem;
}

.faf-quote-block .faf-quote{
    font-size: 1.25rem;
}

.faf-quote-block .faf-quote-sig{
    font-size: 0.875rem;
    font-style: italic;
}

.find-a-franchise-content > div{
    max-width: 900px;
    margin: auto;
}

@media only screen and (max-width: 767px) {
    .franchise-usa-main div#filter #filters .item{
        padding: 10px 15px 0!important;
        background: none;
    }

    .franchise-usa-main div#filter #filters #fafh1,
    .franchise-usa-main div#filter #filters .item{
        margin: 0;
    }

    .franchise-usa-main div#filter #filters #fafh1 h1,
    .franchise-usa-main .white-section h2{
        font-size: 1.5rem !important;
    }

    .franchise-usa-main .white-section{
        padding: 10px;
    }
}


/* Franchise by investment */
.fbi-container{
    border: none !important;
    margin-bottom: 0 !important;
}

.franchise-by-investment{
    padding: 20px;
    background: #fff;
}

.franchise-by-investment ul{
    margin: 0 !important;
    border-top: 2px dashed #dedede;
    padding-top: 20px;
}


/* Franchise how to guide */

#base.fran-how-to-base{
    padding-top: 1rem;
}

#base #fran-how-to .fht-row{
    display: flex;
    flex-wrap: wrap;
    font-size: 1.1rem;
}

#base #fran-how-to .fht-row .fht-item1{
    flex: 0 1 66.66%;
    padding: 12px;
}

#base #fran-how-to .fht-row .fht-item2{
    flex: 0 1 33.33%;
    padding: 12px;
}

.fht-item1 .positionRel {
    position: relative;
    overflow: hidden;
}

.fht-item1 .positionRel .orangearial{
    -ms-transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    width: 140px;
    padding: 0;
    margin: 0;
    text-transform: uppercase;
    font-family: 'Montserrat', sans-serif;
    background: #f60;
    color: #FFF !important;
    text-align: center !important;
    left: -35px;
    position: absolute;
    z-index: 1;
    top: 23px; 
    font-size: 1rem;
}

.fht-item1 .positionRel img {
    padding: 2rem 60px 2rem;
    margin: 0 auto;
    display: table;
    max-width: 100%;
    height: auto;
}

.fht-item1 .positionRel p,
.fht-item1 .positionRel li{
    font-size: 1rem;
}

.fht-item1 .positionRel .blackarialboldheader{
    line-height: 1.2;
    margin: 1rem 0 .5rem;
    font-weight: 700 !important;
    font-family: 'Montserrat', sans-serif;
    font-size: 1.25rem;
}

.fht-item1 .bluearial {
    margin-bottom: 1rem;
    border-bottom: 2px dashed #dedede;
    padding-bottom: 1rem;
}

.fht-item1 .includelist{
    list-style-type: disc !important;
}

.fht-item2 .displayedimg {
    display: table;
    margin: 0 auto 1rem;
}

.fht-item2 .emailform {
    background: #dedede;
    padding: 15px;
    margin-bottom: 1rem;
}

.fht-item2 .emailform input[type="text"], .fht-item2 .emailform input[type="email"] {
    height: 45px;
    width: 100%;
    padding-left: 10px;
    font-size: 12px;
    margin: 0 0 25px;
    border: 2px solid #FFF;
    background-color: #FFF;
}

.fht-item2 .emailform label {
    display: none;
}

.fht-item2 .emailform .changebutton{
    text-align: center;
}

.fht-item2 .emailform .changebutton button{
    color: #FFF;
    font-weight: 200;
    padding: 10px;
    width: 100%;
    display: table;
    border-radius: 0;
    font-size: 14px;
    cursor: pointer;
    text-transform: uppercase;
    margin: 0;
    border: none;
    height: 40px;
    background: #cc1209;
}

.fht-item2 small{
    font-size: 0.85rem;
    text-align: center;
    display: block;
}

@media only screen and (max-width: 767px) {
    #base #fran-how-to .fht-row{
        display: block;
    }
}



/* Home Page */
/* Banner */

#home #bannerWrap{
    background-color: rgba(114,116,121);
    /* background-color: rgba(114,116,121,.6); */
    background-blend-mode: darken;
    min-height: 250px !important;
    /* padding: 3rem; */
    padding: 2rem 1rem;
    width: 100%;
    /* background-image: url(https://franchise-ventures-general.s3.amazonaws.com/cdn_fcom/images/banner.jpg); */
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
}

#home #bannerWrap .bannerTextBlock{
    display: flex;
    justify-content: center;
    padding-top: 5px;
}

#home .home-filter-block{
    padding: 0 3px;
}

#home #bannerWrap .h1wrap{
    color: white;
    font-weight: 400;
    text-shadow: 0 0 7.5px rgb(0 0 0 / 75%);
    margin-top: 1rem;
    margin-bottom: 1rem;
    line-height: 1.1;
    text-align: center;
    font-size: 2.5rem;
    max-width: 66.66%;
}

#home #bannerWrap .h1wrap h1{
    font-weight: 400;
    font-size: 2.5rem;
}

#home #top-franchise, #franCat{
    padding: .5rem 0;
}

#home #franCat{
    background-color: #dedede;
}

#home #top-franchise .tf-row{
    display: flex;
    justify-content: center;
    /* padding: 30px 15px; */
    padding: 10px 15px 0;
}

#home #results .result-item{
    /* padding: 15px 10px; */
    padding: 15px 11.3px 0;
}

#home #top-franchise 
.tf-row .tf-item{
    flex: 0 0 16.666667%;
    padding: 0 10px;
}

#home #top-franchise 
.tf-row .tf-item:last-child{
    text-align: end;
}

#home #top-franchise 
.tf-row .tf-item img{
    height: 100px;
    width: auto;
}

#home #top-franchise 
.tf-row .tf-item-8{
    flex: 0 0 66.666667%;
    text-align: center;
}

#home #top-franchise 
.tf-row .tf-item-8 h2{
    font-size: 2.25rem;
    color: #324972 !important;
    text-align: center;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1;
}

#home #top-franchise 
.tf-row .tf-item-8 a img{
    height: 62px;
    width: 122px;
    max-width: 31%;
    margin: 3px;
    background: #fff;
    position: relative;
    border: 1px solid #c0c0c0;
}

.filter-backdrop{
    height:100px;
    width:100%;
    background: rgba(61, 68, 84, .8);
}

@media only screen and (max-width:992px) {
    .filter-backdrop{
        height:155px;
        width:100%;
    }
}

@media only screen and (max-width:767px) {
    .filter-backdrop{
        height:225px;
        width:100%;
    }
}

/* Franchise Categories */

#home #franCat #franCatDiv{
    padding: 30px 15px;
}

#home #franCat #franCatDiv h2,
#home-news h2{
    text-align: center;
    color: #324972 !important;
    font-size: 2.25rem;
    font-weight: 700;
} 

#home #franCat #franCatDiv h2{
    margin-bottom: 1rem;
    line-height: 1;
}

#home #franCat #franCatDiv .franTabs{
    display: flex;
    margin-bottom: 10px;
}

#home #franCat #franCatDiv 
.franTabs .franTabItem {
    flex: 0 0 25%;
    padding-left: 5px;
}

#home #franCat #franCatDiv 
.franTabs .franTabItem a{
    font-size: 1rem;
    display: block;
    text-align: center;
}

#home-news a{
    text-decoration: underline;
}

@media only screen and (max-width : 767px) {

    #home #franCat #franCatDiv 
    .franTabs .franTabItem{
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 10px;
    }

}

a.green-button{
    background: var(--green);
    color: #FFF;
    text-transform: uppercase;
    border-radius: 6px;
    font-weight: 400;
    line-height: 30px;
    text-align: center;
    padding: 5px;
    text-decoration: none;
    max-width: 400px;
}

#franCatDiv #industry-list{
    display: flex;
    margin-top: .5rem;
}

#franCatDiv #industry-list 
.industry-list-item{
    flex: 0 0 33%;
    padding: 0 20px;
}

#franCatDiv #industry-list 
.industry-list-item .iconUl{
    list-style: none;
}

.industry-list-item .iconUl li{
    border-bottom: 1px solid #c0c0c0;
    min-height: 25px;
    margin-bottom: 5px;
    list-style: none;
    font-size: 1rem;
    padding: 2px 0;
    font-family: sans-serif;
}

.industry-list-item .iconUl li i{
    width: 20px;
    text-align: center;
    margin-right: 10px;
    color: #324972;
}

.industry-list-item .iconUl li a{
    color: #083CA6;
    text-decoration: none;
}

#viewMoreBtn{
    margin: 10px auto;
    transition: all 0.2s ease;
}

@media (min-width: 768px){
    #viewMoreBtn {
        display: none;
    }

    #home #top-franchise 
    .tf-row .tf-item .tf-item-text {
        display: none;
    }
}

@media only screen and (max-width : 767px) {

    #home #franCat #franCatDiv{
        padding: 30px 0;
    }
    
    #home #franCat #franCatDiv 
    .franTabs .franTabItem a {
        font-size: 0.8rem;
        width: 100%;
    }

    #home #top-franchise .tf-row .tf-item-8 h2,
    #home #franCat #franCatDiv h2{
        font-size: 1.6rem;
    }

    #home #top-franchise #results{
        display: block;
    }

    #industry-list .industry-list-item{
        padding: 0 10px;
    }

    #industry-list .industry-list-item .iconUl li{
        font-size: 100%;
    }

    #home #bannerWrap{
        padding: 2rem 1rem;
    }

    #home #bannerWrap .h1wrap,
    #home #bannerWrap .h1wrap h1{
        font-size: 1.9rem;
        max-width: 100%;
    }

    #home #top-franchise .tf-row .tf-item img{
        height: auto;
    }

    #viewMoreBtn{
        display: block !important;
        font-size: 0.75rem;
    }
}

@media only screen and (max-width : 767px) {
    #home #top-franchise .tf-row,
    #franCatDiv #industry-list {
        display: block;
    }

    #home #franCat #franCatDiv{
        padding: 20px 0;
    }

    #home #franCat #franCatDiv > p{
        padding: 0 10px;
    }

    #franCatDiv #industry-list .industry-list-item,
    #home #franCat #franCatDiv .franTabs .franTabItem:nth-child(odd){
        padding: 0;
    }

    #home #bannerWrap{
        padding: 1rem;
        padding-top: 0;
    }

    #home #bannerWrap .container{
        padding: 0;
    }

    #home #bannerWrap .h1wrap,
    #home #bannerWrap .h1wrap h1{
        font-size: 1.2rem;
        max-width: 100%;
    }

    #home #bannerWrap #home-filter {
        max-width: 100%;
        width: 100%;
        padding: 5px;
    }

    #home #bannerWrap .bannerTextBlock{
        padding: 0;
        padding-top: 10px;
    }

    #home #top-franchise .tf-row .tf-item-8 h2,
    #home #franCat #franCatDiv h2,
    #home #top-franchise .tf-row .tf-item .tf-item-text h2{
        font-size: 1.125rem;
        color: #324972 !important;
        line-height: 1;
    }

    #home #top-franchise .tf-row{
        padding: 20px 10px 0;
    }

    #home #top-franchise .tf-row .tf-item img{
        height: 71px;
    }

    #home #top-franchise .tf-row .tf-item{
        text-align: center !important;
    }

    #home #top-franchise .tf-row .tf-item:last-child{
        padding-top: 24px;
    }

    #home #franCat #franCatDiv 
    .franTabs{
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 0;
    }

    #home #franCat #franCatDiv 
    .franTabs .franTabItem{
        flex: 0 0 50%;
    }

    #home #franCat .container{
        padding: 0 15px;
    }

    #home #franCat #franCatDiv .franTabs .franTabItem a{
        font-size: 0.7rem;
    }
}

@media only screen and (max-width: 320px) {
    #home #top-franchise .tf-row .tf-item:last-child{
        padding: 24px 0 0;
    }
}

#home-content{
    padding-top: 2.5rem;
}

#home-content .home-content-text p,
#home-content .home-content-text ul li{
    font-size: 1rem;
}

#home-content .home-content-text p>span{
    color:#324972;
    font-weight:bold;
}

#home-content .home-content-text ul{
    list-style:disc;
    padding-left:15px;
}

#home-content .home-content-text ul li>span{
    font-weight:bold
}

@media only screen and (max-width:767px) {
    #home-content{
        padding: 10px 15px 0;
    }

    #home-content .container{
        padding: 20px 10px;
    }
}

/* News & Info Section */

#home-news{
    padding: 0.5rem 0 1.5rem;
}

#home-news h2{
    padding: 15px 0;
}

#home-news .news-info-row{
    display: flex;
    flex-wrap: wrap;
}

#home-news .news-info-row .pd-2{
    flex: 0 0 31.333333%;
    margin: 2% 1% 0;
}
 
#home-news .news-info-row 
.news-info-item{
    border: 5px solid #dedede;
    background: #fff;
    height: 260px;
    position: relative;
    transition: transform .2s;
}

.news-info-item .news-article{
    padding: 10px;
}

#home-news .news-info-row 
.news-info-item:hover{
    transform: scale(1.07);
    box-shadow: 2px 2px 12px rgba(156, 156, 156, 0.75);
}

#home-news .news-info-row 
.news-info-item a{
    text-decoration: none;
    display: block;
}

#home-news .news-info-row 
.news-info-item a::before{
    content: "";
    background: #2d456f;
    background-image: url('data:image/svg+xml;charset=utf8,%3C?xml version="1.0" encoding="utf-8"?%3E%3Csvg width="16" height="16" viewBox="0 0 1300 1800" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath fill="white" d="M1363 877l-742 742q-19 19-45 19t-45-19l-166-166q-19-19-19-45t19-45l531-531-531-531q-19-19-19-45t19-45l166-166q19-19 45-19t45 19l742 742q19 19 19 45t-19 45z"/%3E%3C/svg%3E');
    background-position: center;
    background-repeat: no-repeat;
    font-size: 16px;
    position: absolute;
    top: calc(50% - 15px);
    right: -15px;
    height: 30px;
    width: 30px;
    border-radius: 50%;
    line-height: 30px;
    text-align: center;
    padding: 2px;
}

#home-news .news-info-row 
.news-info-item strong{
    color: #acacac;
    text-transform: uppercase;
    font-family: 'Montserrat',sans-serif;
    font-weight: 500;
}

#home-news .news-info-row 
.news-info-item h3{
    color: #333;
    line-height: 1.2;
    font-weight: 600;
    font-size: 1rem;
}

#home-news .news-info-row 
.news-info-item p{
    line-height: 2;
    color: #333;
    margin-bottom: 1rem;
    font-size: 1rem;
}

#home-news .news-info-row 
.news-info-item em{
    font-family: 'Montserrat',sans-serif;
    font-weight: 700;
    font-style: normal;
}


#home-news .news-info-row 
.news-info-item em.text-uppercase{
    text-transform: uppercase;
}

#home-news .news-info-row 
.news-info-item .news-info-date{
    background: #dedede;
    color: #000;
    /* padding: 15px; */
    padding: 15px 10px 10px;
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
}

@media only screen and (max-width : 767px) {
    #home-news .news-info-row .news-info-item{
        height: 100%;
    }

    #home-news .news-info-row 
    .news-info-item .news-info-date span{
        font-size: 0.8rem;
    }

    #home-news .news-info-row .news-info-item p{
        font-size: 0.9rem;
    }
}

@media only screen and (max-width : 767px) {
    #home-news .news-info-row .pd-2{
        flex: 0 0 100%;
        padding: 0;
        width: 100%;
        margin: 3px 0;
    }

    #home-news .news-info-row{
        padding: 0 3px;
    }

    #home-news .news-info-row 
    .news-info-item{
        border: 1px solid #2d456f;
        border-radius: 4px;
        padding: .5rem 40px .5rem .5rem;
        height: auto;
    }

    .news-info-item .news-article{
        padding: 0;
    }

    #home-news .news-info-row .news-info-item strong,
    #home-news .news-info-row .news-info-item p,
    #home-news .news-info-row .news-info-item 
    .news-info-date{
        display: none;
    }

    #home-news .news-info-row .news-info-item h3{
        font-size: 1rem;
        font-weight: 400;
    }

    #home-news .news-info-row .news-info-item a::before{
        right: -10px;
        height: 30px;
        width: 30px;
    }

}

/* Home Filter Section */

#home-filter{
    /* padding: 20px; */
    padding: 20px 5px;
    position: relative;
    max-width: 95%;
    margin: auto;
}

#home-filter::before{
    border-radius: 10px;
    mix-blend-mode: hard-light;
    background: rgba(61,68,84,.8);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
}

#home-filter #home-filters{
    padding: 10px 0 10px;
    display: flex;
    justify-content: center;
}

#home-filter #home-filters .home-filter-item{
    flex: 0 0 25%;
    /* padding: 0 15px; */
    padding: 0 12.5px;
}

#home-filter #home-filters select{
    color: black;
    border-radius: 6px;
    font-size: 1rem;
    /* height: 45px; */
    height: 40px;
    border: 1px solid #90a0c6;
    background-image: url('https://dgsgass4s0wnj.cloudfront.net/image/select.png');
    padding: 0 10px;
    box-sizing: border-box;
    width: 100%;
    appearance: none;
    -webkit-appearance: none;
    background-position: right 10px center;
    background-color: #fff;
    background-repeat: no-repeat;
    transform-style: preserve-3d;
}

#home-filter #home-filters button{
    background: var(--green);
    text-transform: none;
    font-size: 1.125rem;
    border-radius: 6px;
    font-weight: 400;
    white-space: nowrap;
    color: #fff;
    width: 100%;
    border: none;
    /* height: 45px; */
    height: 40px;
    transform-style: preserve-3d;
}

@media only screen and (max-width : 767px) {
    #home-filter{
        max-width: 100%;
        padding: 10px;
    }

    #home-filter #home-filters{
        /* padding: 10px; */
        padding: 15px 0 10px;
    }

    #home-filter #home-filters 
    .home-filter-item{
        /* padding: 10px; */
        padding: 0 12.5px;
    }

    #home-filter #home-filters select,
    #home-filter #home-filters button{
        height: 40px;
        font-size: 18px;
    }

    #home .home-filter-block{
        padding: 0;
    }
}

@media only screen and (max-width : 767px) {
    
    #home-filter #home-filters {
        padding: 10px 0 5px;
        display: block;
    }

    #home-filter #home-filters 
    .home-filter-item{
        padding: 5px 0;
    }
}

/* Profile Secction*/

#profileBanner{
    background-position: center;
    background-size: cover;
    padding: 20px;
    background-repeat: no-repeat;
    width: 100%;
    background-color: #efefef;
}

#profile .container{
    max-width: 1190px;
}

#profile .pr-cont{
    display: flex;
    width: 100%;
    justify-content: center;
}

#profileBanner .profile-container {
    border-radius: 10px;
    background: rgba(255,255,255,.95);
    padding: 30px;
    position: relative;
    max-width: 1000px;
    width: 100%;
}

#profileBanner .profile-container .profile-row{
    text-align: center;
}

#profileBanner .profile-container .profile-row .profileImage{
    display: block;
    margin: 0 auto 1rem;
    max-height: 125px;
}

.profile-row .profile-heading{
    font-size: 2rem;
}

.profile-row .profile-h2{
    font-size: 1.5rem;
}

.profile-row .profile-heading,
.profile-row .profile-h2 {
    margin-top: 1rem;
}
.profile-row h1.profile-heading:after{
    content: attr(data-after);
}
.profile-row #pr-add-cart{
    display: none;
}

.profile-row #pr-add-cart button{
    margin: 30px auto 0;
}

.profile-row .profile-h2 {
    margin: 25px 0 10px;
    border-top: 2px dashed #dedede;
    padding-top: 15px;
}

.profile-row p{
    margin: 1rem 0 2rem;
}

.profile-row .profile-p p{
    text-align: left;
}

.profile-row .profileDetails{
    position: relative;
    font-family: 'Montserrat',sans-serif;
    text-align: center;
    font-weight: 600;
    background: #dedede;
    padding: 10px 0 10px;
    margin: 0 0 1rem !important;
    display: flex;
    flex-wrap: wrap;
}

.profile-row .profileDetails .profile-dtl-item{
    flex: 0 0 25%;
}

.profile-row .profileDetails 
.profile-dtl-item span {
    font-family: 'Roboto',sans-serif;
    display: block;
    font-weight: 400;
}

.profile-row .result-checkbox{
    /* width: 30%; */
    max-width: 300px;
    width: 100%;
    margin: 30px auto 0;
}

/* .profile-intro-btn{
    padding-top: 3rem;
} */

.profile-intro-btn .result-checkbox{
    /* width: 25%; */
    max-width: 300px;
    width: 100%;
    margin: 0 auto;
}
.result-checkbox label {
    display: none;
}
.profile-row .result-checkbox button,
.profile-intro-btn .result-checkbox button{
    cursor: pointer;
    padding: 15px;
}

.profile-row .result-checkbox input,
.profile-intro-btn .result-checkbox input{
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    opacity: 0;
    cursor: pointer;
}

@media (max-width: 767px){
    #profileBanner .profile-container{
        max-width: 100%;
    }

    .profile-row .profileDetails .profile-dtl-item{
        flex: 0 0 50%;
    }

    .profile-row .profileDetails .profile-dtl-item:nth-child(3),
    .profile-row .profileDetails .profile-dtl-item:nth-child(4){
        padding-top: 20px;
    }

    .profile-row .result-checkbox,
    .profile-intro-btn .result-checkbox,
    #profile-about .pr-abt-btn .result-checkbox{
        width: 50%;
    }
}

@media (max-width: 767px){
    #profileBanner {
        background: #efefef !important;
    }

    #profileBanner .container{
        padding: 0;
    }

    .profile-row .profileDetails{
        display: block;
    }

    .profile-row .profileDetails 
    .profile-dtl-item{
        padding-top: 20px;
    }

    .profile-row .result-checkbox{
        width: 100%;
    }

    /* #filter{
        display: none;
    } */

    #filters .col-12{
        /* margin: 5px 0; */
        margin: 0 0 10px;
    }

    #filters .col-12:last-child{
        margin: 0;
    }

    /* .profile-body #filter{
        display: none;
    } */
}

/* Profile Intro Section */

/* #profile-breadcrums{
    padding-bottom: 2rem;
} */

#profile-breadcrums li a span{
    margin-left: 0;
}

#profile-intro{
    margin: 1rem 0 2rem;
}

.splide__slide img,
.splide__slide iframe {
	width : 100%;
	height: 350px;
}

.splide--draggable>.splide__track>.splide__list>.splide__slide{
    width: 472px;
}

.splide__pagination__page.is-active{
    background: #ccc!important;
}

.splide__pagination{
    bottom: -20px;
}

#profile-intro-row {
    display: flex;
    /* flex-wrap: wrap; */
}

#profile-intro-row.container,
#profile-intro .profile-locations{
    padding: 0;
    margin-bottom: 2rem;
}

#profile-intro-row .profile-intro-item1{
    flex: 0 0 41.66%;
    /* width: 40%; */
    /* padding: 10px; */
    padding: 20px 12px;
}

#profile-intro-row .profile-intro-item2{
    flex: 0 0 58.33%;
    /* width: 60%; */
    padding: 0 10px;
}

#image-slider .splide__track{
    width: 472px;
}

@media (min-width: 992px) {
    #profile-intro-row .profile-intro-item2 {
        order: 1!important;
    }
}

@media only screen and (max-width:768px) {
    
}

@media only screen and (max-width:767px) {

    #image-slider{
        padding: 0 12px;
    }

    #image-slider .splide__track{
        width: auto;
    }

    .splide--draggable>.splide__track>.splide__list>.splide__slide{
        width: 402px;
    }

    .splide__slide img, .splide__slide iframe{
        height: 300px;
    }

}

#profile-intro-row .profile-intro-item2 
.profileIntroDescription {
    padding: 20px 10px;
}

#profile-intro-row .profile-intro-item2 
.profileIntroDescription h2{
    font-size: 1.5rem;
    /* margin: 25px 0 10px;
    border-top: 2px dashed #dedede; */
    margin: 10px 0 10px;
    padding-top: 15px;
}
#profile-about .profile-abt-row h2:not(.profileSub) {
    font-size: 24px;
    margin: 10px 0 10px;
    /* border-top: 2px dashed #dedede; */
    /* padding-top: 15px; */
}
a.companyLink {
    display: block;
    clear: both;
    /* margin-top: 60px; */
    text-align: center;
    color: var(--blue);
    text-decoration: none;
}

#profile #image-slider{
    margin-bottom: 60px;
}

#profile-intro-row .profile-intro-item1 .profileSocial {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 40px;
    align-items: center;
    border-top: 5px solid #dedede;
    padding-top: 21px;
}

#profile-intro-row .profile-intro-item1 .profileSocial>a {
    padding: 0;
    width: 2.5rem;
    margin-left: 0.25rem;
    text-align: center;
    display: inline-flex;
}
#profile-intro-row .profile-intro-item1 .profileSocial>a span{
    width: 2.5rem;
}

#profile-intro-row .profile-intro-item1 .socialIcon {
    color: #324972;
}

#profile-intro .profile-locations{
    padding: 1.9rem 1rem;
    background: #efefef;
    /* margin-top: 3rem;
    /* word-break: break-all; */
}

#profile-intro .splide__slide{
    display: flex;
    justify-content: center;
    align-items: center;
}

#profile-intro .splide__slide img{
    max-width : 100%;
	max-height: 350px;
    width: auto;
    height: auto;
}

@media (max-width: 767px){
    #profile-intro-row{
        display: block;
    }

    #profile-intro-row .profile-intro-item1, 
    #profile-intro-row .profile-intro-item2{
        width: 100%;
    }

    #profile-intro-row .profile-intro-item2 .profileIntroDescription{
        padding-top: 0;
    }

    #profile-intro-row .profile-intro-item2 .profileIntroDescription h2{
        margin-top: 0;
    }
}

/* Profile Video Section */
.profile-video{
    position: relative; 
    width: 100%;
}

.profile-video .play-arrow{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    background: #ccc;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: .7;
    cursor: pointer;
}

.profile-video .play-arrow::after{
    content: "";
    display: inline-block;
    border-color: transparent transparent transparent #000;
    border-style: solid;
    border-width: 9px 0 9px 17px;
    margin-left: 4px;
}


/* Profile About Section */

#profile-about{
    background-color: #dedede;
    padding: 30px 0;
    width: 100%;
}

#profile-about .profile-abt-row{
    background: #fff;
    padding: 0 20px 20px;
    margin: 0 5px;
}

#profile-about .profile-abt-row ol{
    list-style-type: auto;
    padding: 0 0 0 20px;
}

#profile-about .profile-abt-row h2{
    font-size: 1.5rem;
}

#profile-about .profile-abt-row .profileSub {
    /* font-size: 2rem; */
    padding: 20px 0 0;
    margin-bottom: 14px;
    line-height: 1.2;
}

#profile-about .profile-abt-row .profileSub-1 {
    font-size: 1.5rem;
    margin: 25px 0 10px;
    /* border-top: 2px dashed #dedede; */
    padding-top: 15px;
}

#profile-about .profile-abt-row ul{
    list-style-type: square;
    margin: 0 0 1rem;
    padding-left: 20px;
}

#profile-about .profileSub.top {
    border-top: 5px solid #dedede;
    margin-top: 30px;
    margin-bottom: 0.5rem;
    font-size: 2rem;
    padding: 20px 0 10px;
    border-bottom: none;
}

#profile-about .pr-abt-btn {
    padding-top:0;
}

#profile-about .pr-abt-btn .result-checkbox{
    margin: 1rem auto 0;
}

#profile-about :is(h2,h3,h4,h5,h6){
    font-weight: bold;
    margin-bottom: 0.5rem;
}

#profile-about h2{
    font-size: 1.5rem;
}

#profile-about :is(h3,h4,h5,h6), .profileIntroDescription :is(h3,h4,h5,h6){
    font-size: 1rem !important; 
    margin-bottom: 0.5rem;
}

#profile-about table {
    max-width: 633px;
    width: 100%;
    margin: 1rem auto;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 5px;
}

#profile-about tbody {
    border: 1px solid #ccc;
}

#profile-about tbody tr:nth-child(odd) {
    background-color: #E7E9EB;
}

#profile-about tbody tr:nth-child(even) {
    background-color: #fff;
}

#profile-about tbody tr:first-child {
    background-color: #1a1a1a !important;
    color: #ffffff;
    font-weight: bold;
}

#profile-about tbody tr {
    border-bottom: 1px solid #ddd;
}

#profile-about table td, #profile-about table th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
    line-height: 1;
    font-size: 0.85rem !important;
}

@media (max-width: 767px){
    .profile-intro-btn .result-checkbox{
        width: 100%;
        margin: 20px auto;
        max-width: 300px;
        /* margin: 1rem auto 0; */
    }
}

/* Franchise Categories All */
#franchise-categories-all{
    /* padding: 2rem 0; */
    padding-bottom: 1.5rem;
}

.categories-row,.categories-row .categories-outer-item {
    display: flex;
}

.categories-row .categories-outer-item 
.fca-item{
    flex: 0 0 25%;
}

.categories-row .categories-outer-item 
.fca-item .item{
    padding: 10px;
}

.categories-outer-item .fca-item 
.item .stacked-column{
    border: 2px solid #efefef;
    background: #fff;
    box-sizing: border-box;
    padding: 0.6rem;
}


.franchise-float-tile-header{
    border-bottom: 2px solid #efefef;
    /* padding: 5px; */
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.franchise-float-tile-header a:first-child{
    padding: 0 5px;
    font-family: 'Montserrat', sans-serif;
    color: #000 !important;
    font-weight: bold;
    text-decoration: none;
}

.stacked-column-tile-blurb{
    padding: 10px 0 0;
}

.stacked-column-tile-blurb a{
    position: relative;
}

.stacked-column-tile-blurb a::before {
    content: '\00bb';
    display: inline-block;
    color: #949494;
    font-style: normal !important;
    font-size: 14px;
}

@media only screen and (max-width : 767px) {

    .categories-row .categories-outer-item {
        flex-wrap: wrap;
    }

    .categories-row .categories-outer-item 
    .fca-item{
        flex: 0 0 50%;
    }
}

@media only screen and (max-width : 600px) {
    .categories-row .categories-outer-item{
        display: block;
    }
}



/* Sitemap Section */

ul.sitemap, ul.sitemap ul {
    margin: 0;
    padding: 0;
    background: url(https://franchise-ventures-general.s3.amazonaws.com/cdn_fcom/images/left.png) repeat-y left center;
}

ul.sitemap a, ul.sitemap span, 
ul.sitemap a:visited{
    color: #333 !important;
    background-color: #dedede;
    font-size: 1rem;
    transition: .2s;
    margin: 0;
    padding: 0 1em;
    display: inline-block;
    font-weight: bold;
    text-align: left;
    line-height: 1.6rem;
    text-decoration: none;
    max-width: 100%;
    word-break: break-word;
    border: none;
    cursor: pointer;
    font-family: 'Montserrat', sans-serif;
}

ul.sitemap a::after{
    content: attr(href);
    display: inline-block;
    margin-left: 1em;
    font-size: 90% !important;
    word-wrap: break-word;
    font-weight: normal;
    font-family: 'Roboto', sans-serif;
}

ul.sitemap #home a{
    background-color: #324972;
    color: #FFF !important;
}

ul.sitemap li:not(#home){
    background: url(https://franchise-ventures-general.s3.amazonaws.com/cdn_fcom/images/center.png) no-repeat left top;
    margin: 0.5em 0;
    padding: 0 0 0 30px;
}

body ul.sitemap :is(li:last-child,li.last):not(#home) {
    background-color: #fff;
    background-image: url(https://franchise-ventures-general.s3.amazonaws.com/cdn_fcom/images/corner.png);
    background-repeat: no-repeat;
    background-position: left top -87px;
}

ul.sitemap li ul a{
    background-color: #f7f7f7;
}

ul.sitemap li li ul{
    display: none;
}

ul.sitemap li ul li a, 
ul.sitemap li ul li a:visited{
    background-color: #FFF;
    border-color: #FFF;
    color: #000;
}

ul.sitemap span.toggle {
    border-radius: 5px;
    width: 26px;
    padding: 0;
    text-align: center;
}

.toggle:before {
    content: "+";
}

ul.sitemap a:hover{
    background: #cc1209 !important;
    color: #fff !important;
}

@media only screen and (max-width : 767px) {
    ul.sitemap a, ul.sitemap span, 
    ul.sitemap a:visited{
        font-size: 0.85rem;
    }

    ul.sitemap a::after{
        content: '';
    }
}

/* State City Page*/
.state-city{
    margin-bottom: 2rem;
}

.state-city .state-city-row{
    display: flex;
    flex-wrap: wrap;
}

.state-city .state-city-row 
.state-city-item{
    flex: 0 0 25%;
    margin: 10px 0;
}

.state-city .state-city-row 
.state-city-item a{
    color: var(--blue);
    text-decoration: none;
    font-size: 1rem;
}

.state-city-item a:hover{
    color: #cc1209 !important;
}

@media only screen and (max-width: 767px){
    .state-city .state-city-row 
    .state-city-item{
        flex: 0 0 33.33%;
    }

}

@media only screen and (max-width: 767px) {
    .state-city .state-city-row{
        display: block;
    }
}

/* Error Page */

#base #error{
    margin: 2.5rem 0;
}

#base #error .error-row{
    display: flex;
}

#base #error .error-row .error-item1{
    flex: 0 0 33.33%;
}

#base #error .error-row .error-item2{
    flex: 0 0 66.66%;
}

#base #error .error-row .error-item1,
#base #error .error-row .error-item2{
    padding: 10px;
}

#base #error .error-row .error-item1 img{
    width: 100%;
}

.error-row .error-item2 h2{
    color: #000;
    margin: 0 0 1rem;
    font-size: 1.5rem;
}

.error-row .error-item2 h3{
    color: #000;
    margin: .5rem 0;
    font-size: 1rem;
}

@media only screen and (max-width : 600px) {
    #base #error .error-row{
        display: block;
    }
}

/* Subscribed */
#contact-form{
    display: flex;
    justify-content: center;
}

#contact-form form{
    text-align: center;
    max-width: 70%;
    border: 1px solid #c0c0c0;
    border-radius: 5px;
    padding: 30px;
    margin-bottom: 2rem;
}

#contact-form .form-email{
    width: 100%;
    position: relative;
}

#contact-form .form-email label{
    background-color: white;
    padding: 0 10px;
    top: -8px;
    left: 30px;
    font-size: 11px;
    position: absolute;
    display: inline-block;
    margin-bottom: .5rem;
}

#contact-form .form-email input{
    border-radius: 5px;
    border: 1px solid #c0c0c0;
    font-size: 1rem;
    height: 45px;
    width: 100%;
    padding-left: 10px;
    margin: 0 0 25px;
}

#contact-form button{
    font-weight: 400;
    border-radius: 5px;
    color: #FFF;
    min-height: 40px;
    width: 80%;
    font-size: 1rem;
    padding: 5px 15px 5px;
    line-height: 1;
    margin: 20px 0;
    background: #cc1209;
    text-align: center;
    border: none;
}

#contact-form .newsletter_disclaimer{
    display: block;
}

@media only screen and (max-width : 767px) {
    #contact-form form{
        max-width: 100%;
    }
}

/* OTHER */
#phone_tip {
    position: fixed;
    z-index: 999999;
    background: #8dc63f;
    color:#FFF;
    width: 12.5rem;
    max-width: calc(100% - 2.5rem);
    font-size: .875rem;
    padding: .625rem;
    visibility: hidden;
    top:calc(50% - 6.25rem);
    left:calc(50% - 6.25rem);
    border-radius: .938rem .313rem 0;
}
#phone_tip:after {
	top: 100%;
	right: 0;
	content: " ";
	position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 .625rem .625rem 0;
    border-color: transparent #8dc63f transparent transparent;
}
#phone_tip.tipped{
    visibility: visible;
}
small.right {
    text-align: right;
    display: block;
}

/* ACCEPTS COOKIES */
#valid_cookies {
    background: rgba(0,0,0,.8);
    display: none;
    position: fixed;
    bottom: 0;
    left: 0; 
    right: 0;
    z-index: 99999;
    color: #FFF;
    -webkit-backdrop-filter: blur(.625rem);
    backdrop-filter: blur(.625rem);
}
#valid_cookies.show {
    display: block;
}
#valid_cookies .flexxer {
    max-width: 90%;
    width: 56.25rem;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    align-items: center;
}
#valid_cookies .flexxer p {
    padding-right: .938rem;
    margin: 1.25rem 0;
    font-size: 1rem;
}
#valid_cookies .flexxer p a {
    color: #eee;
    text-decoration: underline;
}
#valid_cookies button.closer {
font-weight: bold;
    text-transform: uppercase;
    padding: .625rem .938rem;
    line-height: 1;
    background-color: rgb(29, 161, 242);
    color: #FFF;
    border: none;
    display: block;
    width: 100%;
    font-size: 1rem;
    cursor: pointer;
    height: 2.125rem;
}

/* FILTER */
div#filter, section#toc-slider {
    background: #404859;
    padding: 5px;
    color:#FFF;
}
div#filter .container, section#toc-slider .container {
    background-color: #3d4454;
    border-radius: 5px;
    text-align: center;
    padding-right: var(--bs-gutter-x,.75rem);
    padding-left: var(--bs-gutter-x,.75rem);
    border-radius: 5px;
}
#filters label {
    display: none;
}

#filter_toggle,#toc-toggle {
    font-size: 24px;
    padding: 10px 15px;
    text-align: left;
    line-height: 1;
    color: #fff;
    background-color: #383f4d;
    border-radius: 0;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px 5px 0 0;
    margin: 0 -15px;
}
#filter_form.closed #filters,#toc-slider.closed #toc-filters {
    display:none !important;
}
#filter_form.closed #filter_toggle,#toc-slider.closed #toc-toggle{
    border-radius: 5px;
}

#filter_toggle svg{
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

#filter_form.closed #filter_toggle i, 
.rotate{
    -webkit-transform: rotate(360deg) !important;
    transform: rotate(360deg) !important;
}

#toc-slider.closed #toc-toggle i, .rotate{
    transition: all 0.2s;
}

/* .rotate{
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    transition: all 0.2s;
} */

/* #filter_form.closed #filter_toggle i,#toc-slider.closed #toc-toggle i{
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
} */
div#filter #filters, section#toc-slider #toc-filters {
    padding: 15px 0;
}
div#filter button, section#toc-slider button, .changebutton button {
    color: #fff;
    font-weight: 200;
    padding: 10px;
    width: 100%;
    display: table;
    border-radius: 6px;
    font-size: 18px;
    font-weight: 400;
    cursor: pointer;
    margin: 0;
    border: none;
    height: 40px;
    background: var(--green);
}
div#filter select, section#toc-slider select {
    border: 1px solid #90a0c6;
    height: 40px;
    background-image: url(https://dgsgass4s0wnj.cloudfront.net/image/select.png);
    padding: 0 10px;
    border-radius: 6px;
    box-sizing: border-box;
    margin: 0;
    width: 100%;
    appearance: none;
    -webkit-appearance: none;
    background-position: right 10px center;
    background-color: #fff;
    background-repeat: no-repeat;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}
section#toc-slider #toc-filters{
    list-style-type: decimal;
    padding:15px;
    display: flex;
}
#toc-filters li{
    margin-left: 20px;
    text-align: left;
    box-sizing: border-box;
    margin-right: -20px;
    padding-right: 20px;
}
#toc-filters li a{
    color: #fff;
}


@media only screen and (max-width: 767px){
    #toc-toggle {
        font-size: 21px;
    }

    div#filter button{
        line-height: 1;
    }
}


ol.interiorList li {
    list-style-type: lower-roman;
}
#mobile-menu li.has_subs {
    min-height: 2.5rem;
    height: auto;
}
#mobile-menu li div {
padding: .313rem .625rem;
    line-height: 1.875rem;
    color: #333;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
#mobile-menu li ul{
    overflow-y: hidden;
    max-height: 0;
    transition: max-height .2s ease;
}
#mobile-menu li div.show + ul{
    max-height: 30rem;
    
}
#mobile-menu li div i:before{
    background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA3LjMzbDIuODI5LTIuODMgOS4xNzUgOS4zMzkgOS4xNjctOS4zMzkgMi44MjkgMi44My0xMS45OTYgMTIuMTd6Ii8+PC9zdmc+);
    content: '';
    height: 1rem;
    width:1rem;
    display: block;
    background-size: 1rem;
    background-repeat: no-repeat;

}
#mobile-menu li div.show i:before{
    	  -webkit-transform: rotate(180deg);
	  -moz-transform: rotate(180deg);
	  -o-transform: rotate(180deg);
	  -ms-transform: rotate(180deg);
	  transform: rotate(180deg);

}

.requested{
    background-color: #cedee5;
    color: rgba(0,0,0,.35);
    cursor: default;
    text-align: center;
}
.requested:before{
        content: '';
    height: .938rem;
    width: .938rem;
  display: inline-block;
  -webkit-mask: url( 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMuMzM0IDExLjk2Yy0uNzEzLS43MjYtLjg3Mi0xLjgyOS0uMzkzLTIuNzI3LjM0Mi0uNjQuMzY2LTEuNDAxLjA2NC0yLjA2Mi0uMzAxLS42Ni0uODkzLTEuMTQyLTEuNjAxLTEuMzAyLS45OTEtLjIyNS0xLjcyMi0xLjA2Ny0xLjgwMy0yLjA4MS0uMDU5LS43MjMtLjQ1MS0xLjM3OC0xLjA2Mi0xLjc3LS42MDktLjM5My0xLjM2Ny0uNDc4LTIuMDUtLjIyOS0uOTU2LjM0Ny0yLjAyNi4wMzItMi42NDItLjc3Ni0uNDQtLjU3Ni0xLjEyNC0uOTE1LTEuODUtLjkxNS0uNzI1IDAtMS40MDkuMzM5LTEuODQ5LjkxNS0uNjEzLjgwOS0xLjY4MyAxLjEyNC0yLjYzOS43NzctLjY4Mi0uMjQ4LTEuNDQtLjE2My0yLjA1LjIyOS0uNjEuMzkyLTEuMDAzIDEuMDQ3LTEuMDYxIDEuNzctLjA4MiAxLjAxNC0uODEyIDEuODU3LTEuODAzIDIuMDgxLS43MDguMTYtMS4zLjY0Mi0xLjYwMSAxLjMwMnMtLjI3NyAxLjQyMi4wNjUgMi4wNjFjLjQ3OS44OTcuMzIgMi4wMDEtLjM5MiAyLjcyNy0uNTA5LjUxNy0uNzQ3IDEuMjQyLS42NDQgMS45NnMuNTM2IDEuMzQ3IDEuMTcgMS43Yy44ODguNDk1IDEuMzUyIDEuNTEgMS4xNDQgMi41MDUtLjE0Ny43MS4wNDQgMS40NDguNTE5IDEuOTk2LjQ3Ni41NDkgMS4xOC44NDQgMS45MDIuNzk4IDEuMDE2LS4wNjMgMS45NTMuNTQgMi4zMTcgMS40ODkuMjU5LjY3OC44MiAxLjE5NSAxLjUxNyAxLjM5OS42OTUuMjA0IDEuNDQ3LjA3MiAyLjAzMS0uMzU3LjgxOS0uNjAzIDEuOTM2LS42MDMgMi43NTQgMCAuNTg0LjQzIDEuMzM2LjU2MiAyLjAzMS4zNTcuNjk3LS4yMDQgMS4yNTgtLjcyMiAxLjUxOC0xLjM5OS4zNjMtLjk0OSAxLjMwMS0xLjU1MyAyLjMxNi0xLjQ4OS43MjQuMDQ2IDEuNDI3LS4yNDkgMS45MDItLjc5OC40NzUtLjU0OC42NjctMS4yODYuNTE5LTEuOTk2LS4yMDctLjk5NS4yNTYtMi4wMSAxLjE0NS0yLjUwNS42MzMtLjM1NCAxLjA2NS0uOTgyIDEuMTY5LTEuN3MtLjEzNS0xLjQ0My0uNjQzLTEuOTZ6bS0xMi41ODQgNS40M2wtNC41LTQuMzY0IDEuODU3LTEuODU3IDIuNjQzIDIuNTA2IDUuNjQzLTUuNzg0IDEuODU3IDEuODU3LTcuNSA3LjY0MnoiLz48L3N2Zz4=');
  mask: url( 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMuMzM0IDExLjk2Yy0uNzEzLS43MjYtLjg3Mi0xLjgyOS0uMzkzLTIuNzI3LjM0Mi0uNjQuMzY2LTEuNDAxLjA2NC0yLjA2Mi0uMzAxLS42Ni0uODkzLTEuMTQyLTEuNjAxLTEuMzAyLS45OTEtLjIyNS0xLjcyMi0xLjA2Ny0xLjgwMy0yLjA4MS0uMDU5LS43MjMtLjQ1MS0xLjM3OC0xLjA2Mi0xLjc3LS42MDktLjM5My0xLjM2Ny0uNDc4LTIuMDUtLjIyOS0uOTU2LjM0Ny0yLjAyNi4wMzItMi42NDItLjc3Ni0uNDQtLjU3Ni0xLjEyNC0uOTE1LTEuODUtLjkxNS0uNzI1IDAtMS40MDkuMzM5LTEuODQ5LjkxNS0uNjEzLjgwOS0xLjY4MyAxLjEyNC0yLjYzOS43NzctLjY4Mi0uMjQ4LTEuNDQtLjE2My0yLjA1LjIyOS0uNjEuMzkyLTEuMDAzIDEuMDQ3LTEuMDYxIDEuNzctLjA4MiAxLjAxNC0uODEyIDEuODU3LTEuODAzIDIuMDgxLS43MDguMTYtMS4zLjY0Mi0xLjYwMSAxLjMwMnMtLjI3NyAxLjQyMi4wNjUgMi4wNjFjLjQ3OS44OTcuMzIgMi4wMDEtLjM5MiAyLjcyNy0uNTA5LjUxNy0uNzQ3IDEuMjQyLS42NDQgMS45NnMuNTM2IDEuMzQ3IDEuMTcgMS43Yy44ODguNDk1IDEuMzUyIDEuNTEgMS4xNDQgMi41MDUtLjE0Ny43MS4wNDQgMS40NDguNTE5IDEuOTk2LjQ3Ni41NDkgMS4xOC44NDQgMS45MDIuNzk4IDEuMDE2LS4wNjMgMS45NTMuNTQgMi4zMTcgMS40ODkuMjU5LjY3OC44MiAxLjE5NSAxLjUxNyAxLjM5OS42OTUuMjA0IDEuNDQ3LjA3MiAyLjAzMS0uMzU3LjgxOS0uNjAzIDEuOTM2LS42MDMgMi43NTQgMCAuNTg0LjQzIDEuMzM2LjU2MiAyLjAzMS4zNTcuNjk3LS4yMDQgMS4yNTgtLjcyMiAxLjUxOC0xLjM5OS4zNjMtLjk0OSAxLjMwMS0xLjU1MyAyLjMxNi0xLjQ4OS43MjQuMDQ2IDEuNDI3LS4yNDkgMS45MDItLjc5OC40NzUtLjU0OC42NjctMS4yODYuNTE5LTEuOTk2LS4yMDctLjk5NS4yNTYtMi4wMSAxLjE0NS0yLjUwNS42MzMtLjM1NCAxLjA2NS0uOTgyIDEuMTY5LTEuN3MtLjEzNS0xLjQ0My0uNjQzLTEuOTZ6bS0xMi41ODQgNS40M2wtNC41LTQuMzY0IDEuODU3LTEuODU3IDIuNjQzIDIuNTA2IDUuNjQzLTUuNzg0IDEuODU3IDEuODU3LTcuNSA3LjY0MnoiLz48L3N2Zz4=');
  -webkit-mask-size: cover;
  mask-position: 50% 50%;
  mask-repeat: no-repeat;
  mask-size: cover;
    background-color: rgba(0,0,0,.35);
   
}
.listing .requested {
    display: block;
    width: auto;
    font-size: 1rem;
    padding: .5rem;
    margin: .125rem -.625rem 0;
}
#profile_content .requested {
    position: fixed;
    bottom: 1.25rem;
    left: 1.25rem;
    right: 1.25rem;
    width: calc(100% - 1.25rem);
    height: 2.5rem;
    line-height: 2.5rem;
    display: block;
    font-size: 1rem;
    margin: 0 -1.25rem -1.25rem;
    padding: 0 .625rem;
}
#profile_content .requested:before {
    margin-top: .75rem;
}
/* .listing input:checked + button:before,.checked #profile_content button:before{
  -webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+') no-repeat 50% 50%;
  mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjAuMjg1IDJsLTExLjI4NSAxMS41NjctNS4yODYtNS4wMTEtMy43MTQgMy43MTYgOSA4LjcyOCAxNS0xNS4yODV6Ii8+PC9zdmc+') no-repeat 50% 50%;
    -webkit-mask-size: cover;
    mask-size: cover;
}

.listing input:checked + button span{
    font-size: 0px;
}
.listing input:checked + button span::after{
    content: 'Added to cart';
} */

/* .listing input:checked + button span:before,.checked #profile_content button span:before{
    content: 'd';
} */
/* MODAL POPUPS */
.overflow{
    overflow: hidden;   
}
.modal{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99999;
    background: rgba(0,0,0,.5);
    display: none;
    transition: all .25s ease-out; 
}
.guts {
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    background: #FFF;
    display: flex;
    justify-content: flex-start;
    max-height: calc(100% - 30px);
    overflow-y: auto;
    flex-direction: column;
    width: 600px;
    max-width: calc(100% - 30px);
    margin: 15px auto;
    padding: 0;
    box-shadow: 0 0 0 10px rgb(255 255 255 / 50%);
    border-radius: 5px;
    z-index: 1;
}

@media only screen and (max-width: 991px){
    .guts {
     width:calc(100% - 1.25rem);
    }
}
.modal-header {
    padding: 15px;
    border-bottom: 1px solid #324972;
}
.modal-content:not(#survey-modal) {
    padding: 15px;
}
.modal .homepage-title {
    margin-top: 10px;
}
.modal .homepage-title h2{
    font-size: 2rem;
    margin-bottom: 10px;
}

.backdrop{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
}

#newsletter form{
    padding: 20px 0;
    color: #000;
    background: #dedede;
    font-size: 14px;
    float: none;
    display: flex;
    flex-wrap: wrap;
}

#newsletter form .subscribe-item{
    flex: 0 1 50%;
    padding: 0 10px;
}

#newsletter input {
    height: 45px;
    width: 100%;
    padding-left: 10px;
    margin: 0 10px;
    font-size: 12px;
    margin: 0 0 15px;
    border: 2px solid #fff;
    caret-color: #009cf7;
}
#newsletter form .sub-btn{
    padding: 0 10px;
    flex: 0 1 100%;
}
.newsletter_disclaimer {
    text-align: center;
    font-size: 13px;
    margin-bottom: 1rem;
    font-style: normal;
}

/* LOADING */

#loading,#page_loading{
    position: fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index: 999999;
    display: none;
}
#loading.wait{
    display: block;
}
#loading div,#page_loading div {
    width: 100%;
    height: 100%;
    display:table;
    background: rgba(51,60,78,.75);
}
#loading div i,#page_loading i {
   display:table-cell;
    vertical-align:middle;
    text-align:center;
    font-size: 5rem;
    color:#FFF;
}

.fa-stack{
    width: 1.25rem;
}
.socialIcon .fa-stack{
    font-size: 1.25rem;
    background-color: #324972;
    border-radius: 2rem;
    height: 2.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
#breadcrumbs{
    display:flex;  
    padding: 20px 0;
    flex-direction: column;
}

@media only screen and (max-width: 767px) {

    #profile-intro-row .profile-intro-item1{
        padding: 10px 0;
    }

    #profile-intro-row .profile-intro-item2{
        padding: 10px 12px;
    }

    #profile-intro{
        margin-top: 0.5rem;
    }

    #profile #profile-listings .result-item{
        padding: 15px 0 0;
    }

    /* .profile-row .listing{
        display: none;
    } */

    #image-slider .splide--draggable>.splide__track>.splide__list>.splide__slide{
        width: 351px;
    }

    #image-slider .splide.is-active .splide__list{
        height: 280px;
    }

    /* #image-slider .splide__slide img,
    .splide__slide iframe{
        height: auto;
    } */

    #image-slider .splide__pagination{
        bottom: -20px;
    }

    .profile-locations-block{
        padding: 0;
    }

    #profile-about .pr-abt-btn .result-checkbox{
        margin: auto;
        width: 100%;
    }

    button#cart_bottom .text{
        font-size: 0.75rem;
        padding: 0 10px;
    }

    button#cart_bottom .text a{
        text-decoration: underline;
    }

    button#cart_bottom .button{
        padding: 0 15px;
    }

    #profileBanner .profile-container{
        padding: 12px;
    }

}

/* Sort Block */ 
.directory-container #sort-results:before {
    content: 'Order by:';
    display: inline-block;
}

.directory-container #sort-results {
    color: #4a4a4a;
    padding: 5px 0 15px;
    border-bottom: 2px dashed #dedede;
}

.directory-container #sort-results span {
    text-transform: uppercase;
    font-weight: 700; 
    font-family: 'Roboto', sans-serif;
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #4a4a4a;
    cursor: pointer;
    margin: 0 0 0 10px;
    font-size: 14px;
    text-transform: uppercase;
    transition: opacity .2s ease-in-out;
}

.directory-container #sort-results span.checked {
    cursor: default;
    background-color: #383f4d;
    border-color: #383f4d;
    color: #FFF;
    opacity: 1 !important;
}

.directory-container #sort-results span.checked:not(#sort_order.checked){
    cursor: pointer;
}

.directory-container #sort-results>*::before {
    content: '';
    margin: 0;
}
@media only screen and (max-width: 767px){
    .directory-container #sort-results {
        text-align: center;
    }
    .directory-container #sort-results:before{
        display: block;
    }
    .directory-container #sort-results span{
        margin-left: 0;
        padding: 5px;
    }
    .directory-container #sort-results>*::before{
        box-sizing: content-box;
    }
}
.profile-p {
    text-align: left;
}

.title-margins{
    padding: 0 !important;
    text-align: left !important;
}

/* Franchise */

.franchise-header{
    padding-bottom: 0 !important;
}

.franchise-page-content h2{
    font-size: 1.5rem;
    font-style: italic;
}

.franchise-page-content h3 {
    font-size: 1rem;
    color: var(--blue);
}

.franchise-page-content h2,
.franchise-page-content h3{
    padding-top: 10px;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.rotate{
    -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

#top {
    background: rgba(56,63,77,0.7);
    color: #FFF;
    padding: 0;
    position: fixed; 
    bottom: 5rem;
    right: 0;
    display: flex;
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    text-transform: uppercase;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    border-radius: 2rem 0 0 2rem;
    cursor: pointer;
    transition: background .5s ease;
    height: 50px;
    width:50px;
    overflow: hidden;
    z-index:25;
}
.changebutton button {
    color: #fff;
    font-weight: 200;
    padding: 10px;
    width: 100%;
    display: table;
    border-radius: 6px;
    font-size: 18px;
    font-weight: 400;
    cursor: pointer;
    margin: 0 0 40px;
    border: none;
    height: 40px;
    background: var(--green);
}
#base #fran-how-to .fht-row .fht-item1:last-child {
    margin: 0 auto;
}


/* Smart banner */

.smartbanner{
    background: linear-gradient(to bottom,#fff,#e0e0e0) !important;
    box-shadow: 0 1px 2px rgb(0 0 0 / 50%);
    line-height: 80px;
    display: none;
}

.smartbanner-container {
    margin: 0 auto;
    white-space: nowrap;
    height: 53px !important;
}

.smartbanner-close {
    display: inline-block;
    vertical-align: middle;
    margin: 0 5px 0 5px;
    font-family: 'ArialRoundedMTBold',Arial;
    font-size: 20px;
    text-align: center;
    color: #888;
    text-decoration: none;
    border: 0;
    border-radius: 14px;
    -webkit-font-smoothing: subpixel-antialiased;
    margin-bottom: 10px;
    line-height: 0 !important;
    width: 18px;
    height: 18px;
    text-shadow: 0 1px 0 white;
}

.smartbanner-icon {
    display: inline-block;
    vertical-align: middle;
    margin-right: 12px;
    background-size: cover;
    border-radius: 10px;
    width: 40px;
    height: 40px;
    vertical-align: baseline !important;
    border-top: 4px solid #fff;
    transition: opacity .3s;
    opacity: 1 !important;
    background-image: url(../images/icon.png) !important;
}

.smartbanner-info {
    display: inline-block;
    width: 45%;
    font-size: 11px;
    line-height: 1.2em;
    font-weight: 700;
    vertical-align: baseline !important;
    padding-top: 5px;
    color: #6a6a6a;
    text-shadow: 0 1px 0 rgb(255 255 255 / 80%);
}

.smartbanner-title {
    font-size: 13px;
    line-height: 18px;
    color: #4d4d4d;
    font-weight: 700;
}

.smartbanner-button {
    display: table-cell;
    vertical-align: middle;
    height: 22px;
    font-size: 14px;
    text-align: center;
    font-weight: 700;
    text-decoration: none;
    text-shadow: 0 1px 0 rgb(255 255 255 / 80%);
    background: #fff !important;
    text-transform: none;
    border: 1px solid #000;
    box-shadow: none !important;
    border-radius: 3px;
    padding: 0 10px;
    min-width: 10%;
    color: #6a6a6a;
}

/* View more buttons */
.center_guts {
    display: flex;
    flex-direction: column;
    /* flex-flow: row wrap; */
    justify-content: center;
    margin: 0 auto;
    max-width: calc(100% - 1rem);
    align-items: center;
}

a.continue_link,a.continue{
    padding: 10px;
    min-width: 300px;
    width: auto;
    text-align: center;
}

a.continue{
    background: #28af32;
    color: #fff;
    text-decoration: none;
    margin: 10px 0;
}

/* Guide to franchisee interview */
section.bio.first {
    border-top: 2px dashed #dedede;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-bottom: 5px solid #dedede;
}
section.bio img {
    width: 150px;
    height: auto;
    margin: 0 auto 2rem;
    display: table;
}
@media (min-width: 768px){
    section.bio img {
        float: left;
        margin: 0 2rem 0 0;
    }

}
section.bio h4 {
    font-size: 20px;
    padding-bottom: 0.5rem;
}

/* HTML extra */
.custom-html :is(ol,ul) {
    list-style-type:decimal;
    padding-left: 1rem;
    margin: 0 0 10px 10px;
}
.custom-html ul {
    list-style-type: disc;
}
.custom-html :is(ol,ul) li{
    margin: 0 0 10px;
}

ol:is(.A,.upper-alpha,[type="A"]){
    list-style-type: upper-alpha;
}

ol:is(.a,.lower-alpha){
    list-style-type: lower-alpha;
}

ol[type="I"]{
    list-style-type:upper-roman; 
}

.custom-html .tableholder {
    overflow-x: auto;
    border: 3px solid #dcdbd8;
    margin-bottom: 10px;
}
.custom-html .tableholder table{
    font-size: 90%;
    width: 100%;
}
.custom-html .tableholder table th {
    background: #dcdbd8;
    font-size: 80%;
    line-height: 1;
    text-align: center;
    padding: 5px;
}
.custom-html .tableholder table td {
    padding: 5px;
    text-align: center;
}
.custom-html .tableholder table tbody tr:nth-child(odd) td {
    background: #ebebeb;
}

/* Quiz CSS */
#survey header#quiz_header{
    border-bottom: 1px solid #324972!important;
}

/* State Facts */
#stateFacts ul{
    list-style: disc;
    padding-left: 2rem;
    margin-top: 10px;
}

#stateFacts table {
    border-collapse: collapse;
    width: 100%;
    max-width: 600px;
    margin: 1.5em auto;
    outline: solid #eeeeee 1px;
    border-radius: 3px;
}

#stateFacts table tr:nth-child(odd) {
    background-color: #f2f2f2;
}

#stateFacts table tr:nth-child(even) {
    background-color: #ffffff;
    color: #555555;
}

#stateFacts table td {
    text-align: center;
}

#intro #showStateFacts::before {
    top: 0.2rem;
    transform: rotate(135deg);
}

#intro #showStateFacts, #intro #hideStateFacts {
    margin: 0;
    margin-bottom: 5px;
    cursor: pointer;
    display: inline-block;
    padding-left: 0;
}

#intro #showStateFacts:before, #intro #hideStateFacts:before {
    border-color: #28af32;
    border-style: solid;
    border-width: 0.25em 0.25em 0 0;
    content: '';
    display: inline-block;
    height: 0.75rem;
    left: 0.25rem;
    position: relative;
    top: 0.6rem;
    transform: rotate(-45deg);
    vertical-align: top;
    width: 0.75rem;
    margin-right: 0.5rem;
}

#intro #showStateFacts::before {
    top: 0.2rem;
    transform: rotate(135deg);
}

#intro img{
    max-width: 400px;
    height: auto;
}

#site_footer_listings{
    margin-top: 2rem;
    border-top: 2px dashed #dedede;
}
#site_footer_listings:not(.ready){
    display: none;
}
#site_footer_listings #footer_title_h2{
    padding: 20px;
    font-size: 36px;
    color: #000;
    font-family: sans-serif;
    line-height: 1.2;
    margin-bottom: 10px;
    text-align: center;
}
#franchise_resources {
    padding-top: 1rem;
    margin-top: 1rem;
    padding-bottom: 1rem;
    border-top: 2px dashed #dedede;
}
#franchise_resources ul {
    list-style-type: disc;
    padding: 1rem 0 0 2rem;
}
#franchise_resources ul a{
    text-decoration: underline;
}
.col-lg-3.pillar-menu{
    margin-top: .9375rem;
}
#copy.container .col-lg-3.pillar-menu ul{
    padding-left: 0;
    list-style-type: none;
    padding-top: .5rem;
}
#copy.container .col-lg-3.pillar-menu li + li {
    border-top: 1px dotted #dedede;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
}
@media (min-width: 992px){
    .col-lg-3.pillar-menu{
        position: relative;
        width: calc(25% - .9375rem);
        margin-top: 0;
        margin-left:.9375rem;
    }
    .col-lg-3.pillar-menu > div:first-child{
        position: sticky;
        inset: 2rem 0 auto 0;
    }
}
small.subdisclaimer {
    font-size: .75rem;
    line-height: 1.2;
    display: block;
    font-style: italic;
}
.text-end{
    text-align: right;
}
#footer .follow a{
    color: #90a0c6;
    text-decoration: none;
    font-size: 1.5rem;
    margin: 0 0 0 5px;
}
#footer .follow svg{
    fill: #90a0c6;
    height:1.5rem;
}
.profileSocial svg{
    text-align: center;
    height: 1.5rem;
    width: 1.5rem;
    fill: #fff;
}
#top svg {
    height: 1.5rem;
    fill: #FFF;
}