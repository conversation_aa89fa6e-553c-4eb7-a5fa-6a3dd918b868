var triggerForm=!0;function onSubmit(e){if(1==triggerForm){triggerForm=!1;let t=document.getElementById("form-contact-us");if(!t.checkValidity())return t.reportValidity(),triggerForm=!0,!1;document.getElementById("waiting").classList.add("wait"),document.getElementById("recaptcha_token").value=e,submitContactInfo()}}function toggleNavbar(){document.getElementById("nav-collapse").classList.toggle("show")}document.addEventListener("DOMContentLoaded",(function(){document.getElementById("image-slider")&&new Splide("#image-slider",{type:"fade",perPage:1,autoplay:!0,rewind:!0}).mount()}));const validateEmail=e=>String(e).toLowerCase().match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);function sanitize(e){const t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"};return e.replace(/[&<>"'/]/gi,(e=>t[e]))}function loop(){let e="",t=document.getElementById("form-contact-us").querySelectorAll("input");for(var n=0;n<t.length;n++){var o=encodeURI(t[n].value);e+=t[n].name+"="+o+"&"}console.log(e)}function submitContactInfo(){let e=new XMLHttpRequest;data="",form=document.getElementById("form-contact-us"),loading=document.getElementById("waiting");let t=form.querySelectorAll("input");for(var n=0;n<t.length;n++){var o=encodeURI(t[n].value);data+=t[n].name+"="+o+"&"}data=data.slice(0,-1),console.log(data),e.open("POST","/request_contact.json"),e.setRequestHeader("Content-type","application/x-www-form-urlencoded"),e.onreadystatechange=function(){if(4===e.readyState&&200===e.status){var t=JSON.parse(e.responseText);console.log(t),loading.classList.remove("wait"),document.getElementById("form-msg").classList.remove("hide"),document.getElementById("form-contact-us").reset(),"success"==t.status?(document.getElementById("form-msg").innerText=t.message,document.getElementById("form-contact-us").classList.add("hide")):(document.getElementById("form-msg").innerText="Your request didn't go through, please try again later.",document.getElementById("form-contact-us").classList.add("hide"),document.getElementById("form-msg").classList.add("error"))}},e.send(data)}