//Lazy Load
!function(t,e){"object"==typeof exports?module.exports=e(t):"function"==typeof define&&define.amd?define([],e):t.LazyLoad=e(t)}("undefined"!=typeof global?global:this.window||this.global,function(t){"use strict";function e(t,e){this.settings=s(r,e||{}),this.images=t||document.querySelectorAll(this.settings.selector),this.observer=null,this.init()}"function"==typeof define&&define.amd&&(t=window);const r={src:"data-src",srcset:"data-srcset",selector:".lazyload",root:null,rootMargin:"0px",threshold:0},s=function(){let t={},e=!1,r=0,o=arguments.length;"[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(e=arguments[0],r++);for(;r<o;r++)!function(r){for(let o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e&&"[object Object]"===Object.prototype.toString.call(r[o])?t[o]=s(!0,t[o],r[o]):t[o]=r[o])}(arguments[r]);return t};if(e.prototype={init:function(){if(!t.IntersectionObserver)return void this.loadImages();let e=this,r={root:this.settings.root,rootMargin:this.settings.rootMargin,threshold:[this.settings.threshold]};this.observer=new IntersectionObserver(function(t){Array.prototype.forEach.call(t,function(t){if(t.isIntersecting){e.observer.unobserve(t.target);let r=t.target.getAttribute(e.settings.src),s=t.target.getAttribute(e.settings.srcset);"img"===t.target.tagName.toLowerCase()?(r&&(t.target.src=r),s&&(t.target.srcset=s)):t.target.style.backgroundImage="url("+r+")"}})},r),Array.prototype.forEach.call(this.images,function(t){e.observer.observe(t)})},loadAndDestroy:function(){this.settings&&(this.loadImages(),this.destroy())},loadImages:function(){if(!this.settings)return;let t=this;Array.prototype.forEach.call(this.images,function(e){let r=e.getAttribute(t.settings.src),s=e.getAttribute(t.settings.srcset);"img"===e.tagName.toLowerCase()?(r&&(e.src=r),s&&(e.srcset=s)):e.style.backgroundImage="url('"+r+"')"})},destroy:function(){this.settings&&(this.observer.disconnect(),this.settings=null)}},t.lazyload=function(t,r){return new e(t,r)},t.jQuery){const r=t.jQuery;r.fn.lazyload=function(t){return t=t||{},t.attribute=t.attribute||"data-src",new e(r.makeArray(this),t),this}}return e});

var triggerForm = true;
document.addEventListener( 'DOMContentLoaded', function () {
    if (document.getElementById('image-slider')){
        new Splide( '#image-slider', {
            type  : 'fade',
            perPage : 1,
            autoplay: true,
            rewind: true,
        } ).mount();
    }
});

function onSubmit(token) {
    if(triggerForm == true){
        triggerForm = false;
        let form = document.getElementById('form-contact-us');
        if (!form.checkValidity()) {
            form.reportValidity();
            triggerForm = true;
            return false;
        } else {
            let loading = document.getElementById('waiting');
            loading.classList.add('wait');
            document.getElementById('recaptcha_token').value = token;
            submitContactInfo();
        }
    }
}

function onSubmitGrowFran(token) {
    if(triggerForm == true){
        triggerForm = false;
        let form = document.getElementById('form-contact-us');
        console.log(form);
        // let loading = document.getElementById('waiting');
        // loading.classList.add('wait');
        document.getElementById('recaptcha_token').value = token;
        form.submit();
    }
}

function toggleNavbar(){
    document.getElementById('nav-collapse').classList.toggle('show');
}

const validateEmail = (email) => {
    return String(email)
        .toLowerCase()
        .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    );
};

function sanitize(string) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        "/": '&#x2F;',
    };
    const reg = /[&<>"'/]/ig;
    return string.replace(reg, (match)=>(map[match]));
}

function loop(){
    let data = '';
    let form = document.getElementById('form-contact-us');
    let inputs = form.querySelectorAll('input');    
    for (var i = 0; i < inputs.length; i++) {
        var temp_value = encodeURI(inputs[i].value);
        data += inputs[i].name+'='+temp_value+'&';
    }
    console.log(data);
}

function submitContactInfo(){
    let xhr = new XMLHttpRequest(),
        url = '/request_contact.json'
        data = '',
        form = document.getElementById('form-contact-us'),
        loading = document.getElementById('waiting');
    let inputs = form.querySelectorAll('input');    
    for (var i = 0; i < inputs.length; i++) {
        var temp_value = encodeURI(inputs[i].value);
        data += inputs[i].name+'='+temp_value+'&';
    }
    data = data.slice(0, -1);
    console.log(data);

    xhr.open("POST", url);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            var json = JSON.parse(xhr.responseText);
            console.log(json);
            loading.classList.remove('wait');
            document.getElementById('form-msg').classList.remove('hide');
            document.getElementById("form-contact-us").reset();
            if (json.status == 'success') {
                document.getElementById('form-msg').innerText = json.message;
                document.getElementById("form-contact-us").classList.add('hide');
            }else{
                document.getElementById('form-msg').innerText = 'Your request didn\'t go through, please try again later.';
                document.getElementById("form-contact-us").classList.add('hide');
                document.getElementById('form-msg').classList.add('error');
            }
        }
    }
    xhr.send(data);
}
lazyload(); 