<?php
    $copy = '';
    if (isset($_POST['contact']) && $_POST['contact'] == true) { 
        $copy = contact_form();
    }
?>
<div id="layer_slider_1" class="container">
    <div class="section_1">
        <div class="wrapper">
            <div class="img-wrapper">
                <img src="https://<?php echo $_SERVER['HTTP_HOST']; ?>/files-network/images/FGN_BANNERS_V1COMPS-04.png">
            </div>
            <div class="title-wrapper grow-franchise-banner" id="title_wrapper">
                <h1>We’d love to learn more about your business and
                    development objectives, and we’re here to talk
                    whenever you’re ready.
                </h1>
                <div>&nbsp;</div>
                <p>
                    Contact us to talk about growth! <br>
                    1-888-363-3390
                </p>
                <img src="https://<?php echo $_SERVER['HTTP_HOST']; ?>/files-network/images/Horizontal-small.png"
                    alt="Franchise.com FranchiseSolutions.com" style="margin-top: 20px;">
            </div>
        </div>
    </div>
</div>
<section class="container" id="title_wrapper">
    <div class="flex grow-franchise" style="padding: 40px 0px;">
        <h4 style="width: 50%; padding-right: 20px; border-right: 1px solid #ddd;">The pool of skilled people looking to
            gain control of their lives through franchise
            ownership continues to grow, and they’re actively searching for businesses like
            yours. Let us share your opportunity with them!
        </h4>
        <h5 style="padding: 0px 20px; border-right: 1px solid #ddd;">
           Franchise.com Network<br> 150 Granby Street <br> Norfolk, VA 23510
        </h5>
        <?php /* <div>  <img src="https://<?php echo $_SERVER['HTTP_HOST']; ?>/files-network/images/franchise-sales-index_200X141.png"  alt="Franchise Sales" title="franchise-sales-index_200X141" height="141" width="200">  </div>  </div> */ ?>       
    </div>
</section>
<section class="container" style="padding: 0px 0px 40px;">
    <div class="text-left" style="padding: 20px 0px;">
        <h4>Contact Us Today</h4>
    </div>
    <?php if ($copy) { ?>
        <div class="contact-us-thanks gray-border white-bg xy-padded text-center"><br><h5><?php is_safe($copy); ?></h5><br></div>
    <?php } else { ?>
    <form id="form-contact-us" action="/network/grow-franchise" method="post">
        <input type="hidden" id="contact" name="contact" value="true"> 
        <input type="hidden" id="recaptcha_token" name="recaptcha_token">
        <div class="form-group" style="display:none !important;">
            <label for="work_phone">Work Phone</label>
            <input type="text" id="work_phone" name="work_phone" value="" tabindex="-1" autocomplete="work-phone">
        </div>
        <div class="form-group">
            <label class="required">First Name</label>
            <input type="text" class="form-control" id="visitor_firstname" name="visitor_firstname" required>
        </div>
        <div class="form-group">
            <label class="required">Last Name</label>
            <input type="text" class="form-control" id="visitor_lastname" name="visitor_lastname" required>
        </div>
        <div class="form-group">
            <label>Company</label>
            <input type="text" class="form-control" name="visitor_company" id="visitor_company">
        </div>
        <div class="form-group">
            <label>Website Address</label>
            <input type="text" class="form-control" name="visitor_website" id="visitor_website">
        </div>
        <div class="form-group">
            <label class="required">Phone</label>
            <input type="tel" class="form-control" id="visitor_phone" name="visitor_phone" required>
        </div>
        <div class="form-group">
            <label class="required">E-Mail</label>
            <input type="email" class="form-control" id="visitor_email" name="visitor_email" required>
        </div>
        <div class="form-group">
            <label class="required">Message</label>
            <textarea cols="30" rows="10" class="form-control" id="visitor_message" name="visitor_message" required></textarea>
        </div>
        <div class="form-group">
            <button type="button" class="g-recaptcha btn btn-submit" 
            data-sitekey="6LesHzMgAAAAAPOkHnSrg2VpQIfqI1bpkeR67PFJ" 
            data-callback='onSubmitGrowFran' 
            data-action='submit'>Submit</button>
        </div>
    </form>
    <?php } ?>
</section>