html {
    color: #333;
    background-color: #fff;
    font-size: 16px;
    line-height: 1.2;
    font-family: 'open sans', 'HelveticaNeue', 'Helvetica Neue', 'Helvetica-Neue', Helvetica, Arial, sans-serif;
}

html,
body {
    padding: 0;
    margin: 0;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: Helvetica, Arial, sans-serif !important;
    font-size: 14px;
    color: #1a1a1a;
}

@media only screen and (min-width: 768px) {
    .container {
        width: 750px;
    }

    .col-sm-push-1 {
        margin-left: 8.33%;
    }
}

@media only screen and (min-width: 992px) {
    .container {
        width: 970px;
    }
}

@media only screen and (min-width: 1200px) {
    .container {
        width: 1170px;
    }
}

@media only screen and (max-width: 992px) and (min-width: 426px) {
    .hidden-md {
        display: none !important;
    }
}

.hide {
    display: none !important;
}

/* img {
    max-width: 100%;
    height: auto;
} */

a {
    text-decoration: none;
    color: #428bca;
}

h1 {
    line-height: 50px;
}

p {
    overflow-wrap: break-word;
    line-height: 23px;
}

.blue {
    color: #0033A0;
}

.green {
    color: green;
}

.red {
    color: red;
}

.pd-15 {
    padding: 0 15px;
}

.padding-40 {
    padding: 40px 0;
}

.margin-btm-20 {
    margin-bottom: 20px;
}

.space-20 {
    width: 100%;
    height: 20px;
    clear: both;
}

.space-50 {
    width: 100%;
    height: 50px;
    clear: both;
}

.space-70 {
    width: 100%;
    height: 50px;
    clear: both;
}

.pull-right {
    float: right !important;
}

.pull-left {
    float: left !important;
}

h1.underline,
h2.underline,
h3.underline,
h4.underline {
    display: block;
    padding-bottom: 8px;
    border-bottom: 1px solid #84bd00;
}

.block-separator {
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-color: #ccc;
}

label {
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: 700;
}

/* Topbar */

.header-main {
    width: 100%;
}

.top-bar {
    background: #f8f8f8 !important;
    color: #555 !important;
}

.top-bar .top-bar-container {
    display: flex;
    justify-content: space-between;
}

.top-bar ul {
    margin: 0px;
}

.list-inline>li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px;
}

.widget ul li {
    padding: 10px 0;
    border-bottom: 1px dotted #7e7e7e;
}

.top-bar .info li {
    font-size: 10px;
    vertical-align: middle;
    color: #555;
    font-weight: 500;
    border-bottom: none;
}

.widget ul li a {
    display: block;
    color: #777;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}

.top-bar .info-link {
    color: gray !important;
    transition: all 250ms ease-in-out !important;
    font-size: 12px !important;
    font-weight: 600;
}

.top-bar .info li a {
    color: gray;
    margin-right: 10px;
}

.hire-sec .services-icon i,
.services-icon i,
.services-box .service-box-icon,
.services-icon i::before,
.services-box .service-box-icon::before {
    background-color: #84bd00;
    color: #fff;
    border: none;
    line-height: 70px;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -webkit-transition: all 200ms ease-in;
    -o-transition: all 200ms ease-in;
    -moz-transition: all 200ms ease-in;
    transition: all 200ms ease-in;
}

.navbar-default {
    background-color: #fff;
    border-color: #eeeeee;
    -webkit-box-shadow: 0 5px 6px -6px #ccc;
    -moz-box-shadow: 0 5px 6px -6px #ccc;
    box-shadow: 0 5px 6px -6px #ccc;
    border: none;
}

.navbar {
    border-radius: 0;
    margin-bottom: 0px;
    padding: 0;
}

#navbar {
    border: 1px solid #e1e1e1 !important;
}

.sticky {
    width: 100%;
    z-index: 999;
    position: fixed;
    top: 0;
    /* min-height: 60px; */
    margin-top: 0px !important;
    margin-bottom: 0px !important;
    padding: 0px !important;
    opacity: 0.95;
    transition: linear 0.2s;
}

#navbar.sticky .navbar-brand img {
    width: 200px !important;
    transition: linear 0.2s;
}

#navbar:not(.sticky) .navbar-brand img {
    transition: linear 0.2s;
}

#navbar.sticky .navbar-default .navbar-nav li a {
    padding: 10px 12px !important;
    font-size: 14px;
    font-family: Helvetica, Arial, sans-serif !important;
    text-transform: Capitalize;
    letter-spacing: -0;
    position: relative;
    display: block;
    line-height: 24px;
    z-index: 999 !important;
    transition: linear 0.2s;
}

#navbar:not(.sticky) .navbar-default .navbar-nav li a {
    transition: linear 0.2s;
}

.navbar-brand {
    margin-left: -10px;
}

#navbar:not(.sticky) .navbar-brand img {
    width: 270px !important;
    height: auto;
    transition: linear 0.2s;
}

.nav>li.active,
.nav>li:hover,
.nav>li.active:hover {
    border-bottom: 2px solid #2d5c88 !important;
}

.dropdown .nav>li,
.dropdown .nav>li.active,
.dropdown .nav>li:hover,
.dropdown .nav>li.active:hover {
    border-bottom: none !important;
}

.dropdown .nav>li:hover {
    background-color: #fbfbfc !important;
}

.nav>li.active>a,
.nav li:hover>a {
    color: black !important;
}

.navbar-nav>li>a {
    text-transform: Capitalize;
    letter-spacing: 0;
}

.navbar-default .navbar-nav li a {
    color: gray;
    font-weight: 500;
    font-size: 14px;
    font-family: Helvetica, Arial, sans-serif !important;
    text-transform: Capitalize;
    letter-spacing: -0;
    position: relative;
    display: block;
    padding: 20px 12px;
    line-height: 44px;
}

.btn,
input.btn,
input[type=button].contact-submit,
.btn:hover,
input.btn:hover,
input[type=button].contact-submit:hover {
    background-color: #84bd00;
    border: 1px solid #689401;
    border-bottom: 2px solid #689401;
    color: #fff;
    font-size: 18px;
    padding-top: 8px;
}

.navbar-toggle {
    display: none;
}

.navbar-collapse {
    display: block !important;
    flex-basis: auto;
    flex-grow: initial;
}

.navbar-collapse ul li {
    display: inline-block;
}

.navbar-nav {
    flex-direction: row;
}

@media only screen and (min-width: 768px) {
    .navbar-brand img {
        width: 270px;
        height: auto;
    }

    .dropdown i {
        display: none !important;
    }
}

@media only screen and (max-width: 992px) {
    .navbar-default .navbar-nav>li>a {
        font-size: 12px;
    }
    img {
        max-width: 100%;
        height: auto;
    }
    
}

@media only screen and (max-width: 767px) {
    #navbar:not(.sticky) .navbar-brand img {
        width: 240px !important;
    }

    .navbar {
        position: relative;
    }

    .header-main:not(.sticky) #nav-collapse {
        position: absolute;
        width: 100%;
        top: 54px !important;
        right: 2px !important;
        padding-bottom: 5px;
        padding-left: 10px;
        margin-bottom: 5px;
        background-color: white;
        border-top: none;
        /* border: 1px solid grey; */
    }
    .navbar-collapse ul li {
        display: block;
        background-color: white !important;    
    }

    .navbar-nav {
        display: block;
    }

    .navbar-collapse {
        flex-basis: 100%;
        flex-grow: 1;
        align-items: center;
        z-index: 999;
    }

    .navbar-collapse ul li {
        border-top: 1px solid #ddd;
        width: 100%;
    }

    .navbar-default .navbar-nav>li>a {
        line-height: 1;
        padding: 12px;
        font-size: 14px;
    }

    .navbar-header {
        width: calc(100% - 1rem);
    }

    .navbar-toggle {
        display: block;
        float: right;
        margin-top: 20px;
        border: none;
        background: white !important;
    }

    .navbar-toggle i {
        color: #000;
        font-size: 20px;
    }

    .collapse:not(.show) {
        display: none !important;
    }

    .dropdown-content {
        display: none;
        background-color: #fff;
        color: gray !important;
        width: 100% !important;
        z-index: 999 !important;
        opacity: 1 !important;
        border-top: none !important;
    }

    .dropdown-content li a {
        font-weight: 400 !important;
        font-size: 14px !important;
        line-height: 1 !important;
        padding: 5px 10px !important;
        font-size: 14px !important;
        z-index: 1 !important;
    }

    .dropdown-content li {
        padding: 10px 25px !important;
        display: block !important;
        opacity: 1 !important;
    }
    .navbar-collapse>li>a{
        border-top: 1px solid #ddd;
    }
    .navbar-collapse>li>a{
        padding: 15px 20px !important;
    }
}


@media only screen and (max-width: 426px) {

    .img-fluid {
        width: 100%;
    }
    .offcanvas-body {
        padding: 0px !important;
    }
    .dropdown-toggle{
        float: right;
    }
    #footer .textwidget.widget_site img{
        width: 120px;
        height: auto;
        padding: 0px;
    }
    #footer .textwidget.widget_site a{
        padding: 0px !important;
    }
    #footer .widget {
        margin: 10px 0px !important;
    }
    #footer .textwidget.widget_site p{
        padding: 0px !important;
        margin-bottom: 5px !important;
    }
}


.form-control {
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -webkit-text-size-adjust: 100%;
    height: 40px;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 25px;
    color: #7a7a7a;
    border: 1px solid #dadada;
    resize: none;
    vertical-align: middle;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background: #FFF;
    -webkit-border-radius: 0;
    border-radius: 0;
    -webkit-transition: all 200ms ease-in;
    -o-transition: all 200ms ease-in;
    -moz-transition: all 200ms ease-in;
    transition: all 200ms ease-in;
}

/* Testimonials */
.accordion-button,
.accordion-button:not(.collapsed) {
    color: #333;
    background-color: #f5f5f5;
    border-color: #ddd;
}

.accordion-button::after,
.accordion-button:not(.collapsed)::after {
    background-image: none !important;
}

.accordion-item {
    margin-top: 5px;
    border-radius: 4px;
    border: 1px solid transparent;
    border-color: #ddd;
}

.accordion-item:not(:first-of-type) {
    border-top: 1px solid #ddd;
}

.newsletter .btn.form-control {
    width: auto;
    text-transform: capitalize;
}

@media (max-width:639px) {
    .top-bar {
        display: none
    }
}

/* BASE CSS */
:root {
    --blue: #182E58;
    --green: #3BB54A;
    --light-gray: #ECECEC;
    --dark-gray: #686A6C;
    --link: #2364AA;
    --icon: #006EBF;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    color: #333;
    background-color: #fff;
    font-size: 16px;
    line-height: 1.2;
}

html,
body {
    padding: 0;
    margin: 0;
    min-height: 100%;
}

body {
    color: #222;
    font-size: 15px;
    line-height: 1.733rem;
}

a {
    color: #2364aa;
    text-decoration: none;
}

body p {
    margin: 0 0 1rem;
    line-height: 24px;
    font-size: 14px;
    color: gray;
}

.h1,
h1 {
    margin: 0 0 0.25rem;
    padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
    line-height: 1.5;
    color: #303030;
}

/* .w-100{
    max-width: 72rem;
    width: calc(100% - 1rem);
} */

@media only screen and (min-width: 48rem) {
    .container {
        margin: 0 auto;
        padding: 0;
        max-width: 72rem;
        width: calc(100% - 1rem);
    }

    .w-100 {
        width: 100% !important;
        max-width: 75rem;
    }
}

@media (min-width: 426px) {
    #footer .container {
        display: flex;
        flex-direction: row;
    }

    #footer .container>div {
        margin: 10px;
        width: 450px;
    }
}


#footer {
    background-color: #222 !important;
    color: #ddd !important;
}

#footer h3 {
    color: #919191;
    font-size: 14px !important;
    font-weight: 500 !important;
}

#footer p,
#footer span {
    font-size: 14px !important;
}

#footer .widget {
    padding: 0;
    margin: 30px 0 30px 0;
    overflow: hidden;
}

.flex_column .widget .widgettitle,
.content .widget .widgettitle {
    margin-top: 0.85em;
}

.widgettitle {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 1.1em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'open sans', 'HelveticaNeue', 'Helvetica Neue', 'Helvetica-Neue', Helvetica, Arial, sans-serif;
}

p a,
p a:visited {
    line-height: inherit;
}

#footer a {
    color: #fff;
}

.media-kit-button {
    position: relative;
    text-decoration: none;
    display: inline-block;
    vertical-align: middle;
    width: 324px;
    height: 50px;
    border: 2px solid #0f9bd7;
    border-radius: 4px 4px 4px 4px;
    background-color: #0f9bd7;
    box-shadow: 0 0 2px 0 #0f9bd7;
}

.media-kit-button .mb-text {
    color: #fff;
    font-family: Tahoma;
    font-size: 15px;
    text-align: center;
    font-style: normal;
    font-weight: 400;
    line-height: 1em;
    box-sizing: border-box;
    display: block;
    background-color: unset;
    padding: 18px 0 0 0;
}

.media-kit-button:hover {
    border-color: #152f55;
    background-color: #152f55;
    box-shadow: 0 0 2px 0 #152f55;
}

h2 {
    text-transform: uppercase;
    letter-spacing: 1px;
}
.wrapper .title-wrapper{
    max-width: 100%;
}
#layer_slider_1,
#layer_slider_2 {
    height: 350px;
    position: relative;
}

#layer_slider_1 .wrapper .img-wrapper,
#layer_slider_2 .wrapper .img-wrapper {
    z-index: 101;
    width: (100%-2rem);
    /* max-width: 1400px; */
    height: 400px;
    display: block;
    text-align: center;
    position: absolute;
    top: 100;
    left: 0;
    right: 0;
}

#title_wrapper.mt-4 {
    top: 0 !important;
    transition: linear 0.2s;
}

#title_wrapper:not(.mt-4) {
    transition: linear 0.2s;
}

#layer_slider_1 .wrapper .img-wrapper img,
#layer_slider_2 .wrapper .img-wrapper img {
    z-index: auto;
    width: 100%;
    max-width: 72rem;
    height: auto;
    max-height: 450px;
    text-align: center;
    opacity: 0.7;
}

#layer_slider_1 .wrapper .title-wrapper {
    position: relative;
    z-index: 102;
    margin-left: 0px;
    margin-top: 0px;
    width: 600px;
    height: 100px;
    left: 50px;
    top: 20px;
    opacity: 1;
    display: block;
}

#layer_slider_2 .wrapper .title-wrapper {
    position: relative;
    z-index: 102;
    margin-left: 0px;
    margin-top: 0px;
    width: 600px;
    height: 100px;
    left: 460px;
    top: 40px;
    opacity: 1;
    display: block;
}
@media only screen and (min-width: 48rem) {
    :is(#layer_slider_1,#layer_slider_2) .wrapper .title-wrapper{
        max-width: 75rem !important;
    }
}

#layer_slider_1 .wrapper .title-wrapper h1,
#layer_slider_2 .wrapper .title-wrapper h1 {
    letter-spacing: -0.776316px;
    font-weight: 500;
    font-size: 28px;
    line-height: 30px;
    color: rgb(68, 68, 68);
}

#layer_slider_1 .wrapper .title-wrapper p,
#layer_slider_2 .wrapper .title-wrapper p {
    font-size: 15px;
    line-height: 17px;
    margin-top: 20px;
    letter-spacing: 0px;
}

#layer_slider_1 .wrapper .title-wrapper a img {
    float: right;
    margin-top: 20px;
}

#layer_slider_2 .wrapper .title-wrapper a img {
    float: left;
    margin-top: 20px;
}

.margin-top-240 {
    margin-top: 240px;
}

@media only screen and (max-width: 786px) {
    #title_wrapper.mt-4 {
        top: -300 !important;
        transition: linear 0.2s;
    }

    #title_wrapper:not(.mt-4) {
        transition: linear 0.2s;
    }

    .margin-top-240 {
        margin-top: 0px;
    }

    :is(#layer_slider_1 ,#layer_slider_2) {
        height: 300px;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .img-wrapper {
        z-index: 101;
        width: 100%;
        height: 200px;
        display: block;
        text-align: center;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .img-wrapper img {
        z-index: auto;
        width: 100%;
        min-height: 250px;
        height: auto;
        /* height: 280px; */
        text-align: center;
    }

    #layer_slider_1 .wrapper .title-wrapper {
        position: absolute;
        z-index: 102;
        margin-left: 0px;
        margin-top: 0px;
        width: 400px;
        height: 100px;
        left: 50px;
        top: 100px;
        opacity: 1;
        display: block;
    }

    #layer_slider_2 .wrapper .title-wrapper {
        position: absolute;
        z-index: 102;
        margin-left: 0px;
        margin-top: 0px;
        width: 400px;
        height: 100px;
        left: 300px;
        top: 100px;
        opacity: 1;
        display: block;
    }

    :is(#layer_slider_1 ,#layer_slider_2) h1 {
        letter-spacing: -0.776316px;
        font-weight: 600;
        font-size: 18px;
        line-height: 20px;
        color: rgb(68, 68, 68);
    }

    :is(#layer_slider_1 ,#layer_slider_2) p {
        font-size: 12px;
        line-height: 17px;
        margin-top: 10px;
        letter-spacing: 0px;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper img{
        margin: 0 auto 0 !important;
        float: none !important;
        max-width: 90%;
        display: initial;
        padding: 0;
    }
}

@media only screen and (max-width: 426px) {
    .margin-top-240 {
        margin-top: 0px;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper > *:not(a){
        max-width: 90%;
    }
    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper > a{
        width:100%;
    }

    :is(#layer_slider_1 ,#layer_slider_2) {
        height: 300px;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .img-wrapper {
        display: none;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper {
        position: absolute;
        z-index: 102;
        margin-left: 0px;
        margin-top: 0px;
        width: 400px;
        height: 100px;
        left: 10px;
        top: 70px;
        opacity: 1;
        display: block;
    }
    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper:after{
        display: table;
        content: '';
        clear: both;
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper h1 {
        letter-spacing: -0.776316px;
        font-weight: 600;
        font-size: 18px;
        line-height: 20px;
        color: rgb(68, 68, 68);
    }

    :is(#layer_slider_1 ,#layer_slider_2) .wrapper .title-wrapper p {
        font-size: 12px;
        line-height: 17px;
        margin-top: 10px;
        letter-spacing: 0px;
    }
}

.hide {
    display: none !important;
}

.title {
    margin: 20px auto;
}

.testimonial-section {
    display: flex;
    flex-direction: row;
    padding: 5px 0px;
    color: rgb(145, 145, 145);
}

.testimonial-section blockquote {
    padding: 0px 30px;
    border-left: 8px solid rgb(45, 92, 136);
    width: 75%;
}

.testimonial-section img {
    width: 240px;
    height: 150px;
}

.testimonial-footer {
    margin-left: 80px;
}

.testimonial-footer .testimonial-name {
    font-weight: 600;
}

.testimonial-footer .testimonial-subtitle {
    font-weight: 300px;
    color: rgb(145, 145, 145);
    font-size: 14px;
    margin-top: -8px;
}

form button.btn-submit {
    margin-top: 20px;
    font-size: 16px;
    min-width: 142px;
    padding: 16px 20px;
    border-radius: 2px;
    background-color: rgb(11, 58, 102);
    color: rgb(255, 255, 255);
    border-color: rgb(11, 58, 102);
}

form button.btn-submit:hover {
    background-color: rgb(61, 113, 161);
    color: rgb(255, 255, 255);
    border-color: rgb(27, 80, 128);
    padding: 16px 20px;
    font-size: 16px;
}

textarea {
    min-height: 160px !important;
    line-height: 1.5em;
}

.form-group label.required:after {
    content: " *";
    color: red;
}

.flex {
    display: flex;
}

.bordered {
    border: 1px solid black;
    padding: 20px;
}

/* FAQS */
#faqs {
    padding: 40px 0px;
}

#faqs div {
    border-left: #2d5c88 7px solid;
    margin: 0 0 15px;
    padding: 0;
    background: #EEE;
}

#faqs div.open p {
    /* background-color: #fff; */
    display: block;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#faqs p {
    display: none;
    margin: 0;
    padding: 10px
}

#faqs h5 {
    font-size: .9rem;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #2d5c88;
}

#faqs h5 span {
    max-width: calc(100% - 1.5rem);
}

#faqs h5:after {
    content: "";
    width: .9rem;
    height: .9rem;
    display: block;
    -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgOWgtOXYtOWgtNnY5aC05djZoOXY5aDZ2LTloOXoiLz48L3N2Zz4=);
    mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgOWgtOXYtOWgtNnY5aC05djZoOXY5aDZ2LTloOXoiLz48L3N2Zz4=);
    -webkit-mask-size: cover;
    mask-size: cover;
    -webkit-mask-position: center;
    mask-position: center;
    background-color: #2d5c88;
    /* padding-left: 10px; */
}

#faqs h5:after:hover {
    background-color: #2d5c88;
    ;
}

#faqs .open h5:after {
    background-color: #2d5c88;
    -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA5aDI0djZoLTI0eiIvPjwvc3ZnPg==);
    mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA5aDI0djZoLTI0eiIvPjwvc3ZnPg==)
}

.survey-form {
    border: 1px solid black;
    margin: 20px 0px;
}

/* Navbar tabs dropdown */
@media screen and (min-width: 768px) {
    .dropdown {
        position: relative !important;
        display: inline-block !important;
    }

    .dropdown-content {
        display: none;
        position: absolute !important;
        background-color: #fff;
        color: gray !important;
        width: 350px;
        z-index: 999 !important;
        border: 1px solid #e1e1e1;
        opacity: 1 !important;
        /* margin-top: 2px; */
        border-top: 2px solid #2d5c88;
    }

    .dropdown-content li a {
        padding: 0px !important;
        margin: 0px !important;
        font-weight: 400 !important;
        font-size: 14px !important;
    }

    .dropdown-content li {
        display: block !important;
        opacity: 1 !important;
        margin: 0px !important;
        padding: 0px 15px !important;
    }

    .dropdown:hover .dropdown-content {
        display: block !important;
        opacity: 1 !important;
    }

    #navbar:not(.sticky) .navbar-brand img,#navbar.sticky .navbar-brand img {
        width: 180px !important;
    }

    .navbar-brand{
        margin-left: 0px;
    }

    
    .navbar>.container{
        flex-wrap: nowrap;
    }
    #navbar.sticky li a {
        font-size: 12px !important;
    }

    #footer .textwidget.widget_site img{
        width: 120px;
        height: auto;
        padding: 0px;
    }
    #footer .widget {
        margin: 10px 0px !important;
    }
    #footer .textwidget.widget_site p{
        padding: 0px !important;
        margin-bottom: 10px !important;
    }
}

@media screen and (min-width: 650px) and (max-width: 768px) {
    #title_wrapper{
        margin-top: 30px !important;
    }
    #layer_slider_1 .wrapper .title-wrapper h1,
    #layer_slider_2 .wrapper .title-wrapper h1 {
        letter-spacing: -0.776316px;
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        color: rgb(68, 68, 68);
    }

    #layer_slider_1 .wrapper .title-wrapper p,
    #layer_slider_2 .wrapper .title-wrapper p {
        font-size: 14px;
        line-height: 17px;
        margin-top: 10px;
        letter-spacing: 0px;
    }
}

@media screen and (min-width: 769px) and (max-width: 820px) {
    #title_wrapper{
        margin-top: 30px !important;
    }
    #layer_slider_1 .wrapper .title-wrapper h1,
    #layer_slider_2 .wrapper .title-wrapper h1 {
        letter-spacing: -0.776316px;
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        color: rgb(68, 68, 68);
    }

    #layer_slider_1 .wrapper .title-wrapper p,
    #layer_slider_2 .wrapper .title-wrapper p {
        font-size: 14px;
        line-height: 17px;
        margin-top: 10px;
        letter-spacing: 0px;
    }

    #layer_slider_1 .wrapper .title-wrapper img,
    #layer_slider_2 .wrapper .title-wrapper img {
        margin-top: 5px !important;
    }
    #layer_slider_1, #layer_slider_2 {
        height: 250px;
    }
    #title_wrapper {
        margin-top: 0px !important;
    }
    .who-we-are.flex{
        width: 100% !important;
    }
    .fcom_sales_index.flex {
        display: block;
    }
    .fcom_sales_index.flex>div {
        width: 100% !important;
        border-right: none !important;
    }
    #layer_slider_2 .wrapper .title-wrapper {
        position: relative;
        z-index: 102;
        margin-left: 0px;
        margin-top: 0px;
        width: 600px;
        height: 100px;
        left: 200px;
        top: 40px;
        opacity: 1;
        display: block;
    }
    #layer_slider_2 .wrapper .title-wrapper h1{
        margin-top: 10px !important;
    }
    .how-we-help#section_2 .flex{
        padding: 40px !important;
    }
    #layer_slider_1 .wrapper .title-wrapper p, #layer_slider_2 .wrapper .title-wrapper p {
        margin-top: 0px !important;
    }
}

.who-we-are .wrapper {
    text-align: left;
}

.who-we-are .content-wrapper {
    padding: 0px 20px !important;
}

.how-we-help#section_4 h2 {
    font-size: 17px;
    line-height: 34px;
    font-weight: 700;
}

.card-holder {
    padding: 45px 20px 20px;
    width: 300px;
    margin: 0px 20px;
    background: #fcfcfc;
    border-radius: 3px;
    box-shadow: 0 0 1px 0 rgb(0 0 0 / 30%);
}

.grow-franchise-banner h1{
    line-height:35px !important;
}
.grow-franchise-banner p{
    font-size:24px !important;
    color:black !important;
    padding:0px 80px !important;
    line-height:30px !important;
    font-weight:600 !important;
}

@media only screen and (max-width: 786px) {
    .franchise-insights {
        width: 100%;
        display: block;
    }

    .franchise-insights div {
        width: 100% !important;
        border: none !important;
    }

    .fcom_sales_index.flex {
        display: block;
    }

    .fcom_sales_index.flex div {
        border-right: none !important;
        padding-right: 0px !important;
        padding-left: 0px !important;
        width: 100% !important;
    }

    .how-we-help#section_2 .flex {
        padding: 10px 0px !important;
    }

    .how-we-help#section_3 .flex {
        margin: 10px 0px !important;
    }

    .how-we-help#section_3 .img-wrapper {
        margin: 10px auto;
        text-align: center;
        vertical-align: middle;
    }

    .how-we-help .card-holder {
        padding: 15px 10px 10px !important;
        margin: 0px 10px !important;
    }

    .how-we-help#section_4 p {
        margin: 20px !important;
    }

    .how-we-help#section_4 h2 {
        font-size: 14px !important;
        line-height: 20px !important;
        font-weight: 700 !important;
    }

    .who-we-are .content-wrapper img {
        width: 100px !important;
        height: 100px !important;
        max-width: 100px !important;
    }

    .who-we-are .wrapper {
        padding: 0px 10px !important;
    }

    .who-we-are {
        width: 100% !important;
    }

    .grow-franchise-banner h1{
        line-height:25px !important;
    }
    .grow-franchise-banner p{
        font-size:20px !important;
        color:black !important;
        padding:0px 20px !important;
        line-height:25px !important;
        font-weight:600 !important;
    }
}



@media only screen and (max-width: 426px) {
    .franchise-insights div {
        display: block;
    }

    .grow-franchise h4,
    .grow-franchise h5,
    .grow-franchise div {
        display: block !important;
        border-right: none !important;
        padding-right: 0px !important;
        font-size: 16px;
        width: 100% !important;
        text-align: center;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px !important;
    }

    .flex {
        display: block !important;
    }

    .fcom_sales_index .flex {
        display: block;
        text-align: center;
    }

    .fcom_sales_index .flex p {
        text-align: left;
    }

    .turn_franchise_portal {
        margin-top: 20px !important;
        margin-bottom: 20px !important;
        padding: 10px 0px !important;
    }

    .turn_franchise_portal hr {
        margin-top: 20px !important;
    }

    .turn_franchise_portal .flex {
        text-align: center;
        margin: 20px 0px !important;
    }

    .turn_franchise_portal .flex img {
        padding-bottom: 10px !important;
    }

    #faqs {
        padding: 20px 0px !important;
    }

    .what-clients-say {
        border-bottom: 1px solid #ddd !important;
        padding-bottom: 20px !important;
        margin-bottom: 20px !important;
    }

    .what-clients-say .testimonial-section {
        display: flex;
        flex-direction: column-reverse !important;
        text-align: center;
        border-top: 5px solid rgb(45, 92, 136) !important;
        margin-top: 20px !important;
    }

    .what-clients-say hr {
        display: none !important;
    }

    .what-clients-say .testimonial-section blockquote {
        width: 100% !important;
        padding: 0px !important;
        border-left: none !important;
        margin-top: 10px !important;
        text-align: left !important;
    }

    .what-clients-say .testimonial-section blockquote p {
        margin-bottom: 0px !important;
    }

    .what-clients-say .testimonial-footer {
        width: 100% !important;
        margin-left: 0px !important;
    }

    .what-clients-say img {
        width: 300px;
        height: auto;
        margin: 20px auto 0px !important;
    }

    .how-we-help .card-holder {
        padding: 15px 10px 10px !important;
        margin: 5px auto 10px !important;
        width: 90%;
    }

    h2 {
        letter-spacing: 0.3px;
    }

    .who-we-are {
        padding: 0px !important;
    }

    .who-we-are .content-wrapper {
        margin-bottom: 20px !important;
        border-bottom: 1px solid #ddd !important;
        padding: 0px !important;
    }

    .what-we-do .col-md-12 {
        margin-top: 20px !important;
    }

    .who-we-are {
        width: 100% !important;
        margin: 0px !important;
        padding: 0px !important;
    }

    .who-we-are .content-wrapper {
        margin: 10px 0px !important;
    }

    .grow-franchise-banner h1{
        line-height:25px !important;
    }
    .grow-franchise-banner p{
        font-size:20px !important;
        color:black !important;
        padding:0px !important;
        line-height:25px !important;
        font-weight:600 !important;
    }
}


#scroll-to-top {
    visibility: hidden;
    position: fixed;
    bottom: 40px;
    right: 30px;
    z-index: 99;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 10px;
    border-radius: 0px;
    font-size: 18px;
    color: #eee;
    border: 1px solid #444;
    background-color: #333;
    opacity: .7;
    width: 50px;
    height: 50px;
}


#scroll-to-top:hover {
    background-color: #fcfcfc;
    color: #2d5c88;
    border: 1px solid #e1e1e1;
}

.footer-bottom {
    background-color: #333;
    padding: 15px 0px;
}

.copyright {
    color: #eee;
    font-size: 12px;
}

.fcom_sales_index ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.fcom_sales_index li a {
    color: #919191;
    font-size: 14px;
}

.fcom_sales_index li a.active {
    color: #222;
}

.fcom_sales_index li a:hover {
    color: #2d5c88;
    text-decoration: underline;
}

.what-we-do ul {
    padding: 0;
    margin: 0;
    padding-left: 20px;
}

.what-we-do li {
    color: #2d5c88;
    font-weight: 600;
    font-size: 14px;
}

.who-we-are p {
    color: #7e7e7e;
}

.who-we-are p strong {
    color: #2d5c88;
    font-weight: 600;
}

.who-we-are p {
    font-size: 14px;
}

.how-we-help p {
    font-size: 14px;
    color: #7e7e7e;
    line-height: 20px;
}

.how-we-help h3 p {
    color: #222;
}

/* Animation Effect */
.animation-effect a {
    position: relative;
    width: 50%;
}

.animation-effect:hover img {
    opacity: 0.3 !important;
}

.animation-effect:hover .middle {
    opacity: 1 !important;
}

.animation-effect img {
    opacity: 1;
    display: block;
    width: 300px;
    height: auto;
    transition: linear 0.2s;
    backface-visibility: hidden;
}

.animation-effect .middle {
    transition: linear 0.2s;
    opacity: 0;
    position: absolute;
    top: 50%;
    left: 30%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    text-align: center;
}

.animation-effect .middle .text {
    background-color: #2d5c88;
    color: white;
    width: 100px;
    height: 100px;
    padding: 35px;
    vertical-align: middle;
    border-radius: 50%;
    opacity: 0.8;
}

.animation-effect .middle .text i {
    font-size: 26px;
    transform: scale(-1, 1);
}

.animation-effect:hover .middle .text i {
    animation: rotate-btn .3s linear;
}

@keyframes rotate-btn {
    0% {
        transform: rotate(-45deg) scale(-1, 1);
    }

    100% {
        transform: rotate(0deg) scale(-1, 1);
    }
}

@media screen and (min-width:992px) {
    .media-kit img.pdf-image {
        width: 700px !important;
    }
}

.head-title {
    font-size: 14px;
    color: #2d5c88;
    margin: 20px 0px;
}

#footer p {
    color: #eee !important;
}

.pages-title {
    font-size: 14px;
    letter-spacing: 1px;
    text-transform: uppercase;
    font-weight: 600;
}
.normal-tag{
    color: grey;
}
.normal-tag:hover{
    text-decoration: underline;
    color: grey;
    cursor: pointer;
}
.normal-tag-new,.normal-tag-new:hover{
    color: grey;
}


/* LOADING */

#loading,#page_loading{
    position: fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index: 999999;
    display: none;
}
#loading.wait{
    display: block;
}
#loading div,#page_loading div {
    width: 100%;
    height: 100%;
    display:table;
    background: rgba(51,60,78,.75);
}
#loading div i,#page_loading i {
   display:table-cell;
    vertical-align:middle;
    text-align:center;
    font-size: 5rem;
    color:#FFF;
}

#custom_id {
    display: flex;
    padding: 20px; 
    border-radius:0px; 
    flex-direction: row; 
    align-items: center; 
    justify-content: space-between; 
    margin: 0px auto; 
    text-align: left;
}

.img-wrapper {
    text-align:left; 
    width: max-content;
}

#custom-div-center {
    display:flex; 
    flex-direction: column; 
    justify-content: center; 
    align-items: center
}

#custom-div-middle {
    position:absolute; 
    left:23rem
}

#title_wrapper {
    padding: 40px 0px;
}

#custom_id h1 {
    font-size: 16px; 
    font-weight: 400;
}

#custom_id div {
    font-size: 12px;
}

#custom-section {
    background-color: #f8f8f8;
}

#custom-div-center p {
    margin: 20px 0px;
}