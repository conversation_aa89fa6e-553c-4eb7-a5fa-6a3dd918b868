<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,maximum-scale=5.0" />
    <title><?php echo $meta[$section]['title']; ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" media="all"
        href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/files-network/css/mobile.css?random=<?php echo mt_rand(100000, 999999);?>" />
    <link rel="icon" type="image/x-icon"
        href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/files-network/images/cropped-FN-Icon-Color-192x192.png">
    <meta name="robots" content="<?php echo $global_robots; ?>">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@latest/dist/css/splide.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@latest/dist/js/splide.min.js"></script>
    <?php if(isset($meta[$section]['description'])){ ?>
    <meta name="description" content="<?php echo $meta[$section]['description'] ?>">
    <?php } ?>
</head>