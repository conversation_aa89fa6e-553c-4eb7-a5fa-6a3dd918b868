<?php
// Always include global functions
include_once($_SERVER['DOCUMENT_ROOT'].'/includes/global.php');
pageAuthentication($_GET['section'].'s/');
$custom_css_js = ['directories'];

// Page Variables
$page = [
    'browser_title' => ucwords($_GET['section']).'s',
    'title' => ucwords($_GET['section']).'s',
    'subtitle' => 'Directory of Available '.ucwords($_GET['section']).'s',
    'menu' => $_GET['section'].'s',
    'current' => $_GET['section'].'s'
];
$priority = ($session_unit_id == 0)?6:$session_unit_id;
if(isset($_REQUEST['priority']) && strlen($_REQUEST['priority'] ?? '')){
    $priority = $_REQUEST['priority'];
}

// Paramaters for CURL Call
$parameters = array();
$current_page = strlen($_REQUEST['page'] ?? '') ? $_REQUEST['page'] : 1;
$filters = ['fbo_id','gpfv_id','name'];
foreach($filters as $variable){
    if(strlen($_REQUEST[$variable] ?? '')){
        $_REQUEST['filter'] = $variable;
        $_REQUEST['filter_value'] = $_REQUEST[$variable];
    }
}
if(strlen($_REQUEST['status'] ?? '')){
    $parameters['status'] = ($_REQUEST['status'] == 0)?null:$_REQUEST['status'];
}else{
    $parameters['status'] = $_REQUEST['status'] =  3;
}
if(strlen($_REQUEST['filter'] ?? '') && strlen($_REQUEST['filter_value'] ?? '')){
    unset($parameters['status'],$_REQUEST['status']);
    $parameters[$_REQUEST['filter']] = $_REQUEST['filter_value'];
}

// Default Variables
$query = http_build_query($parameters);
$endpoint_url = 'directory/'.$_GET['section'].'?page='.$current_page.'&'.$query;
$data = get_json($endpoint_url);
$name = $_GET['section'].'s';
$thead = '<thead><tr><td class="text-center">&nbsp;</td></tr></thead>';
$tbody = '<tbody><tr><td class="text-center">No Data Found</td></tr></tbody>';

// If Data...
if(!empty($data['data'])){
    $mapped = [
        'concept' => [
            'id' => 'fbo_id',
            'text' => 'FBO ID'            
        ],
        'customer' => [
            'id' => 'gpfv_id',
            'text' => 'Great Plains'            
        ]
    ];
    $first_key = array_key_first($data['data']);
    $last_key = array_key_last($data['data']);
    if(isset($parameters['name']) && $parameters['name']){
        $page['subtitle'] = '<span>Names containing "'.$parameters['name'].'"</span>';
    }else{
        $page['subtitle'] = '<span>"'.$data['data'][$first_key]['name'];
        if($data['data'][$first_key]['name'] != $data['data'][$last_key]['name']){
            $page['subtitle'] .= '" to "'.$data['data'][$last_key]['name'];
        }
        $page['subtitle'] .= '"</span>';
    }
    $units = ($session_unit_id == 60) ? [60=>'FCOM'] : [60=>'FCOM',6=>'FCN',4=>'FG',5=>'FON'];
    $thead = '<tr>';
    $thead .= '<th class="small_sub text-center"><span data-after="'.$mapped[$_GET['section']]['text'].'">ID</span></th>';
    if($_GET['section'] == 'concept'){
        $thead .= '<th class="small_sub"><span data-after="'.$unit_ids[$priority]['name'].'">Concept Name</span></th>';
        $thead .= '<th class="small_sub"><span data-after="'.$unit_ids[$priority]['name'].'">Customer Name</span></th>';
        $thead .= '<th class="small_sub"><span data-after="'.$unit_ids[$priority]['name'].'">Business Type</span></th>';
    }else if($_GET['section'] == 'customer'){
        $thead .= '<th>Customer Name</th>';
    }
    foreach($units as $key => $value){
        $thead .= '<th class="small_sub text-center"><span data-after="'.$value.'">Status</span></th>';
        if($_GET['section'] == 'customer'){
            $thead .= '<th class="small_sub text-center"><span data-after="'.$value.'">Contract(s)</span></th>';
        }
    }
    $thead .= '</tr>';
    $tbody = '';
    foreach($data['data'] as $key => $value){
        if($session_unit_id == 60){
            $temp_contract_statuses = @explode(',',$value[60]['contract_statuses']);
            if(
                (!isset($value[60])) ||
                (isset($_REQUEST['status']) && !in_array($_REQUEST['status'],$temp_contract_statuses) &&  $_REQUEST['status'] != 0)
                ){
                continue;
            }

        }
        if(isset($value[$mapped[$_GET['section']]['id']]) || is_null($value[$mapped[$_GET['section']]['id']] ?? null)){
            $tbody .= '<tr>';
            $tbody .= '<td class="text-center">'.@$value[$mapped[$_GET['section']]['id']].'</td>';
            if($_GET['section'] == 'concept'){
                $temp_concept = $value['name'];
                $temp_customer = $temp_business_type = null;

                if(array_key_exists($priority,$value)){
                    $temp_concept = '<a href="/concepts/'.$value[$priority]['concepts_id'].'/?vaid='.$priority.'#0" target="_blank" class="customer_link">'.$value[$priority]['name'].'<i class="fa fa-link" aria-hidden="true"></i></a>';
                    $temp_business_type = $value[$priority]['business_type'];
                    if($value[$priority]['customers_id']){
                        $temp_customer = '<a href="/customers/'.$value[$priority]['customers_id'].'/?vaid='.$priority.'#0" target="_blank" class="customer_link">'.$value[$priority]['customers_name'].'<i class="fa fa-link" aria-hidden="true"></i></a>';
                    }
                }
                $tbody .= '<td>'.$temp_concept.'</td>';
                $tbody .= '<td>'.$temp_customer.'</td>';
                $tbody .= '<td>'.$temp_business_type.'</td>';
            }else if($_GET['section'] == 'customer'){
                $tbody .= '<td>'.$value['name'].'</td>';
            }
            foreach($units as $unit => $abbr){
                $temp_status = '<span class="fifty"></span>';
                $temp_contracts = $temp_class = $temp_data_text = $temp_onclick = null;
                $temp = null;
                if(array_key_exists($unit,$value)){
                    $temp = $value[$unit];
                }
                if($temp){
                    $temp_class = ' hoverable';
                    $temp_data_text = ' data-text="View this '.$_GET['section'].'"';
                    $temp_onclick = 'onclick="open_click(\''.$_GET['section'].'\',\''.$temp[$_GET['section'].'s_id'].'\',\''.$temp['site_id'].'\');"';
                    $temp_contracts = table_format_number($temp['contracts_count'],true);
                    $temp_status = return_contract_status($temp[$_GET['section'].'_status']);
                }
                $tbody .= '<td class="text-center left_side'.$temp_class.'" '.$temp_onclick.$temp_data_text.'>'.$temp_status.'</td>';
                if($_GET['section'] == 'customer'){
                    $tbody .= '<td class="text-center right_side'.$temp_class.'" '.$temp_onclick.$temp_data_text.'>'.$temp_contracts.'</td>';
                }
            }
            $tbody .= '</tr>';
        }
        
    }
}

// Sample Dropdown
$dropdowns = [
    'filter' => [
        ['id' => 'name','display_name' => 'Name'],
        ['id' => 'fbo_id','display_name' => 'FBO ID']
    ],
    'priorities' => [
        ['id' => '6','display_name' => 'Franchise.com Network'],
        ['id' => '4','display_name' => 'Franchise Gator'],
        ['id' => '5','display_name' => 'Franchise Opportunities']
    ],
    'status' => [
        ['id'=>'0','display_name'=>'All'],
        ['id'=>'3','display_name'=>'Active'],
        ['id'=>'142','display_name'=>'Dayparting'],
        ['id'=>'68','display_name'=>'Paused By Request'],
        ['id'=>'162','display_name'=>'Paused - Accounting'],
        ['id'=>'6','display_name'=>'Paused - Monthly Cap'],
        ['id'=>'7','display_name'=>'Paused - Smoothing'],
        ['id'=>'71','display_name'=>'Pending'],
        ['id'=>'4','display_name'=>'Inactive'],
        ['id'=>'73','display_name'=>'No Contract']
    ]
];
if($session_unit_id == 60){
    $dropdowns['priorities'] = [['id' => '60','display_name' => 'FCOM']];
}
if($_GET['section'] == 'customer'){
    $dropdowns['filter'][1] = ['id' => 'gpfv_id','display_name' => 'Great Plains ID'];
}

// Results Header
function return_header(){
    global $data;
    $return = '0 Results';
    if(!empty($data['data'])){
        $return = number_format($data['total_results']).' Result';
        if($data['total_results'] != 1){
            $return .= 's';
        }
        if($data['current_page'] != $data['total_pages']){
            $return .= '<span>Page '.number_format($data['current_page']).' of '.number_format($data['total_pages']).'</span>';
        }
    }
    return $return;
}

// Return Contract Status
function return_contract_status($value){
    $statuses = [
        3 => 'Active',
        4 => 'Inactive',
        5 =>'Paused',
        6 => 'Paused - Monthly Cap',
        7 => 'Paused - Smoothing',
        68 => 'Paused By Request',
        71 => 'Pending',
        73 => 'No Contract',
        142 => 'Dayparting',
        162 => 'Paused - Accounting'
    ];
    if(!array_key_exists($value,$statuses)){
        $value = 73;
    }
    return '<span class="status_color color'.$value.'">'.strtolower($statuses[$value]).'</span>';
}

// Filter Selects
function directory_filters(){
    global $dropdowns,$priority;
    $return = '';
    if($_GET['section'] == 'concept'){
        $return .= '<div class="toggler">'.create_types_select($dropdowns['priorities'],'priority',$priority,false,'',false,null,false).'</div>';

    }
    $return .= '<div class="toggler">'.create_types_select($dropdowns['status'],'status',@$_REQUEST['status'],false,'',false,null,false).'</div>';
    return '<div class="directory_filters">'.$return.'</div>';
}
?>
<!doctype html>
<html>
    <?php include_once($_SERVER['DOCUMENT_ROOT'].'/includes/head.php'); ?>
    <body>
        <div class="flex">
            <?php include_once($_SERVER['DOCUMENT_ROOT'].'/includes/menu.php'); ?>
            <div id="content">
                <?php include_once($_SERVER['DOCUMENT_ROOT'].'/includes/navbar.php'); ?>
                <div id="scroll">
                    <?php echo create_breadcrumbs(); ?>
                    <header id="header">
                        <?php echo create_title().create_button('add','','Create New '.ucwords($_GET['section']),'load_modal(\'/modals/modal_directories.php?site_id='.$priority.'&section='.$_GET['section'].'\', \'wide\');'); ?>
                    </header>
                    <main id="main">
                        <section>
                            <header class="filtering">
                                <h3><?php echo return_header(); ?></h3>
                                <?php echo directory_filters(); ?>
                            </header>
                            <div class="container">
                                <form id="search" method="get">
                                    <input type="hidden" id="priority" name="priority" value="<?php echo $priority; ?>">
                                    <div class="container">
                                        <div class="col100">
                                            <label class="advanced">Filter by</label>
                                            <div class="searching">
                                                <div class="stretch">
                                                    <?php 
                                                        echo create_types_select($dropdowns['filter'],'filter',@$_REQUEST['filter'],false,'',false,null,false).form_input(null,'text','filter_value','Enter any partial information to filter results...',@$_REQUEST['filter_value']); 
                                                    ?>
                                                    <div class="clear_input" onclick="clear_input('.filter_value');">
                                                        <i class="fa fa-window-close" aria-hidden="true"></i>
                                                    </div>
                                                </div>
                                                <?php echo create_button('search','submit','Search',null); ?>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="container">
                                <div id="table_move" class="table_move">
                                    <table class="checks no_hover no_reflow <?php echo $_GET['section']; ?>">
                                        <thead>
                                            <?php echo $thead; ?>
                                        </thead>
                                        <tbody>
                                            <?php echo $tbody; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="container">
                                <div class="col100">
                                    <?php echo create_pagetoggle(@$data['current_page'],@$data['total_pages'],$name,$name,$query); ?>
                                </div>
                            </div>
                        </section>
                    </main>
                </div>
            </div>
        </div>
        <?php include_once($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?>
    </body>
</html>